{"version": 3, "file": "clone-pseudos.js", "sourceRoot": "", "sources": ["../src/clone-pseudos.ts"], "names": [], "mappings": ";;;AACA,+BAAiD;AAIjD,SAAS,aAAa,CAAC,KAA0B;IAC/C,IAAM,OAAO,GAAG,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;IACjD,OAAO,UAAG,KAAK,CAAC,OAAO,wBAAc,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,OAAI,CAAA;AACtE,CAAC;AAED,SAAS,mBAAmB,CAAC,KAA0B,EAAE,OAAgB;IACvE,OAAO,IAAA,yBAAkB,EAAC,OAAO,CAAC;SAC/B,GAAG,CAAC,UAAC,IAAI;QACR,IAAM,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAM,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;QAEhD,OAAO,UAAG,IAAI,eAAK,KAAK,SAAG,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,MAAG,CAAA;IAC7D,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,CAAC,CAAA;AACd,CAAC;AAED,SAAS,qBAAqB,CAC5B,SAAiB,EACjB,MAAc,EACd,KAA0B,EAC1B,OAAgB;IAEhB,IAAM,QAAQ,GAAG,WAAI,SAAS,cAAI,MAAM,CAAE,CAAA;IAC1C,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO;QAC3B,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC;QACtB,CAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;IAEvC,OAAO,QAAQ,CAAC,cAAc,CAAC,UAAG,QAAQ,cAAI,OAAO,MAAG,CAAC,CAAA;AAC3D,CAAC;AAED,SAAS,kBAAkB,CACzB,UAAa,EACb,UAAa,EACb,MAAc,EACd,OAAgB;IAEhB,IAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;IACzD,IAAM,OAAO,GAAG,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;IACjD,IAAI,OAAO,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;QACxC,OAAM;KACP;IAED,IAAM,SAAS,GAAG,IAAA,WAAI,GAAE,CAAA;IACxB,IAAI;QACF,UAAU,CAAC,SAAS,GAAG,UAAG,UAAU,CAAC,SAAS,cAAI,SAAS,CAAE,CAAA;KAC9D;IAAC,OAAO,GAAG,EAAE;QACZ,OAAM;KACP;IAED,IAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IACpD,YAAY,CAAC,WAAW,CACtB,qBAAqB,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CACzD,CAAA;IACD,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;AACtC,CAAC;AAED,SAAgB,mBAAmB,CACjC,UAAa,EACb,UAAa,EACb,OAAgB;IAEhB,kBAAkB,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;IAC9D,kBAAkB,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;AAC/D,CAAC;AAPD,kDAOC"}