export function bigInt(): Promise<boolean>;
export function bulkMemory(): boolean;
export function exceptions(): boolean;
export function extendedConst(): boolean;
export function gc(): boolean;
export function memory64(): boolean;
export function multiValue(): boolean;
export function mutableGlobals(): boolean;
export function referenceTypes(): boolean;
export function relaxedSimd(): boolean;
export function saturatedFloatToInt(): boolean;
export function signExtensions(): boolean;
export function simd(): boolean;
export function streamingCompilation(): Promise<boolean>;
export function tailCall(): Promise<boolean>;
export function threads(): Promise<boolean>;
export function webgpu(): Promise<boolean>;
export function maxNumThreads(): number;
//# sourceMappingURL=capabilities.d.ts.map