"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/studio/studio.tsx":
/*!******************************************!*\
  !*** ./src/components/studio/studio.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Studio: function() { return /* binding */ Studio; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.ts\");\n/* harmony import */ var _studio_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./studio-header */ \"(app-pages-browser)/./src/components/studio/studio-header.tsx\");\n/* harmony import */ var _studio_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./studio-sidebar */ \"(app-pages-browser)/./src/components/studio/studio-sidebar.tsx\");\n/* harmony import */ var _image_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./image-upload */ \"(app-pages-browser)/./src/components/studio/image-upload.tsx\");\n/* harmony import */ var _text_layer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./text-layer */ \"(app-pages-browser)/./src/components/studio/text-layer.tsx\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Studio auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Studio() {\n    _s();\n    const { image, subjectImage, textLayers, setActiveLayerId, imageEffects, justUploadedImage, setJustUploadedImage } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)();\n    const { data: session, status } = useSession();\n    const router = useRouter();\n    const [redirectTimerSet, setRedirectTimerSet] = useState(false);\n    // Redirect unauthenticated users\n    useEffect(()=>{\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router\n    ]);\n    // Check subscription status\n    useEffect(()=>{\n        var _session_user;\n        // If user just uploaded an image and used their last credit, don't redirect immediately\n        if (justUploadedImage) {\n            console.log(\"Just uploaded image, skipping redirect check\");\n            return;\n        }\n        if ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.subscription) && session.user.subscription.plan_type === \"free\" && session.user.subscription.free_edits_remaining === 0 && image && !redirectTimerSet) {\n            // Set a flag to prevent multiple redirects\n            setRedirectTimerSet(true);\n            // Only redirect when trying to upload a new image, not immediately after \n            // using the last credit\n            console.log(\"Zero credits detected - will redirect on next upload\");\n        }\n    }, [\n        session,\n        image,\n        router,\n        justUploadedImage,\n        redirectTimerSet\n    ]);\n    // Clear the \"just uploaded\" flag after a short delay\n    useEffect(()=>{\n        if (justUploadedImage) {\n            const timer = setTimeout(()=>{\n                console.log(\"Clearing justUploadedImage flag\");\n                setJustUploadedImage(false);\n            }, 3000); // Give user 3 seconds to edit before removing the flag\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        justUploadedImage,\n        setJustUploadedImage\n    ]);\n    // Loading state\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 flex items-center justify-center bg-black text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin h-10 w-10 text-white mx-auto\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading your studio...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this);\n    }\n    if (!image) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_6__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_image_upload__WEBPACK_IMPORTED_MODULE_4__.ImageUpload, {}, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Handle click on canvas (outside of text) to deselect\n    const handleCanvasClick = ()=>{\n        setActiveLayerId(null);\n    };\n    // Filter text layers into two groups: behind subject and over subject\n    const behindSubjectLayers = textLayers.filter((layer)=>layer.isBehindSubject);\n    const overSubjectLayers = textLayers.filter((layer)=>!layer.isBehindSubject);\n    // Get original index for each layer to use for z-index calculation\n    const getLayerIndex = (layerId)=>{\n        return textLayers.findIndex((layer)=>layer.id === layerId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 flex flex-col h-screen w-full bg-black text-white overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_6__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_studio_header__WEBPACK_IMPORTED_MODULE_2__.StudioHeader, {}, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-[55vh] md:h-auto md:w-3/5 bg-black/10 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            \"data-canvas\": \"true\",\n                            className: \"relative p-1 md:p-4 min-w-[320px] min-h-[320px] md:min-w-[640px] md:min-h-[480px] overflow-hidden flex items-center justify-center\",\n                            onClick: handleCanvasClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mx-auto\",\n                                style: {\n                                    display: \"inline-block\",\n                                    maxWidth: \"100%\",\n                                    maxHeight: \"100%\"\n                                },\n                                children: [\n                                    imageEffects.applyToBackground ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Background\",\n                                        className: \"max-w-full w-full h-auto object-contain md:max-h-[calc(100vh-10rem)]\",\n                                        style: {\n                                            filter: \"blur(\".concat(imageEffects.blur, \"px) brightness(\").concat(100 - imageEffects.darken, \"%)\"),\n                                            maxWidth: \"100%\",\n                                            maxHeight: \"calc(60vh - 2rem)\",\n                                            minWidth: \"320px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Background\",\n                                        className: \"max-w-full w-full h-auto object-contain md:max-h-[calc(100vh-10rem)]\",\n                                        style: {\n                                            maxWidth: \"100%\",\n                                            maxHeight: \"calc(60vh - 2rem)\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this),\n                                    imageEffects.vignette > 0 && imageEffects.applyToBackground && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 pointer-events-none\",\n                                        style: {\n                                            background: \"radial-gradient(circle at center, transparent 0%, rgba(0,0,0,\".concat(imageEffects.vignette / 100, \") 100%)\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this),\n                                    subjectImage && behindSubjectLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_text_layer__WEBPACK_IMPORTED_MODULE_5__.TextLayer, {\n                                            layer: layer,\n                                            zIndex: 10 + (textLayers.length - getLayerIndex(layer.id))\n                                        }, \"behind-\".concat(layer.id), false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)),\n                                    subjectImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: subjectImage,\n                                        alt: \"Subject\",\n                                        className: \"absolute top-0 left-0 w-full h-full pointer-events-none object-contain\",\n                                        style: {\n                                            filter: imageEffects.applyToSubject ? \"blur(\".concat(imageEffects.blur, \"px) brightness(\").concat(100 - imageEffects.darken, \"%)\") : \"none\",\n                                            zIndex: 100,\n                                            maxWidth: \"100%\",\n                                            maxHeight: \"calc(55vh - 2rem)\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this),\n                                    imageEffects.vignette > 0 && imageEffects.applyToSubject && subjectImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 pointer-events-none\",\n                                        style: {\n                                            background: \"radial-gradient(circle at center, transparent 0%, rgba(0,0,0,\".concat(imageEffects.vignette / 100, \") 100%)\"),\n                                            mixBlendMode: \"multiply\",\n                                            pointerEvents: \"none\",\n                                            zIndex: 101\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this),\n                                    overSubjectLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_text_layer__WEBPACK_IMPORTED_MODULE_5__.TextLayer, {\n                                            layer: layer,\n                                            zIndex: 200 + (textLayers.length - getLayerIndex(layer.id))\n                                        }, layer.id, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 h-full overflow-hidden bg-black\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_studio_sidebar__WEBPACK_IMPORTED_MODULE_3__.StudioSidebar, {}, void 0, false, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n_s(Studio, \"OdwyE1Thxb4sv/dztZ1NAXrkabk=\", true, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore\n    ];\n});\n_c = Studio;\nvar _c;\n$RefreshReg$(_c, \"Studio\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/studio/studio.tsx\n"));

/***/ })

});