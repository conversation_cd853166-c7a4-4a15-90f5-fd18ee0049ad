"use strict";var S=Object.defineProperty;var F=Object.getOwnPropertyDescriptor;var D=Object.getOwnPropertyNames;var j=Object.prototype.hasOwnProperty;var O=(e,t)=>{for(var n in t)S(e,n,{get:t[n],enumerable:!0})},q=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of D(t))!j.call(e,o)&&o!==n&&S(e,o,{get:()=>t[o],enumerable:!(r=F(t,o))||r.enumerable});return e};var M=e=>q(S({},"__esModule",{value:!0}),e);var ke={};O(ke,{QueryCompiler:()=>C,__wbg_String_8f0eb39a4a4c2f66:()=>N,__wbg_buffer_609cc3eee51ed158:()=>$,__wbg_call_672a4d21634d4a24:()=>V,__wbg_call_7cccdd69e0791ae2:()=>W,__wbg_crypto_805be4ce92f1e370:()=>B,__wbg_entries_3265d4158b33e5dc:()=>z,__wbg_getRandomValues_f6a868620c8bab49:()=>G,__wbg_getTime_46267b1c24877e30:()=>P,__wbg_get_b9b93047fe3cf45b:()=>Q,__wbg_getwithrefkey_1dc361bd10053bfe:()=>H,__wbg_instanceof_ArrayBuffer_e14585432e3737fc:()=>J,__wbg_instanceof_Uint8Array_17156bcf118086a9:()=>Y,__wbg_isSafeInteger_343e2beeeece1bb0:()=>K,__wbg_keys_5c77a08ddc2fb8a6:()=>X,__wbg_length_a446193dc22c12f8:()=>Z,__wbg_length_e2d2a49132c1b256:()=>v,__wbg_msCrypto_2ac4d17c4748234a:()=>ee,__wbg_new0_f788a2397c7ca929:()=>te,__wbg_new_a12002a7f91c75be:()=>ne,__wbg_newnoargs_105ed471475aaf50:()=>re,__wbg_newwithbyteoffsetandlength_d97e637ebe145a9a:()=>oe,__wbg_newwithlength_a381634e90c276d4:()=>_e,__wbg_node_ecc8306b9857f33d:()=>ce,__wbg_now_807e54c39636c349:()=>ie,__wbg_now_b3f7572f6ef3d3a9:()=>ue,__wbg_process_5cff2739921be718:()=>se,__wbg_randomFillSync_d3c85af7e31cf1f8:()=>fe,__wbg_require_0c566c6f2eef6c79:()=>be,__wbg_set_65595bdd868b3009:()=>ae,__wbg_set_wasm:()=>L,__wbg_setmessage_82ae475bb413aa5c:()=>de,__wbg_static_accessor_GLOBAL_88a902d13a557d07:()=>le,__wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0:()=>ge,__wbg_static_accessor_SELF_37c5d418e4bf5819:()=>we,__wbg_static_accessor_WINDOW_5de37043a91a9c40:()=>pe,__wbg_subarray_aa9065fa9dc5df96:()=>xe,__wbg_versions_a8e5a362e1f16442:()=>ye,__wbindgen_as_number:()=>me,__wbindgen_boolean_get:()=>he,__wbindgen_debug_string:()=>Se,__wbindgen_error_new:()=>Te,__wbindgen_in:()=>Ae,__wbindgen_init_externref_table:()=>Ie,__wbindgen_is_function:()=>Ee,__wbindgen_is_object:()=>Fe,__wbindgen_is_string:()=>De,__wbindgen_is_undefined:()=>je,__wbindgen_jsval_loose_eq:()=>Oe,__wbindgen_memory:()=>qe,__wbindgen_number_get:()=>Me,__wbindgen_string_get:()=>Le,__wbindgen_string_new:()=>Re,__wbindgen_throw:()=>Ue});module.exports=M(ke);var p=()=>{};p.prototype=p;let _;function L(e){_=e}let a=0,x=null;function y(){return(x===null||x.byteLength===0)&&(x=new Uint8Array(_.memory.buffer)),x}const R=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let m=new R("utf-8");const U=typeof m.encodeInto=="function"?function(e,t){return m.encodeInto(e,t)}:function(e,t){const n=m.encode(e);return t.set(n),{read:e.length,written:n.length}};function h(e,t,n){if(n===void 0){const u=m.encode(e),s=t(u.length,1)>>>0;return y().subarray(s,s+u.length).set(u),a=u.length,s}let r=e.length,o=t(r,1)>>>0;const i=y();let c=0;for(;c<r;c++){const u=e.charCodeAt(c);if(u>127)break;i[o+c]=u}if(c!==r){c!==0&&(e=e.slice(c)),o=n(o,r,r=c+e.length*3,1)>>>0;const u=y().subarray(o+c,o+r),s=U(e,u);c+=s.written,o=n(o,r,c,1)>>>0}return a=c,o}let d=null;function f(){return(d===null||d.buffer.detached===!0||d.buffer.detached===void 0&&d.buffer!==_.memory.buffer)&&(d=new DataView(_.memory.buffer)),d}function w(e){const t=_.__externref_table_alloc();return _.__wbindgen_export_4.set(t,e),t}function l(e,t){try{return e.apply(this,t)}catch(n){const r=w(n);_.__wbindgen_exn_store(r)}}const k=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let A=new k("utf-8",{ignoreBOM:!0,fatal:!0});A.decode();function g(e,t){return e=e>>>0,A.decode(y().subarray(e,e+t))}function b(e){return e==null}function T(e){const t=typeof e;if(t=="number"||t=="boolean"||e==null)return`${e}`;if(t=="string")return`"${e}"`;if(t=="symbol"){const o=e.description;return o==null?"Symbol":`Symbol(${o})`}if(t=="function"){const o=e.name;return typeof o=="string"&&o.length>0?`Function(${o})`:"Function"}if(Array.isArray(e)){const o=e.length;let i="[";o>0&&(i+=T(e[0]));for(let c=1;c<o;c++)i+=", "+T(e[c]);return i+="]",i}const n=/\[object ([^\]]+)\]/.exec(toString.call(e));let r;if(n&&n.length>1)r=n[1];else return toString.call(e);if(r=="Object")try{return"Object("+JSON.stringify(e)+")"}catch{return"Object"}return e instanceof Error?`${e.name}: ${e.message}
${e.stack}`:r}function I(e){const t=_.__wbindgen_export_4.get(e);return _.__externref_table_dealloc(e),t}const E=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(e=>_.__wbg_querycompiler_free(e>>>0,1));class C{__destroy_into_raw(){const t=this.__wbg_ptr;return this.__wbg_ptr=0,E.unregister(this),t}free(){const t=this.__destroy_into_raw();_.__wbg_querycompiler_free(t,0)}constructor(t){const n=_.querycompiler_new(t);if(n[2])throw I(n[1]);return this.__wbg_ptr=n[0]>>>0,E.register(this,this.__wbg_ptr,this),this}compile(t){let n,r;try{const c=h(t,_.__wbindgen_malloc,_.__wbindgen_realloc),u=a,s=_.querycompiler_compile(this.__wbg_ptr,c,u);var o=s[0],i=s[1];if(s[3])throw o=0,i=0,I(s[2]);return n=o,r=i,g(o,i)}finally{_.__wbindgen_free(n,r,1)}}}function N(e,t){const n=String(t),r=h(n,_.__wbindgen_malloc,_.__wbindgen_realloc),o=a;f().setInt32(e+4*1,o,!0),f().setInt32(e+4*0,r,!0)}function $(e){return e.buffer}function V(){return l(function(e,t){return e.call(t)},arguments)}function W(){return l(function(e,t,n){return e.call(t,n)},arguments)}function B(e){return e.crypto}function z(e){return Object.entries(e)}function G(){return l(function(e,t){e.getRandomValues(t)},arguments)}function P(e){return e.getTime()}function Q(e,t){return e[t>>>0]}function H(e,t){return e[t]}function J(e){let t;try{t=e instanceof ArrayBuffer}catch{t=!1}return t}function Y(e){let t;try{t=e instanceof Uint8Array}catch{t=!1}return t}function K(e){return Number.isSafeInteger(e)}function X(e){return Object.keys(e)}function Z(e){return e.length}function v(e){return e.length}function ee(e){return e.msCrypto}function te(){return new Date}function ne(e){return new Uint8Array(e)}function re(e,t){return new p(g(e,t))}function oe(e,t,n){return new Uint8Array(e,t>>>0,n>>>0)}function _e(e){return new Uint8Array(e>>>0)}function ce(e){return e.node}function ie(){return Date.now()}function ue(){return l(function(){return Date.now()},arguments)}function se(e){return e.process}function fe(){return l(function(e,t){e.randomFillSync(t)},arguments)}function be(){return l(function(){return module.require},arguments)}function ae(e,t,n){e.set(t,n>>>0)}function de(e,t){global.PRISMA_WASM_PANIC_REGISTRY.set_message(g(e,t))}function le(){const e=typeof global>"u"?null:global;return b(e)?0:w(e)}function ge(){const e=typeof globalThis>"u"?null:globalThis;return b(e)?0:w(e)}function we(){const e=typeof self>"u"?null:self;return b(e)?0:w(e)}function pe(){const e=typeof window>"u"?null:window;return b(e)?0:w(e)}function xe(e,t,n){return e.subarray(t>>>0,n>>>0)}function ye(e){return e.versions}function me(e){return+e}function he(e){const t=e;return typeof t=="boolean"?t?1:0:2}function Se(e,t){const n=T(t),r=h(n,_.__wbindgen_malloc,_.__wbindgen_realloc),o=a;f().setInt32(e+4*1,o,!0),f().setInt32(e+4*0,r,!0)}function Te(e,t){return new Error(g(e,t))}function Ae(e,t){return e in t}function Ie(){const e=_.__wbindgen_export_4,t=e.grow(4);e.set(0,void 0),e.set(t+0,void 0),e.set(t+1,null),e.set(t+2,!0),e.set(t+3,!1)}function Ee(e){return typeof e=="function"}function Fe(e){const t=e;return typeof t=="object"&&t!==null}function De(e){return typeof e=="string"}function je(e){return e===void 0}function Oe(e,t){return e==t}function qe(){return _.memory}function Me(e,t){const n=t,r=typeof n=="number"?n:void 0;f().setFloat64(e+8*1,b(r)?0:r,!0),f().setInt32(e+4*0,!b(r),!0)}function Le(e,t){const n=t,r=typeof n=="string"?n:void 0;var o=b(r)?0:h(r,_.__wbindgen_malloc,_.__wbindgen_realloc),i=a;f().setInt32(e+4*1,i,!0),f().setInt32(e+4*0,o,!0)}function Re(e,t){return g(e,t)}function Ue(e,t){throw new Error(g(e,t))}0&&(module.exports={QueryCompiler,__wbg_String_8f0eb39a4a4c2f66,__wbg_buffer_609cc3eee51ed158,__wbg_call_672a4d21634d4a24,__wbg_call_7cccdd69e0791ae2,__wbg_crypto_805be4ce92f1e370,__wbg_entries_3265d4158b33e5dc,__wbg_getRandomValues_f6a868620c8bab49,__wbg_getTime_46267b1c24877e30,__wbg_get_b9b93047fe3cf45b,__wbg_getwithrefkey_1dc361bd10053bfe,__wbg_instanceof_ArrayBuffer_e14585432e3737fc,__wbg_instanceof_Uint8Array_17156bcf118086a9,__wbg_isSafeInteger_343e2beeeece1bb0,__wbg_keys_5c77a08ddc2fb8a6,__wbg_length_a446193dc22c12f8,__wbg_length_e2d2a49132c1b256,__wbg_msCrypto_2ac4d17c4748234a,__wbg_new0_f788a2397c7ca929,__wbg_new_a12002a7f91c75be,__wbg_newnoargs_105ed471475aaf50,__wbg_newwithbyteoffsetandlength_d97e637ebe145a9a,__wbg_newwithlength_a381634e90c276d4,__wbg_node_ecc8306b9857f33d,__wbg_now_807e54c39636c349,__wbg_now_b3f7572f6ef3d3a9,__wbg_process_5cff2739921be718,__wbg_randomFillSync_d3c85af7e31cf1f8,__wbg_require_0c566c6f2eef6c79,__wbg_set_65595bdd868b3009,__wbg_set_wasm,__wbg_setmessage_82ae475bb413aa5c,__wbg_static_accessor_GLOBAL_88a902d13a557d07,__wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0,__wbg_static_accessor_SELF_37c5d418e4bf5819,__wbg_static_accessor_WINDOW_5de37043a91a9c40,__wbg_subarray_aa9065fa9dc5df96,__wbg_versions_a8e5a362e1f16442,__wbindgen_as_number,__wbindgen_boolean_get,__wbindgen_debug_string,__wbindgen_error_new,__wbindgen_in,__wbindgen_init_externref_table,__wbindgen_is_function,__wbindgen_is_object,__wbindgen_is_string,__wbindgen_is_undefined,__wbindgen_jsval_loose_eq,__wbindgen_memory,__wbindgen_number_get,__wbindgen_string_get,__wbindgen_string_new,__wbindgen_throw});
