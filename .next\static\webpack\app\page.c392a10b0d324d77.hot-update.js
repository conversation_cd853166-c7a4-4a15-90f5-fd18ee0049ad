"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: function() { return /* binding */ AppSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=File,Info,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=File,Info,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=File,Info,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=File,Info,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AppSidebar(param) {\n    let { className } = param;\n    var _session, _session_user, _session1, _session_user1, _session2, _session3;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { setActiveLayerId } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    // Close sidebar when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (e)=>{\n            const target = e.target;\n            // Check if the click is outside the sidebar and not on the menu button\n            if (open && !target.closest(\"[data-sidebar]\") && !target.closest(\"[data-sidebar-trigger]\")) {\n                setOpen(false);\n            }\n        };\n        // When canvas is clicked, deselect active text layer and close sidebar\n        const handleCanvasClick = ()=>{\n            setOpen(false);\n        };\n        document.addEventListener(\"click\", handleClickOutside);\n        // Find and add event listener to the canvas element\n        const canvasElement = document.querySelector(\"[data-canvas]\");\n        if (canvasElement) {\n            canvasElement.addEventListener(\"click\", handleCanvasClick);\n        }\n        return ()=>{\n            document.removeEventListener(\"click\", handleClickOutside);\n            if (canvasElement) {\n                canvasElement.removeEventListener(\"click\", handleCanvasClick);\n            }\n        };\n    }, [\n        open,\n        setActiveLayerId\n    ]);\n    const handleSignOut = async ()=>{\n        await signOut({\n            redirect: false\n        });\n        router.push(\"/\");\n    };\n    // Get subscription status text based on plan type\n    const getSubscriptionStatus = ()=>{\n        var _session_user, _session, _session_user1, _session1, _session_user2, _session2;\n        // Check for admin or influencer status first\n        if ((_session = session) === null || _session === void 0 ? void 0 : (_session_user = _session.user) === null || _session_user === void 0 ? void 0 : _session_user.is_admin) {\n            return \"Admin - Unlimited Edits\";\n        }\n        if ((_session1 = session) === null || _session1 === void 0 ? void 0 : (_session_user1 = _session1.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.is_influencer) {\n            return \"Influencer - Unlimited Edits\";\n        }\n        if (!((_session2 = session) === null || _session2 === void 0 ? void 0 : (_session_user2 = _session2.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.subscription)) return \"No subscription\";\n        const { subscription } = session.user;\n        switch(subscription.plan_type){\n            case \"pro\":\n                return \"Pro Plan - Unlimited Edits\";\n            case \"lite\":\n                return \"Lite Plan - \".concat(subscription.lite_edits_remaining, \"/\").concat(subscription.lite_edits_monthly_limit, \" edits\");\n            case \"free\":\n                return \"Free Plan - \".concat(subscription.free_edits_remaining, \"/5 edits\");\n            default:\n                return \"Unknown plan\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"icon\",\n                onClick: ()=>setOpen(!open),\n                className: \"absolute top-1 left-2 z-50 h-8 w-8 text-white hover:bg-white/10\",\n                \"data-sidebar-trigger\": \"true\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: \"Toggle Menu\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black/50\",\n                onClick: ()=>setOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-sidebar\": \"true\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed top-0 left-0 z-50 h-full w-64 bg-black border-r border-white/10 p-4 shadow-xl transition-transform duration-200 ease-in-out\", open ? \"translate-x-0\" : \"-translate-x-full\", className),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: \"Image-Text Studio\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setOpen(false),\n                                className: \"h-8 w-8 text-white hover:bg-white/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    ((_session = session) === null || _session === void 0 ? void 0 : _session.user) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 py-4 border-b border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-10 w-10 rounded-full bg-white/10 flex items-center justify-center overflow-hidden\",\n                                        children: session.user.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: session.user.image,\n                                            alt: session.user.name || \"User\",\n                                            className: \"h-full w-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                            className: \"h-5 w-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-white font-medium\",\n                                                children: session.user.email\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-white/60\",\n                                                children: getSubscriptionStatus()\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleSignOut,\n                                className: \"w-full border-white/20 text-white hover:bg-white/10 hover:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogOut, {\n                                        className: \"mr-2 h-3.5 w-3.5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sign Out\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 py-4 border-b border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/auth/signin\"),\n                            className: \"w-full border-white/20 text-white hover:bg-white/10 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                    className: \"mr-2 h-3.5 w-3.5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this),\n                                \"Sign In\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-1\",\n                        children: [\n                            ((_session1 = session) === null || _session1 === void 0 ? void 0 : (_session_user = _session1.user) === null || _session_user === void 0 ? void 0 : _session_user.is_admin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/admin\",\n                                className: \"block w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-start text-white hover:bg-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LayoutDashboard, {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Admin Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this),\n                            ((_session2 = session) === null || _session2 === void 0 ? void 0 : (_session_user1 = _session2.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.is_influencer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/dashboard/influencer\",\n                                className: \"block w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-start text-white hover:bg-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Star, {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Influencer Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this),\n                            ((_session3 = session) === null || _session3 === void 0 ? void 0 : _session3.user) && !session.user.is_admin && !session.user.is_influencer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"block w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-start text-white hover:bg-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Your Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/pricing\",\n                                className: \"block w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-start text-white hover:bg-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Plans & Pricing\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/about\",\n                                className: \"block w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-start text-white hover:bg-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"About Us\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 mt-4 border-t border-white/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-white/60 mb-2 px-2\",\n                                        children: \"Legal & Policies\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/terms\",\n                                        className: \"block w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-white hover:bg-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Terms of Use\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/privacy-policy\",\n                                        className: \"block w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-white hover:bg-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Privacy Policy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/disclaimer\",\n                                        className: \"block w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-white hover:bg-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Disclaimer\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/shipping-delivery\",\n                                        className: \"block w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-white hover:bg-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Shipping & Delivery\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/refund-policy\",\n                                        className: \"block w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-white hover:bg-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Refund & Cancellation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-4 left-0 right-0 px-4 text-white/60 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Image-Text Studio v1.0\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2025 Image-Text Studio. Crafted with ❤️ by Bhanu\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AppSidebar, \"xWmlrQ0gIcmEhZuHBVpQnD/HRBc=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});