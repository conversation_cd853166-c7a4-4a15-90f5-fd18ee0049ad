export var ValueType;
(function (ValueType) {
    ValueType[ValueType["NULL"] = 0] = "NULL";
    ValueType[ValueType["INT"] = 1] = "INT";
    ValueType[ValueType["UINT"] = 2] = "UINT";
    ValueType[ValueType["FLOAT"] = 3] = "FLOAT";
    ValueType[ValueType["KEY"] = 4] = "KEY";
    ValueType[ValueType["STRING"] = 5] = "STRING";
    ValueType[ValueType["INDIRECT_INT"] = 6] = "INDIRECT_INT";
    ValueType[ValueType["INDIRECT_UINT"] = 7] = "INDIRECT_UINT";
    ValueType[ValueType["INDIRECT_FLOAT"] = 8] = "INDIRECT_FLOAT";
    ValueType[ValueType["MAP"] = 9] = "MAP";
    ValueType[ValueType["VECTOR"] = 10] = "VECTOR";
    ValueType[ValueType["VECTOR_INT"] = 11] = "VECTOR_INT";
    ValueType[ValueType["VECTOR_UINT"] = 12] = "VECTOR_UINT";
    ValueType[ValueType["VECTOR_FLOAT"] = 13] = "VECTOR_FLOAT";
    ValueType[ValueType["VECTOR_KEY"] = 14] = "VECTOR_KEY";
    ValueType[ValueType["VECTOR_STRING_DEPRECATED"] = 15] = "VECTOR_STRING_DEPRECATED";
    ValueType[ValueType["VECTOR_INT2"] = 16] = "VECTOR_INT2";
    ValueType[ValueType["VECTOR_UINT2"] = 17] = "VECTOR_UINT2";
    ValueType[ValueType["VECTOR_FLOAT2"] = 18] = "VECTOR_FLOAT2";
    ValueType[ValueType["VECTOR_INT3"] = 19] = "VECTOR_INT3";
    ValueType[ValueType["VECTOR_UINT3"] = 20] = "VECTOR_UINT3";
    ValueType[ValueType["VECTOR_FLOAT3"] = 21] = "VECTOR_FLOAT3";
    ValueType[ValueType["VECTOR_INT4"] = 22] = "VECTOR_INT4";
    ValueType[ValueType["VECTOR_UINT4"] = 23] = "VECTOR_UINT4";
    ValueType[ValueType["VECTOR_FLOAT4"] = 24] = "VECTOR_FLOAT4";
    ValueType[ValueType["BLOB"] = 25] = "BLOB";
    ValueType[ValueType["BOOL"] = 26] = "BOOL";
    ValueType[ValueType["VECTOR_BOOL"] = 36] = "VECTOR_BOOL";
})(ValueType || (ValueType = {}));
