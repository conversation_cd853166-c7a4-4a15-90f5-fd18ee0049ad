"use client"

import { useStore } from "@/store/useStore"
import { StudioHeader } from "./studio-header"
import { StudioSidebar } from "./studio-sidebar"
import { ImageUpload } from "./image-upload"
import { TextLayer } from "./text-layer"
import { AppSidebar } from "@/components/app-sidebar"

export function Studio() {
  const { image, subjectImage, textLayers, setActiveLayerId, imageEffects } = useStore()

  if (!image) {
    return (
      <>
        <AppSidebar />
        <ImageUpload />
      </>
    )
  }

  // Handle click on canvas (outside of text) to deselect
  const handleCanvasClick = () => {
    setActiveLayerId(null)
  }

  // Filter text layers into two groups: behind subject and over subject
  const behindSubjectLayers = textLayers.filter(layer => layer.isBehindSubject)
  const overSubjectLayers = textLayers.filter(layer => !layer.isBehindSubject)

  // Get original index for each layer to use for z-index calculation
  const getLayerIndex = (layerId: string) => {
    return textLayers.findIndex(layer => layer.id === layerId);
  }

  return (
    <div className="fixed inset-0 flex flex-col h-screen w-full bg-black text-white overflow-hidden">
      <AppSidebar />
      
      {/* Fixed header */}
      <div className="flex-shrink-0">
        <StudioHeader />
      </div>
      
      {/* Main content area */}
      <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
        {/* Canvas area - fixed position */}
        <div className="flex items-center justify-center h-[55vh] md:h-auto md:w-3/5 bg-black/10 overflow-hidden">
          <div 
            data-canvas="true"
            className="relative p-1 md:p-4 min-w-[320px] min-h-[320px] md:min-w-[640px] md:min-h-[480px] overflow-hidden flex items-center justify-center"
            onClick={handleCanvasClick}
          >
            <div className="relative mx-auto" style={{ display: "inline-block", maxWidth: "100%", maxHeight: "100%" }}>
              {/* Step 1: Base Image (always at the bottom) */}
              {imageEffects.applyToBackground ? (
                <img
                  src={image}
                  alt="Background"
                  className="max-w-full w-full h-auto object-contain md:max-h-[calc(100vh-10rem)]"
                  style={{
                    filter: `blur(${imageEffects.blur}px) brightness(${100 - imageEffects.darken}%)`,
                    maxWidth: "100%",
                    maxHeight: "calc(60vh - 2rem)",
                    minWidth: "320px"
                  }}
                />
              ) : (
                <img
                  src={image}
                  alt="Background"
                  className="max-w-full w-full h-auto object-contain md:max-h-[calc(100vh-10rem)]"
                  style={{
                    maxWidth: "100%",
                    maxHeight: "calc(60vh - 2rem)"
                  }}
                />
              )}
              
              {/* Step 2: Vignette Effect (only if applied to background) */}
              {imageEffects.vignette > 0 && imageEffects.applyToBackground && (
                <div
                  className="absolute inset-0 pointer-events-none"
                  style={{
                    background: `radial-gradient(circle at center, transparent 0%, rgba(0,0,0,${imageEffects.vignette / 100}) 100%)`,
                  }}
                />
              )}
              
              {/* Step 3: Text Layers that should be behind the subject - render in array order (first = bottom) */}
              {subjectImage && behindSubjectLayers.map((layer) => (
                <TextLayer 
                  key={`behind-${layer.id}`} 
                  layer={layer} 
                  zIndex={10 + (textLayers.length - getLayerIndex(layer.id))} // Higher z-index = higher in stack
                />
              ))}
              
              {/* Step 4: Subject Image (if available) */}
              {subjectImage && (
                <img
                  src={subjectImage}
                  alt="Subject"
                  className="absolute top-0 left-0 w-full h-full pointer-events-none object-contain"
                  style={{ 
                    filter: imageEffects.applyToSubject ? `blur(${imageEffects.blur}px) brightness(${100 - imageEffects.darken}%)` : 'none',
                    zIndex: 100,
                    maxWidth: "100%",
                    maxHeight: "calc(55vh - 2rem)"
                  }}
                />
              )}
              
              {/* Step 5: Vignette Effect for Subject (if applied to subject) */}
              {imageEffects.vignette > 0 && imageEffects.applyToSubject && subjectImage && (
                <div
                  className="absolute inset-0 pointer-events-none"
                  style={{
                    background: `radial-gradient(circle at center, transparent 0%, rgba(0,0,0,${imageEffects.vignette / 100}) 100%)`,
                    mixBlendMode: "multiply",
                    pointerEvents: "none",
                    zIndex: 101, // Just above the subject image
                  }}
                />
              )}
              
              {/* Step 6: Text Layers that should appear over the subject - render in array order (first = bottom) */}
              {overSubjectLayers.map((layer) => (
                <TextLayer 
                  key={layer.id} 
                  layer={layer} 
                  zIndex={200 + (textLayers.length - getLayerIndex(layer.id))} // Higher z-index = higher in stack
                />
              ))}
            </div>
          </div>
        </div>
        
        {/* Toolbar - ensure no overflow */}
        <div className="flex-1 h-full overflow-hidden bg-black">
          <StudioSidebar />
        </div>
      </div>
    </div>
  )
} 