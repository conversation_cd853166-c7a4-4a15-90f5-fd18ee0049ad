"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zustand";
exports.ids = ["vendor-chunks/zustand"];
exports.modules = {

/***/ "(ssr)/./node_modules/zustand/esm/middleware.mjs":
/*!*************************************************!*\
  !*** ./node_modules/zustand/esm/middleware.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   persist: () => (/* binding */ persist),\n/* harmony export */   redux: () => (/* binding */ redux),\n/* harmony export */   subscribeWithSelector: () => (/* binding */ subscribeWithSelector)\n/* harmony export */ });\nconst reduxImpl = (reducer, initial)=>(set, _get, api)=>{\n        api.dispatch = (action)=>{\n            set((state)=>reducer(state, action), false, action);\n            return action;\n        };\n        api.dispatchFromDevtools = true;\n        return {\n            dispatch: (...a)=>api.dispatch(...a),\n            ...initial\n        };\n    };\nconst redux = reduxImpl;\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name)=>{\n    const api = trackedConnections.get(name);\n    if (!api) return {};\n    return Object.fromEntries(Object.entries(api.stores).map(([key, api2])=>[\n            key,\n            api2.getState()\n        ]));\n};\nconst extractConnectionInformation = (store, extensionConnector, options)=>{\n    if (store === undefined) {\n        return {\n            type: \"untracked\",\n            connection: extensionConnector.connect(options)\n        };\n    }\n    const existingConnection = trackedConnections.get(options.name);\n    if (existingConnection) {\n        return {\n            type: \"tracked\",\n            store,\n            ...existingConnection\n        };\n    }\n    const newConnection = {\n        connection: extensionConnector.connect(options),\n        stores: {}\n    };\n    trackedConnections.set(options.name, newConnection);\n    return {\n        type: \"tracked\",\n        store,\n        ...newConnection\n    };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {})=>(set, get, api)=>{\n        const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n        let extensionConnector;\n        try {\n            extensionConnector = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n        } catch (e) {}\n        if (!extensionConnector) {\n            return fn(set, get, api);\n        }\n        const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n        let isRecording = true;\n        api.setState = (state, replace, nameOrAction)=>{\n            const r = set(state, replace);\n            if (!isRecording) return r;\n            const action = nameOrAction === undefined ? {\n                type: anonymousActionType || \"anonymous\"\n            } : typeof nameOrAction === \"string\" ? {\n                type: nameOrAction\n            } : nameOrAction;\n            if (store === undefined) {\n                connection == null ? undefined : connection.send(action, get());\n                return r;\n            }\n            connection == null ? undefined : connection.send({\n                ...action,\n                type: `${store}/${action.type}`\n            }, {\n                ...getTrackedConnectionState(options.name),\n                [store]: api.getState()\n            });\n            return r;\n        };\n        const setStateFromDevtools = (...a)=>{\n            const originalIsRecording = isRecording;\n            isRecording = false;\n            set(...a);\n            isRecording = originalIsRecording;\n        };\n        const initialState = fn(api.setState, get, api);\n        if (connectionInformation.type === \"untracked\") {\n            connection == null ? undefined : connection.init(initialState);\n        } else {\n            connectionInformation.stores[connectionInformation.store] = api;\n            connection == null ? undefined : connection.init(Object.fromEntries(Object.entries(connectionInformation.stores).map(([key, store2])=>[\n                    key,\n                    key === connectionInformation.store ? initialState : store2.getState()\n                ])));\n        }\n        if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n            let didWarnAboutReservedActionType = false;\n            const originalDispatch = api.dispatch;\n            api.dispatch = (...a)=>{\n                if ( true && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n                    console.warn('[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.');\n                    didWarnAboutReservedActionType = true;\n                }\n                originalDispatch(...a);\n            };\n        }\n        connection.subscribe((message)=>{\n            var _a;\n            switch(message.type){\n                case \"ACTION\":\n                    if (typeof message.payload !== \"string\") {\n                        console.error(\"[zustand devtools middleware] Unsupported action format\");\n                        return;\n                    }\n                    return parseJsonThen(message.payload, (action)=>{\n                        if (action.type === \"__setState\") {\n                            if (store === undefined) {\n                                setStateFromDevtools(action.state);\n                                return;\n                            }\n                            if (Object.keys(action.state).length !== 1) {\n                                console.error(`\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `);\n                            }\n                            const stateFromDevtools = action.state[store];\n                            if (stateFromDevtools === undefined || stateFromDevtools === null) {\n                                return;\n                            }\n                            if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                                setStateFromDevtools(stateFromDevtools);\n                            }\n                            return;\n                        }\n                        if (!api.dispatchFromDevtools) return;\n                        if (typeof api.dispatch !== \"function\") return;\n                        api.dispatch(action);\n                    });\n                case \"DISPATCH\":\n                    switch(message.payload.type){\n                        case \"RESET\":\n                            setStateFromDevtools(initialState);\n                            if (store === undefined) {\n                                return connection == null ? undefined : connection.init(api.getState());\n                            }\n                            return connection == null ? undefined : connection.init(getTrackedConnectionState(options.name));\n                        case \"COMMIT\":\n                            if (store === undefined) {\n                                connection == null ? undefined : connection.init(api.getState());\n                                return;\n                            }\n                            return connection == null ? undefined : connection.init(getTrackedConnectionState(options.name));\n                        case \"ROLLBACK\":\n                            return parseJsonThen(message.state, (state)=>{\n                                if (store === undefined) {\n                                    setStateFromDevtools(state);\n                                    connection == null ? undefined : connection.init(api.getState());\n                                    return;\n                                }\n                                setStateFromDevtools(state[store]);\n                                connection == null ? undefined : connection.init(getTrackedConnectionState(options.name));\n                            });\n                        case \"JUMP_TO_STATE\":\n                        case \"JUMP_TO_ACTION\":\n                            return parseJsonThen(message.state, (state)=>{\n                                if (store === undefined) {\n                                    setStateFromDevtools(state);\n                                    return;\n                                }\n                                if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                                    setStateFromDevtools(state[store]);\n                                }\n                            });\n                        case \"IMPORT_STATE\":\n                            {\n                                const { nextLiftedState } = message.payload;\n                                const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? undefined : _a.state;\n                                if (!lastComputedState) return;\n                                if (store === undefined) {\n                                    setStateFromDevtools(lastComputedState);\n                                } else {\n                                    setStateFromDevtools(lastComputedState[store]);\n                                }\n                                connection == null ? undefined : connection.send(null, // FIXME no-any\n                                nextLiftedState);\n                                return;\n                            }\n                        case \"PAUSE_RECORDING\":\n                            return isRecording = !isRecording;\n                    }\n                    return;\n            }\n        });\n        return initialState;\n    };\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f)=>{\n    let parsed;\n    try {\n        parsed = JSON.parse(stringified);\n    } catch (e) {\n        console.error(\"[zustand devtools middleware] Could not parse the received json\", e);\n    }\n    if (parsed !== undefined) f(parsed);\n};\nconst subscribeWithSelectorImpl = (fn)=>(set, get, api)=>{\n        const origSubscribe = api.subscribe;\n        api.subscribe = (selector, optListener, options)=>{\n            let listener = selector;\n            if (optListener) {\n                const equalityFn = (options == null ? undefined : options.equalityFn) || Object.is;\n                let currentSlice = selector(api.getState());\n                listener = (state)=>{\n                    const nextSlice = selector(state);\n                    if (!equalityFn(currentSlice, nextSlice)) {\n                        const previousSlice = currentSlice;\n                        optListener(currentSlice = nextSlice, previousSlice);\n                    }\n                };\n                if (options == null ? undefined : options.fireImmediately) {\n                    optListener(currentSlice, currentSlice);\n                }\n            }\n            return origSubscribe(listener);\n        };\n        const initialState = fn(set, get, api);\n        return initialState;\n    };\nconst subscribeWithSelector = subscribeWithSelectorImpl;\nconst combine = (initialState, create)=>(...a)=>Object.assign({}, initialState, create(...a));\nfunction createJSONStorage(getStorage, options) {\n    let storage;\n    try {\n        storage = getStorage();\n    } catch (e) {\n        return;\n    }\n    const persistStorage = {\n        getItem: (name)=>{\n            var _a;\n            const parse = (str2)=>{\n                if (str2 === null) {\n                    return null;\n                }\n                return JSON.parse(str2, options == null ? undefined : options.reviver);\n            };\n            const str = (_a = storage.getItem(name)) != null ? _a : null;\n            if (str instanceof Promise) {\n                return str.then(parse);\n            }\n            return parse(str);\n        },\n        setItem: (name, newValue)=>storage.setItem(name, JSON.stringify(newValue, options == null ? undefined : options.replacer)),\n        removeItem: (name)=>storage.removeItem(name)\n    };\n    return persistStorage;\n}\nconst toThenable = (fn)=>(input)=>{\n        try {\n            const result = fn(input);\n            if (result instanceof Promise) {\n                return result;\n            }\n            return {\n                then (onFulfilled) {\n                    return toThenable(onFulfilled)(result);\n                },\n                catch (_onRejected) {\n                    return this;\n                }\n            };\n        } catch (e) {\n            return {\n                then (_onFulfilled) {\n                    return this;\n                },\n                catch (onRejected) {\n                    return toThenable(onRejected)(e);\n                }\n            };\n        }\n    };\nconst persistImpl = (config, baseOptions)=>(set, get, api)=>{\n        let options = {\n            storage: createJSONStorage(()=>localStorage),\n            partialize: (state)=>state,\n            version: 0,\n            merge: (persistedState, currentState)=>({\n                    ...currentState,\n                    ...persistedState\n                }),\n            ...baseOptions\n        };\n        let hasHydrated = false;\n        const hydrationListeners = /* @__PURE__ */ new Set();\n        const finishHydrationListeners = /* @__PURE__ */ new Set();\n        let storage = options.storage;\n        if (!storage) {\n            return config((...args)=>{\n                console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);\n                set(...args);\n            }, get, api);\n        }\n        const setItem = ()=>{\n            const state = options.partialize({\n                ...get()\n            });\n            return storage.setItem(options.name, {\n                state,\n                version: options.version\n            });\n        };\n        const savedSetState = api.setState;\n        api.setState = (state, replace)=>{\n            savedSetState(state, replace);\n            void setItem();\n        };\n        const configResult = config((...args)=>{\n            set(...args);\n            void setItem();\n        }, get, api);\n        api.getInitialState = ()=>configResult;\n        let stateFromStorage;\n        const hydrate = ()=>{\n            var _a, _b;\n            if (!storage) return;\n            hasHydrated = false;\n            hydrationListeners.forEach((cb)=>{\n                var _a2;\n                return cb((_a2 = get()) != null ? _a2 : configResult);\n            });\n            const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? undefined : _b.call(options, (_a = get()) != null ? _a : configResult)) || undefined;\n            return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue)=>{\n                if (deserializedStorageValue) {\n                    if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n                        if (options.migrate) {\n                            const migration = options.migrate(deserializedStorageValue.state, deserializedStorageValue.version);\n                            if (migration instanceof Promise) {\n                                return migration.then((result)=>[\n                                        true,\n                                        result\n                                    ]);\n                            }\n                            return [\n                                true,\n                                migration\n                            ];\n                        }\n                        console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);\n                    } else {\n                        return [\n                            false,\n                            deserializedStorageValue.state\n                        ];\n                    }\n                }\n                return [\n                    false,\n                    undefined\n                ];\n            }).then((migrationResult)=>{\n                var _a2;\n                const [migrated, migratedState] = migrationResult;\n                stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);\n                set(stateFromStorage, true);\n                if (migrated) {\n                    return setItem();\n                }\n            }).then(()=>{\n                postRehydrationCallback == null ? undefined : postRehydrationCallback(stateFromStorage, undefined);\n                stateFromStorage = get();\n                hasHydrated = true;\n                finishHydrationListeners.forEach((cb)=>cb(stateFromStorage));\n            }).catch((e)=>{\n                postRehydrationCallback == null ? undefined : postRehydrationCallback(undefined, e);\n            });\n        };\n        api.persist = {\n            setOptions: (newOptions)=>{\n                options = {\n                    ...options,\n                    ...newOptions\n                };\n                if (newOptions.storage) {\n                    storage = newOptions.storage;\n                }\n            },\n            clearStorage: ()=>{\n                storage == null ? undefined : storage.removeItem(options.name);\n            },\n            getOptions: ()=>options,\n            rehydrate: ()=>hydrate(),\n            hasHydrated: ()=>hasHydrated,\n            onHydrate: (cb)=>{\n                hydrationListeners.add(cb);\n                return ()=>{\n                    hydrationListeners.delete(cb);\n                };\n            },\n            onFinishHydration: (cb)=>{\n                finishHydrationListeners.add(cb);\n                return ()=>{\n                    finishHydrationListeners.delete(cb);\n                };\n            }\n        };\n        if (!options.skipHydration) {\n            hydrate();\n        }\n        return stateFromStorage || configResult;\n    };\nconst persist = persistImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/middleware.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/react.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/react.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n\n\nconst identity = (arg)=>arg;\nfunction useStore(api, selector = identity) {\n    const slice = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(api.subscribe, ()=>selector(api.getState()), ()=>selector(api.getInitialState()));\n    react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue(slice);\n    return slice;\n}\nconst createImpl = (createState)=>{\n    const api = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)(createState);\n    const useBoundStore = (selector)=>useStore(api, selector);\n    Object.assign(useBoundStore, api);\n    return useBoundStore;\n};\nconst create = (createState)=>createState ? createImpl(createState) : createImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/react.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore)\n/* harmony export */ });\nconst createStoreImpl = (createState)=>{\n    let state;\n    const listeners = /* @__PURE__ */ new Set();\n    const setState = (partial, replace)=>{\n        const nextState = typeof partial === \"function\" ? partial(state) : partial;\n        if (!Object.is(nextState, state)) {\n            const previousState = state;\n            state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n            listeners.forEach((listener)=>listener(state, previousState));\n        }\n    };\n    const getState = ()=>state;\n    const getInitialState = ()=>initialState;\n    const subscribe = (listener)=>{\n        listeners.add(listener);\n        return ()=>listeners.delete(listener);\n    };\n    const api = {\n        setState,\n        getState,\n        getInitialState,\n        subscribe\n    };\n    const initialState = state = createState(setState, getState, api);\n    return api;\n};\nconst createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;