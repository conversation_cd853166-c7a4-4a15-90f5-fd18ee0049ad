/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/terms/page";
exports.ids = ["app/terms/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fterms%2Fpage&page=%2Fterms%2Fpage&appPaths=%2Fterms%2Fpage&pagePath=private-next-app-dir%2Fterms%2Fpage.tsx&appDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fterms%2Fpage&page=%2Fterms%2Fpage&appPaths=%2Fterms%2Fpage&pagePath=private-next-app-dir%2Fterms%2Fpage.tsx&appDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'terms',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/terms/page.tsx */ \"(rsc)/./src/app/terms/page.tsx\")), \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/terms/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/terms/page\",\n        pathname: \"/terms\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fterms%2Fpage&page=%2Fterms%2Fpage&appPaths=%2Fterms%2Fpage&pagePath=private-next-app-dir%2Fterms%2Fpage.tsx&appDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp%5Cfonts.css&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Ccomponents%5Cfont-preloader.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp%5Cfonts.css&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Ccomponents%5Cfont-preloader.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/font-preloader.tsx */ \"(ssr)/./src/components/font-preloader.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RSUzQSU1Q2NvZGluZyU1Q0hUTUwlNUNJbWFnZS10ZXh0LXN0dWRpbyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDc2NyaXB0LmpzJm1vZHVsZXM9RSUzQSU1Q2NvZGluZyU1Q0hUTUwlNUNJbWFnZS10ZXh0LXN0dWRpbyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUUlM0ElNUNjb2RpbmclNUNIVE1MJTVDSW1hZ2UtdGV4dC1zdHVkaW8lNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUUlM0ElNUNjb2RpbmclNUNIVE1MJTVDSW1hZ2UtdGV4dC1zdHVkaW8lNUNzcmMlNUNhcHAlNUNmb250cy5jc3MmbW9kdWxlcz1FJTNBJTVDY29kaW5nJTVDSFRNTCU1Q0ltYWdlLXRleHQtc3R1ZGlvJTVDc3JjJTVDY29tcG9uZW50cyU1Q2ZvbnQtcHJlbG9hZGVyLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQXFIO0FBQ3JIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1hZ2UtdGV4dC1zdHVkaW8tMDMvP2EyZTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxjb2RpbmdcXFxcSFRNTFxcXFxJbWFnZS10ZXh0LXN0dWRpb1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxzY3JpcHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGNvZGluZ1xcXFxIVE1MXFxcXEltYWdlLXRleHQtc3R1ZGlvXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGZvbnQtcHJlbG9hZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp%5Cfonts.css&modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Ccomponents%5Cfont-preloader.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp%5Cterms%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp%5Cterms%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/terms/page.tsx */ \"(ssr)/./src/app/terms/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RSUzQSU1Q2NvZGluZyU1Q0hUTUwlNUNJbWFnZS10ZXh0LXN0dWRpbyU1Q3NyYyU1Q2FwcCU1Q3Rlcm1zJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1hZ2UtdGV4dC1zdHVkaW8tMDMvP2ExZWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxjb2RpbmdcXFxcSFRNTFxcXFxJbWFnZS10ZXh0LXN0dWRpb1xcXFxzcmNcXFxcYXBwXFxcXHRlcm1zXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp%5Cterms%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/terms/page.tsx":
/*!********************************!*\
  !*** ./src/app/terms/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TermsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ScrollablePage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ScrollablePage */ \"(ssr)/./src/components/ScrollablePage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TermsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollablePage__WEBPACK_IMPORTED_MODULE_2__.ScrollablePage, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black text-white p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-3xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Terms of Use\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/\",\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-md flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: \"←\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Back to Studio\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 rounded-lg p-8 shadow-lg space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-purple-400\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 leading-relaxed space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: 'These Terms of Service (\"Terms\") govern your access to and use of Image-Text Studio\\'s website, services, and applications (the \"Service\"). By accessing or using the Service, you agree to be bound by these Terms.'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"We reserve the right to modify these terms at any time. Your continued use of the Service after any changes constitutes your acceptance of the new Terms.\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-purple-400\",\n                                        children: \"Account Responsibilities\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 leading-relaxed space-y-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"You are responsible for safeguarding your account and for any activities or actions under your account. We cannot and will not be liable for any loss or damage arising from your failure to comply with this security obligation.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-purple-400\",\n                                        children: \"Privacy & Data Usage\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 leading-relaxed space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"We respect your privacy and are committed to protecting it. We don't store any information about you except the necessary information required for account functionality.\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The necessary information we collect includes your name, user ID, email address, and account-related information such as subscription status. This information is only used to provide and improve our service.\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"We do not track your usage patterns, sell your data to third parties, or use your information for marketing purposes. Your uploaded content is processed in real-time and is not stored on our servers.\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-purple-400\",\n                                        children: \"Content Ownership\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 leading-relaxed space-y-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"You retain full ownership of your intellectual property rights in the content you create using our Service. By uploading your content, you grant us a limited license solely to process your content in real time for the purpose of delivering our service. We do not store, reproduce, modify, create derivative works, or distribute your content beyond what is strictly necessary for the immediate operation of the service.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-purple-400\",\n                                        children: \"Prohibited Uses\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 leading-relaxed space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"You may not use our Service for any illegal or unauthorized purpose nor may you, in the use of the Service, violate any laws in your jurisdiction.\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Violate any applicable laws or regulations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Infringe upon intellectual property rights\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Transmit harmful or malicious code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Attempt to breach our security measures\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-purple-400\",\n                                        children: \"Last Updated\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 leading-relaxed\",\n                                        children: \"These terms were last updated on March 21, 2025.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\terms\\\\page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/terms/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ScrollablePage.tsx":
/*!*******************************************!*\
  !*** ./src/components/ScrollablePage.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollablePage: () => (/* binding */ ScrollablePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ScrollablePage auto */ \n\nfunction ScrollablePage({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Enable scrolling\n        document.documentElement.style.overflow = \"auto\";\n        document.body.style.overflow = \"auto\";\n        // Cleanup function to reset when unmounting\n        return ()=>{\n            document.documentElement.style.overflow = \"hidden\";\n            document.body.style.overflow = \"hidden\";\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen overflow-auto\",\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\ScrollablePage.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TY3JvbGxhYmxlUGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWtDO0FBTTNCLFNBQVNDLGVBQWUsRUFBRUMsUUFBUSxFQUF1QjtJQUM5REYsZ0RBQVNBLENBQUM7UUFDUixtQkFBbUI7UUFDbkJHLFNBQVNDLGVBQWUsQ0FBQ0MsS0FBSyxDQUFDQyxRQUFRLEdBQUc7UUFDMUNILFNBQVNJLElBQUksQ0FBQ0YsS0FBSyxDQUFDQyxRQUFRLEdBQUc7UUFFL0IsNENBQTRDO1FBQzVDLE9BQU87WUFDTEgsU0FBU0MsZUFBZSxDQUFDQyxLQUFLLENBQUNDLFFBQVEsR0FBRztZQUMxQ0gsU0FBU0ksSUFBSSxDQUFDRixLQUFLLENBQUNDLFFBQVEsR0FBRztRQUNqQztJQUNGLEdBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDRTtRQUFJQyxXQUFVO2tCQUNaUDs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWFnZS10ZXh0LXN0dWRpby0wMy8uL3NyYy9jb21wb25lbnRzL1Njcm9sbGFibGVQYWdlLnRzeD9mMTU5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmludGVyZmFjZSBTY3JvbGxhYmxlUGFnZVByb3BzIHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gU2Nyb2xsYWJsZVBhZ2UoeyBjaGlsZHJlbiB9OiBTY3JvbGxhYmxlUGFnZVByb3BzKSB7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIEVuYWJsZSBzY3JvbGxpbmdcclxuICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5vdmVyZmxvdyA9ICdhdXRvJztcclxuICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnYXV0byc7XHJcbiAgICBcclxuICAgIC8vIENsZWFudXAgZnVuY3Rpb24gdG8gcmVzZXQgd2hlbiB1bm1vdW50aW5nXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUub3ZlcmZsb3cgPSAnaGlkZGVuJztcclxuICAgICAgZG9jdW1lbnQuYm9keS5zdHlsZS5vdmVyZmxvdyA9ICdoaWRkZW4nO1xyXG4gICAgfTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBvdmVyZmxvdy1hdXRvXCI+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0gIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsIlNjcm9sbGFibGVQYWdlIiwiY2hpbGRyZW4iLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsInN0eWxlIiwib3ZlcmZsb3ciLCJib2R5IiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ScrollablePage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/font-preloader.tsx":
/*!*******************************************!*\
  !*** ./src/components/font-preloader.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FontPreloader: () => (/* binding */ FontPreloader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_fonts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/fonts */ \"(ssr)/./src/constants/fonts.ts\");\n/* __next_internal_client_entry_do_not_use__ FontPreloader auto */ \n\n\n// This component preloads Google Fonts by rendering invisible text with each font\nfunction FontPreloader() {\n    const systemFonts = [\n        \"Arial\",\n        \"Times New Roman\",\n        \"Helvetica\",\n        \"Courier New\",\n        \"Verdana\"\n    ];\n    const googleFonts = _constants_fonts__WEBPACK_IMPORTED_MODULE_2__.ALL_FONTS.filter((font)=>!systemFonts.includes(font));\n    const [fontsLoaded, setFontsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Global font loading state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Add loading class to document\n        if (typeof document !== \"undefined\") {\n            document.documentElement.classList.add(\"fonts-loading\");\n        }\n        return ()=>{\n            if (typeof document !== \"undefined\") {\n                document.documentElement.classList.remove(\"fonts-loading\");\n            }\n        };\n    }, []);\n    // Split fonts into smaller chunks to avoid URL length limitations\n    const fontChunks = [];\n    const chunkSize = 15 // Google has a limit on URL length, so we chunk the fonts\n    ;\n    for(let i = 0; i < googleFonts.length; i += chunkSize){\n        fontChunks.push(googleFonts.slice(i, i + chunkSize));\n    }\n    // Active font loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof document === \"undefined\") return;\n        let loadedCount = 0;\n        const totalFonts = googleFonts.length;\n        // Load fonts in chunks\n        const loadFontChunks = async ()=>{\n            for (const chunk of fontChunks){\n                await loadFontChunk(chunk);\n            }\n            // Mark all fonts as loaded\n            if (typeof document !== \"undefined\") {\n                document.documentElement.classList.add(\"fonts-loaded\");\n                document.documentElement.classList.remove(\"fonts-loading\");\n                setFontsLoaded(true);\n            }\n        };\n        // Load a chunk of fonts\n        const loadFontChunk = (fontChunk)=>{\n            return new Promise((resolve)=>{\n                // Create link element for this chunk\n                const link = document.createElement(\"link\");\n                link.rel = \"stylesheet\";\n                link.href = `https://fonts.googleapis.com/css2?${fontChunk.map((font)=>`family=${font.replace(/\\s+/g, \"+\")}:wght@400;700&display=swap`).join(\"&\")}&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789`;\n                // When this chunk is loaded\n                link.onload = ()=>{\n                    loadedCount += fontChunk.length;\n                    // Force load fonts by rendering text\n                    const canvas = document.createElement(\"canvas\");\n                    const ctx = canvas.getContext(\"2d\");\n                    if (ctx) {\n                        fontChunk.forEach((font)=>{\n                            // Force browser to load the font\n                            ctx.font = `15px \"${font}\", sans-serif`;\n                            ctx.fillText(font, 0, 15);\n                        });\n                    }\n                    // Continue to next chunk\n                    resolve();\n                };\n                // If loading fails, still continue\n                link.onerror = ()=>{\n                    console.warn(`Failed to load font chunk: ${fontChunk.join(\", \")}`);\n                    resolve();\n                };\n                // Add to document\n                document.head.appendChild(link);\n            });\n        };\n        // Start loading\n        loadFontChunks();\n        // Fallback: if loading takes too long, mark as loaded after timeout\n        const timeoutId = setTimeout(()=>{\n            if (!fontsLoaded && typeof document !== \"undefined\") {\n                document.documentElement.classList.add(\"fonts-loaded\");\n                document.documentElement.classList.remove(\"fonts-loading\");\n                setFontsLoaded(true);\n            }\n        }, 3000);\n        return ()=>{\n            clearTimeout(timeoutId);\n        };\n    }, [\n        fontChunks,\n        fontsLoaded\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"absolute\",\n            visibility: \"hidden\",\n            pointerEvents: \"none\",\n            height: 0,\n            width: 0,\n            overflow: \"hidden\"\n        },\n        children: [\n            systemFonts.map((font)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontFamily: `'${font}', sans-serif`,\n                        fontDisplay: \"swap\"\n                    },\n                    children: [\n                        font,\n                        \" - The quick brown fox jumps over the lazy dog\"\n                    ]\n                }, font, true, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\font-preloader.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)),\n            googleFonts.map((font)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontFamily: `'${font}', sans-serif`,\n                        fontDisplay: \"swap\"\n                    },\n                    children: [\n                        font,\n                        \" - The quick brown fox jumps over the lazy dog\"\n                    ]\n                }, font, true, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\font-preloader.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\font-preloader.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/font-preloader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/constants/fonts.ts":
/*!********************************!*\
  !*** ./src/constants/fonts.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_FONTS: () => (/* binding */ ALL_FONTS),\n/* harmony export */   FREE_FONTS: () => (/* binding */ FREE_FONTS)\n/* harmony export */ });\nconst FREE_FONTS = [\n    \"Arial\",\n    \"Times New Roman\",\n    \"Helvetica\",\n    \"Georgia\",\n    \"Verdana\",\n    \"Inter\",\n    \"Playfair Display\",\n    \"Dancing Script\",\n    \"Oswald\",\n    \"Merriweather\",\n    \"Pacifico\"\n];\nconst ALL_FONTS = [\n    \"Arial\",\n    \"Times New Roman\",\n    \"Helvetica\",\n    \"Georgia\",\n    \"Verdana\",\n    \"ABeeZee\",\n    \"Abel\",\n    \"Abril Fatface\",\n    \"Acme\",\n    \"Akshar\",\n    \"Alata\",\n    \"Albert Sans\",\n    \"Alegreya\",\n    \"Alegreya Sans\",\n    \"Alegreya Sans SC\",\n    \"Alfa Slab One\",\n    \"Alice\",\n    \"Allerta Stencil\",\n    \"Almarai\",\n    \"Amatic SC\",\n    \"Amiri\",\n    \"Antic Slab\",\n    \"Anton\",\n    \"Architects Daughter\",\n    \"Archivo\",\n    \"Archivo Black\",\n    \"Archivo Narrow\",\n    \"Arimo\",\n    \"Arsenal\",\n    \"Arvo\",\n    \"Asap\",\n    \"Asap Condensed\",\n    \"Assistant\",\n    \"Barlow\",\n    \"Barlow Condensed\",\n    \"Barlow Semi Condensed\",\n    \"Be Vietnam Pro\",\n    \"Bebas Neue\",\n    \"Big Shoulders Stencil\",\n    \"Birthstone\",\n    \"Bitter\",\n    \"Black Ops One\",\n    \"Bodoni Moda\",\n    \"Boldonse\",\n    \"Bree Serif\",\n    \"Bungee\",\n    \"Bytesized\",\n    \"Cabin\",\n    \"Cairo\",\n    \"Cantarell\",\n    \"Cardo\",\n    \"Catamaran\",\n    \"Caveat\",\n    \"Chakra Petch\",\n    \"Changa\",\n    \"Chivo\",\n    \"Cinzel\",\n    \"Comfortaa\",\n    \"Commissioner\",\n    \"Concert One\",\n    \"Cookie\",\n    \"Cormorant\",\n    \"Cormorant Garamond\",\n    \"Courgette\",\n    \"Crete Round\",\n    \"Crimson Pro\",\n    \"Crimson Text\",\n    \"Cuprum\",\n    \"DM Sans\",\n    \"DM Serif Display\",\n    \"DM Serif Text\",\n    \"Dancing Script\",\n    \"Didact Gothic\",\n    \"Domine\",\n    \"Dosis\",\n    \"EB Garamond\",\n    \"Eczar\",\n    \"El Messiri\",\n    \"Electrolize\",\n    \"Encode Sans\",\n    \"Encode Sans Condensed\",\n    \"Exo\",\n    \"Exo 2\",\n    \"Figtree\",\n    \"Fira Sans\",\n    \"Fira Sans Condensed\",\n    \"Fjalla One\",\n    \"Francois One\",\n    \"Frank Ruhl Libre\",\n    \"Fraunces\",\n    \"Gelasio\",\n    \"Gloria Hallelujah\",\n    \"Gothic A1\",\n    \"Great Vibes\",\n    \"Gruppo\",\n    \"Hachi Maru Pop\",\n    \"Heebo\",\n    \"Hind\",\n    \"Hind Madurai\",\n    \"Hind Siliguri\",\n    \"Hurricane\",\n    \"IBM Plex Mono\",\n    \"IBM Plex Sans\",\n    \"IBM Plex Sans Arabic\",\n    \"IBM Plex Sans Condensed\",\n    \"IBM Plex Serif\",\n    \"Inconsolata\",\n    \"Indie Flower\",\n    \"Inter\",\n    \"Inter Tight\",\n    \"Jaini\",\n    \"Josefin Sans\",\n    \"Josefin Slab\",\n    \"Jost\",\n    \"Kalam\",\n    \"Kanit\",\n    \"Karla\",\n    \"Kaushan Script\",\n    \"Khand\",\n    \"Lato\",\n    \"League Spartan\",\n    \"Lexend\",\n    \"Lexend Deca\",\n    \"Libre Barcode 39\",\n    \"Libre Baskerville\",\n    \"Libre Caslon Text\",\n    \"Libre Franklin\",\n    \"Lilita One\",\n    \"Lobster\",\n    \"Lobster Two\",\n    \"Lora\",\n    \"Luckiest Guy\",\n    \"M PLUS 1p\",\n    \"M PLUS Rounded 1c\",\n    \"Macondo\",\n    \"Manrope\",\n    \"Marcellus\",\n    \"Martel\",\n    \"Mate\",\n    \"Merriweather\",\n    \"Montserrat\",\n    \"Mukta\",\n    \"Mulish\",\n    \"Nanum Gothic\",\n    \"Noto Sans\",\n    \"Noto Sans JP\",\n    \"Noto Sans KR\",\n    \"Noto Serif\",\n    \"Nunito\",\n    \"Nunito Sans\",\n    \"Open Sans\",\n    \"Oswald\",\n    \"Oxygen\",\n    \"PT Sans\",\n    \"PT Serif\",\n    \"Pacifico\",\n    \"Poppins\",\n    \"Prompt\",\n    \"Quicksand\",\n    \"Raleway\",\n    \"Roboto\",\n    \"Roboto Condensed\",\n    \"Roboto Mono\",\n    \"Roboto Slab\",\n    \"Rubik\",\n    \"Sacramento\",\n    \"Source Code Pro\",\n    \"Source Sans Pro\",\n    \"Source Serif Pro\",\n    \"Space Grotesk\",\n    \"Space Mono\",\n    \"Spectral\",\n    \"Syne\",\n    \"Tajawal\",\n    \"Tangerine\",\n    \"Titillium Web\",\n    \"Ubuntu\",\n    \"Ubuntu Mono\",\n    \"Varela Round\",\n    \"Winky Sans\",\n    \"Work Sans\",\n    \"Yanone Kaffeesatz\",\n    \"Yeseva One\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29uc3RhbnRzL2ZvbnRzLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU8sTUFBTUEsYUFBYTtJQUN0QjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0gsQ0FBQztBQUVLLE1BQU1DLFlBQVk7SUFDckI7SUFBUztJQUFtQjtJQUFhO0lBQVc7SUFDcEQ7SUFBVztJQUFRO0lBQWlCO0lBQVE7SUFBVTtJQUFTO0lBQy9EO0lBQVk7SUFBaUI7SUFBb0I7SUFBaUI7SUFBUztJQUFtQjtJQUM5RjtJQUFhO0lBQVM7SUFBYztJQUFTO0lBQXVCO0lBQ3BFO0lBQWlCO0lBQWtCO0lBQVM7SUFBVztJQUFRO0lBQVE7SUFDdkU7SUFBYTtJQUFVO0lBQW9CO0lBQXlCO0lBQ3BFO0lBQWM7SUFBeUI7SUFBYztJQUFVO0lBQWlCO0lBQWU7SUFBWTtJQUFjO0lBQVU7SUFBYTtJQUNoSjtJQUFTO0lBQWE7SUFBUztJQUFhO0lBQVU7SUFBZ0I7SUFBVTtJQUNoRjtJQUFVO0lBQWE7SUFBZ0I7SUFBZTtJQUFVO0lBQ2hFO0lBQXNCO0lBQWE7SUFBZTtJQUFlO0lBQWdCO0lBQ2pGO0lBQVc7SUFBb0I7SUFBaUI7SUFBa0I7SUFBaUI7SUFDbkY7SUFBUztJQUFlO0lBQVM7SUFBYztJQUFlO0lBQzlEO0lBQXlCO0lBQU87SUFBUztJQUFXO0lBQWE7SUFDakU7SUFBYztJQUFnQjtJQUFvQjtJQUFZO0lBQVc7SUFDekU7SUFBYTtJQUFlO0lBQVU7SUFBa0I7SUFBUztJQUFRO0lBQWdCO0lBQWlCO0lBQzFHO0lBQWlCO0lBQWlCO0lBQXdCO0lBQzFEO0lBQWtCO0lBQWU7SUFBZ0I7SUFBUztJQUFlO0lBQVM7SUFDbEY7SUFBZ0I7SUFBUTtJQUFTO0lBQVM7SUFBUztJQUFrQjtJQUFTO0lBQzlFO0lBQWtCO0lBQVU7SUFBZTtJQUFvQjtJQUMvRDtJQUFxQjtJQUFrQjtJQUFjO0lBQVc7SUFBZTtJQUMvRTtJQUFnQjtJQUFhO0lBQXFCO0lBQVc7SUFBVztJQUN4RTtJQUFVO0lBQVE7SUFBZ0I7SUFBYztJQUFTO0lBQVU7SUFDbkU7SUFBYTtJQUFnQjtJQUFnQjtJQUFjO0lBQVU7SUFBZTtJQUFhO0lBQVU7SUFDM0c7SUFBVztJQUFZO0lBQVk7SUFBVztJQUFVO0lBQWE7SUFDckU7SUFBVTtJQUFvQjtJQUFlO0lBQWU7SUFBUztJQUNyRTtJQUFtQjtJQUFtQjtJQUFvQjtJQUFpQjtJQUMzRTtJQUFZO0lBQVE7SUFBVztJQUFhO0lBQWlCO0lBQVU7SUFBZTtJQUN0RjtJQUFjO0lBQWE7SUFBcUI7Q0FDbkQsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ltYWdlLXRleHQtc3R1ZGlvLTAzLy4vc3JjL2NvbnN0YW50cy9mb250cy50cz81NmI4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBGUkVFX0ZPTlRTID0gW1xyXG4gICAgJ0FyaWFsJyxcclxuICAgICdUaW1lcyBOZXcgUm9tYW4nLFxyXG4gICAgJ0hlbHZldGljYScsXHJcbiAgICAnR2VvcmdpYScsXHJcbiAgICAnVmVyZGFuYScsXHJcbiAgICAnSW50ZXInLFxyXG4gICAgJ1BsYXlmYWlyIERpc3BsYXknLFxyXG4gICAgJ0RhbmNpbmcgU2NyaXB0JyxcclxuICAgICdPc3dhbGQnLFxyXG4gICAgJ01lcnJpd2VhdGhlcicsXHJcbiAgICAnUGFjaWZpY28nXHJcbl07XHJcblxyXG5leHBvcnQgY29uc3QgQUxMX0ZPTlRTID0gW1xyXG4gICAgXCJBcmlhbFwiLCBcIlRpbWVzIE5ldyBSb21hblwiLCBcIkhlbHZldGljYVwiLCBcIkdlb3JnaWFcIiwgXCJWZXJkYW5hXCIsIFxyXG4gICAgXCJBQmVlWmVlXCIsIFwiQWJlbFwiLCBcIkFicmlsIEZhdGZhY2VcIiwgXCJBY21lXCIsIFwiQWtzaGFyXCIsIFwiQWxhdGFcIiwgXCJBbGJlcnQgU2Fuc1wiLCBcclxuICAgIFwiQWxlZ3JleWFcIiwgXCJBbGVncmV5YSBTYW5zXCIsIFwiQWxlZ3JleWEgU2FucyBTQ1wiLCBcIkFsZmEgU2xhYiBPbmVcIiwgXCJBbGljZVwiLCBcIkFsbGVydGEgU3RlbmNpbFwiLCBcIkFsbWFyYWlcIixcclxuICAgIFwiQW1hdGljIFNDXCIsIFwiQW1pcmlcIiwgXCJBbnRpYyBTbGFiXCIsIFwiQW50b25cIiwgXCJBcmNoaXRlY3RzIERhdWdodGVyXCIsIFwiQXJjaGl2b1wiLCBcclxuICAgIFwiQXJjaGl2byBCbGFja1wiLCBcIkFyY2hpdm8gTmFycm93XCIsIFwiQXJpbW9cIiwgXCJBcnNlbmFsXCIsIFwiQXJ2b1wiLCBcIkFzYXBcIiwgXCJBc2FwIENvbmRlbnNlZFwiLFxyXG4gICAgXCJBc3Npc3RhbnRcIiwgXCJCYXJsb3dcIiwgXCJCYXJsb3cgQ29uZGVuc2VkXCIsIFwiQmFybG93IFNlbWkgQ29uZGVuc2VkXCIsIFwiQmUgVmlldG5hbSBQcm9cIixcclxuICAgIFwiQmViYXMgTmV1ZVwiLCBcIkJpZyBTaG91bGRlcnMgU3RlbmNpbFwiLCBcIkJpcnRoc3RvbmVcIiwgXCJCaXR0ZXJcIiwgXCJCbGFjayBPcHMgT25lXCIsIFwiQm9kb25pIE1vZGFcIiwgXCJCb2xkb25zZVwiLCBcIkJyZWUgU2VyaWZcIiwgXCJCdW5nZWVcIiwgXCJCeXRlc2l6ZWRcIiwgXCJDYWJpblwiLFxyXG4gICAgXCJDYWlyb1wiLCBcIkNhbnRhcmVsbFwiLCBcIkNhcmRvXCIsIFwiQ2F0YW1hcmFuXCIsIFwiQ2F2ZWF0XCIsIFwiQ2hha3JhIFBldGNoXCIsIFwiQ2hhbmdhXCIsIFwiQ2hpdm9cIixcclxuICAgIFwiQ2luemVsXCIsIFwiQ29tZm9ydGFhXCIsIFwiQ29tbWlzc2lvbmVyXCIsIFwiQ29uY2VydCBPbmVcIiwgXCJDb29raWVcIiwgXCJDb3Jtb3JhbnRcIiwgXHJcbiAgICBcIkNvcm1vcmFudCBHYXJhbW9uZFwiLCBcIkNvdXJnZXR0ZVwiLCBcIkNyZXRlIFJvdW5kXCIsIFwiQ3JpbXNvbiBQcm9cIiwgXCJDcmltc29uIFRleHRcIiwgXCJDdXBydW1cIixcclxuICAgIFwiRE0gU2Fuc1wiLCBcIkRNIFNlcmlmIERpc3BsYXlcIiwgXCJETSBTZXJpZiBUZXh0XCIsIFwiRGFuY2luZyBTY3JpcHRcIiwgXCJEaWRhY3QgR290aGljXCIsIFwiRG9taW5lXCIsXHJcbiAgICBcIkRvc2lzXCIsIFwiRUIgR2FyYW1vbmRcIiwgXCJFY3phclwiLCBcIkVsIE1lc3NpcmlcIiwgXCJFbGVjdHJvbGl6ZVwiLCBcIkVuY29kZSBTYW5zXCIsIFxyXG4gICAgXCJFbmNvZGUgU2FucyBDb25kZW5zZWRcIiwgXCJFeG9cIiwgXCJFeG8gMlwiLCBcIkZpZ3RyZWVcIiwgXCJGaXJhIFNhbnNcIiwgXCJGaXJhIFNhbnMgQ29uZGVuc2VkXCIsXHJcbiAgICBcIkZqYWxsYSBPbmVcIiwgXCJGcmFuY29pcyBPbmVcIiwgXCJGcmFuayBSdWhsIExpYnJlXCIsIFwiRnJhdW5jZXNcIiwgXCJHZWxhc2lvXCIsIFwiR2xvcmlhIEhhbGxlbHVqYWhcIixcclxuICAgIFwiR290aGljIEExXCIsIFwiR3JlYXQgVmliZXNcIiwgXCJHcnVwcG9cIiwgXCJIYWNoaSBNYXJ1IFBvcFwiLCBcIkhlZWJvXCIsIFwiSGluZFwiLCBcIkhpbmQgTWFkdXJhaVwiLCBcIkhpbmQgU2lsaWd1cmlcIiwgXCJIdXJyaWNhbmVcIixcclxuICAgIFwiSUJNIFBsZXggTW9ub1wiLCBcIklCTSBQbGV4IFNhbnNcIiwgXCJJQk0gUGxleCBTYW5zIEFyYWJpY1wiLCBcIklCTSBQbGV4IFNhbnMgQ29uZGVuc2VkXCIsIFxyXG4gICAgXCJJQk0gUGxleCBTZXJpZlwiLCBcIkluY29uc29sYXRhXCIsIFwiSW5kaWUgRmxvd2VyXCIsIFwiSW50ZXJcIiwgXCJJbnRlciBUaWdodFwiLCBcIkphaW5pXCIsIFwiSm9zZWZpbiBTYW5zXCIsXHJcbiAgICBcIkpvc2VmaW4gU2xhYlwiLCBcIkpvc3RcIiwgXCJLYWxhbVwiLCBcIkthbml0XCIsIFwiS2FybGFcIiwgXCJLYXVzaGFuIFNjcmlwdFwiLCBcIktoYW5kXCIsIFwiTGF0b1wiLFxyXG4gICAgXCJMZWFndWUgU3BhcnRhblwiLCBcIkxleGVuZFwiLCBcIkxleGVuZCBEZWNhXCIsIFwiTGlicmUgQmFyY29kZSAzOVwiLCBcIkxpYnJlIEJhc2tlcnZpbGxlXCIsXHJcbiAgICBcIkxpYnJlIENhc2xvbiBUZXh0XCIsIFwiTGlicmUgRnJhbmtsaW5cIiwgXCJMaWxpdGEgT25lXCIsIFwiTG9ic3RlclwiLCBcIkxvYnN0ZXIgVHdvXCIsIFwiTG9yYVwiLFxyXG4gICAgXCJMdWNraWVzdCBHdXlcIiwgXCJNIFBMVVMgMXBcIiwgXCJNIFBMVVMgUm91bmRlZCAxY1wiLCBcIk1hY29uZG9cIiwgXCJNYW5yb3BlXCIsIFwiTWFyY2VsbHVzXCIsXHJcbiAgICBcIk1hcnRlbFwiLCBcIk1hdGVcIiwgXCJNZXJyaXdlYXRoZXJcIiwgXCJNb250c2VycmF0XCIsIFwiTXVrdGFcIiwgXCJNdWxpc2hcIiwgXCJOYW51bSBHb3RoaWNcIixcclxuICAgIFwiTm90byBTYW5zXCIsIFwiTm90byBTYW5zIEpQXCIsIFwiTm90byBTYW5zIEtSXCIsIFwiTm90byBTZXJpZlwiLCBcIk51bml0b1wiLCBcIk51bml0byBTYW5zXCIsIFwiT3BlbiBTYW5zXCIsIFwiT3N3YWxkXCIsIFwiT3h5Z2VuXCIsXHJcbiAgICBcIlBUIFNhbnNcIiwgXCJQVCBTZXJpZlwiLCBcIlBhY2lmaWNvXCIsIFwiUG9wcGluc1wiLCBcIlByb21wdFwiLCBcIlF1aWNrc2FuZFwiLCBcIlJhbGV3YXlcIiwgXHJcbiAgICBcIlJvYm90b1wiLCBcIlJvYm90byBDb25kZW5zZWRcIiwgXCJSb2JvdG8gTW9ub1wiLCBcIlJvYm90byBTbGFiXCIsIFwiUnViaWtcIiwgXCJTYWNyYW1lbnRvXCIsXHJcbiAgICBcIlNvdXJjZSBDb2RlIFByb1wiLCBcIlNvdXJjZSBTYW5zIFByb1wiLCBcIlNvdXJjZSBTZXJpZiBQcm9cIiwgXCJTcGFjZSBHcm90ZXNrXCIsIFwiU3BhY2UgTW9ub1wiLFxyXG4gICAgXCJTcGVjdHJhbFwiLCBcIlN5bmVcIiwgXCJUYWphd2FsXCIsIFwiVGFuZ2VyaW5lXCIsIFwiVGl0aWxsaXVtIFdlYlwiLCBcIlVidW50dVwiLCBcIlVidW50dSBNb25vXCIsIFwiVmFyZWxhIFJvdW5kXCIsXHJcbiAgICBcIldpbmt5IFNhbnNcIiwgXCJXb3JrIFNhbnNcIiwgXCJZYW5vbmUgS2FmZmVlc2F0elwiLCBcIlllc2V2YSBPbmVcIlxyXG5dOyAiXSwibmFtZXMiOlsiRlJFRV9GT05UUyIsIkFMTF9GT05UUyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/constants/fonts.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/fonts.css":
/*!***************************!*\
  !*** ./src/app/fonts.css ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"efea74c4660a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2ZvbnRzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2ltYWdlLXRleHQtc3R1ZGlvLTAzLy4vc3JjL2FwcC9mb250cy5jc3M/YzkyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVmZWE3NGM0NjYwYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/fonts.css\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bbfc820773d2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1hZ2UtdGV4dC1zdHVkaW8tMDMvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzE2OWUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiYmZjODIwNzczZDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _fonts_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./fonts.css */ \"(rsc)/./src/app/fonts.css\");\n/* harmony import */ var _components_font_preloader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/font-preloader */ \"(rsc)/./src/components/font-preloader.tsx\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Image Text Studio - Free Image Text Editor\",\n    description: \"Create beautiful text overlays on images with our completely free editor. No signup required, unlimited use.\",\n    icons: {\n        icon: \"/favicon.jpg\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        as: \"font\",\n                        href: \"https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        as: \"font\",\n                        href: \"https://fonts.gstatic.com/s/opensans/v35/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVI.woff2\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        as: \"font\",\n                        href: \"https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Hw5aXo.woff2\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@400;500;600;700&family=Oswald:wght@400;500;600;700&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Big+Shoulders+Stencil:opsz,wght@10..72,100..900&family=Boldonse&family=Bytesized&family=Jaini&family=Noto+Sans+JP:wght@100..900&family=Noto+Sans+KR:wght@100..900&family=Akshar:wght@300..700&family=Allerta+Stencil&family=Hachi+Maru+Pop&family=Tangerine:wght@400;700&family=Birthstone&family=Hurricane&family=Winky+Sans:ital,wght@0,300..900;1,300..900&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=ABeeZee:ital@0;1&family=Abel&family=Abril+Fatface&family=Acme&family=Alata&family=Albert+Sans:wght@100;200;300;400;500;600;700;800;900&family=Alegreya:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Alegreya+Sans:ital,wght@0,100;0,300;0,400;0,500;0,700;0,800;0,900;1,100;1,300;1,400;1,500;1,700;1,800;1,900&family=Alegreya+Sans+SC:ital,wght@0,100;0,300;0,400;0,500;0,700;0,800;0,900;1,100;1,300;1,400;1,500;1,700;1,800;1,900&family=Alfa+Slab+One&family=Alice&family=Amatic+SC:wght@400;700&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Anton&family=Archivo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Arimo:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&family=Asap:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Barlow:wght@100;200;300;400;500;600;700;800;900&family=Caveat:wght@400;500;600;700&family=Dancing+Script:wght@400;500;600;700&family=DM+Sans:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Fira+Sans:wght@100;200;300;400;500;600;700;800;900&family=Josefin+Sans:wght@100;200;300;400;500;600;700&family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&family=Mulish:wght@200;300;400;500;600;700;800;900&family=Nunito:wght@200;300;400;500;600;700;800;900&family=Oxygen:wght@300;400;700&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-loading-spinner\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-loading-content\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"min-h-screen\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_font_preloader__WEBPACK_IMPORTED_MODULE_3__.FontPreloader, {}, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        id: \"font-loader\",\n                        strategy: \"beforeInteractive\",\n                        children: `\r\n            // Mark document as loading fonts ASAP\r\n            document.documentElement.classList.add('fonts-loading');\r\n            \r\n            // Initialize document once the initial core fonts are loaded\r\n            if ('fonts' in document) {\r\n              Promise.all([\r\n                document.fonts.load('1em \"Roboto\"'),\r\n                document.fonts.load('1em \"Open Sans\"'),\r\n                document.fonts.load('1em \"Montserrat\"'),\r\n                document.fonts.load('1em \"Poppins\"')\r\n              ]).then(() => {\r\n                // Show content with core fonts loaded\r\n                document.documentElement.classList.add('fonts-core-loaded');\r\n              }).catch(() => {\r\n                // Fallback if font loading fails\r\n                setTimeout(() => {\r\n                  document.documentElement.classList.add('fonts-core-loaded');\r\n                }, 500);\r\n              });\r\n            } else {\r\n              // Browser doesn't support font loading API\r\n              document.documentElement.classList.add('fonts-core-loaded');\r\n            }\r\n          `\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/terms/page.tsx":
/*!********************************!*\
  !*** ./src/app/terms/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding\HTML\Image-text-studio\src\app\terms\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/font-preloader.tsx":
/*!*******************************************!*\
  !*** ./src/components/font-preloader.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FontPreloader: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding\HTML\Image-text-studio\src\components\font-preloader.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\coding\HTML\Image-text-studio\src\components\font-preloader.tsx#FontPreloader`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fterms%2Fpage&page=%2Fterms%2Fpage&appPaths=%2Fterms%2Fpage&pagePath=private-next-app-dir%2Fterms%2Fpage.tsx&appDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();