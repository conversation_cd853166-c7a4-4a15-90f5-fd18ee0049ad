"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/studio/studio.tsx":
/*!******************************************!*\
  !*** ./src/components/studio/studio.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Studio: function() { return /* binding */ Studio; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.ts\");\n/* harmony import */ var _studio_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./studio-header */ \"(app-pages-browser)/./src/components/studio/studio-header.tsx\");\n/* harmony import */ var _studio_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./studio-sidebar */ \"(app-pages-browser)/./src/components/studio/studio-sidebar.tsx\");\n/* harmony import */ var _image_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./image-upload */ \"(app-pages-browser)/./src/components/studio/image-upload.tsx\");\n/* harmony import */ var _text_layer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./text-layer */ \"(app-pages-browser)/./src/components/studio/text-layer.tsx\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Studio auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Studio() {\n    _s();\n    const { image, subjectImage, textLayers, setActiveLayerId, imageEffects } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)();\n    if (!image) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_6__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_image_upload__WEBPACK_IMPORTED_MODULE_4__.ImageUpload, {}, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Handle click on canvas (outside of text) to deselect\n    const handleCanvasClick = ()=>{\n        setActiveLayerId(null);\n    };\n    // Filter text layers into two groups: behind subject and over subject\n    const behindSubjectLayers = textLayers.filter((layer)=>layer.isBehindSubject);\n    const overSubjectLayers = textLayers.filter((layer)=>!layer.isBehindSubject);\n    // Get original index for each layer to use for z-index calculation\n    const getLayerIndex = (layerId)=>{\n        return textLayers.findIndex((layer)=>layer.id === layerId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 flex flex-col h-screen w-full bg-black text-white overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_6__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_studio_header__WEBPACK_IMPORTED_MODULE_2__.StudioHeader, {}, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-[55vh] md:h-auto md:w-3/5 bg-black/10 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            \"data-canvas\": \"true\",\n                            className: \"relative p-1 md:p-4 min-w-[320px] min-h-[320px] md:min-w-[640px] md:min-h-[480px] overflow-hidden flex items-center justify-center\",\n                            onClick: handleCanvasClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mx-auto\",\n                                style: {\n                                    display: \"inline-block\",\n                                    maxWidth: \"100%\",\n                                    maxHeight: \"100%\"\n                                },\n                                children: [\n                                    imageEffects.applyToBackground ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Background\",\n                                        className: \"max-w-full w-full h-auto object-contain md:max-h-[calc(100vh-10rem)]\",\n                                        style: {\n                                            filter: \"blur(\".concat(imageEffects.blur, \"px) brightness(\").concat(100 - imageEffects.darken, \"%)\"),\n                                            maxWidth: \"100%\",\n                                            maxHeight: \"calc(60vh - 2rem)\",\n                                            minWidth: \"320px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: \"Background\",\n                                        className: \"max-w-full w-full h-auto object-contain md:max-h-[calc(100vh-10rem)]\",\n                                        style: {\n                                            maxWidth: \"100%\",\n                                            maxHeight: \"calc(60vh - 2rem)\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this),\n                                    imageEffects.vignette > 0 && imageEffects.applyToBackground && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 pointer-events-none\",\n                                        style: {\n                                            background: \"radial-gradient(circle at center, transparent 0%, rgba(0,0,0,\".concat(imageEffects.vignette / 100, \") 100%)\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this),\n                                    subjectImage && behindSubjectLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_text_layer__WEBPACK_IMPORTED_MODULE_5__.TextLayer, {\n                                            layer: layer,\n                                            zIndex: 10 + (textLayers.length - getLayerIndex(layer.id))\n                                        }, \"behind-\".concat(layer.id), false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)),\n                                    subjectImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: subjectImage,\n                                        alt: \"Subject\",\n                                        className: \"absolute top-0 left-0 w-full h-full pointer-events-none object-contain\",\n                                        style: {\n                                            filter: imageEffects.applyToSubject ? \"blur(\".concat(imageEffects.blur, \"px) brightness(\").concat(100 - imageEffects.darken, \"%)\") : \"none\",\n                                            zIndex: 100,\n                                            maxWidth: \"100%\",\n                                            maxHeight: \"calc(55vh - 2rem)\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    imageEffects.vignette > 0 && imageEffects.applyToSubject && subjectImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 pointer-events-none\",\n                                        style: {\n                                            background: \"radial-gradient(circle at center, transparent 0%, rgba(0,0,0,\".concat(imageEffects.vignette / 100, \") 100%)\"),\n                                            mixBlendMode: \"multiply\",\n                                            pointerEvents: \"none\",\n                                            zIndex: 101\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this),\n                                    overSubjectLayers.map((layer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_text_layer__WEBPACK_IMPORTED_MODULE_5__.TextLayer, {\n                                            layer: layer,\n                                            zIndex: 200 + (textLayers.length - getLayerIndex(layer.id))\n                                        }, layer.id, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 h-full overflow-hidden bg-black\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_studio_sidebar__WEBPACK_IMPORTED_MODULE_3__.StudioSidebar, {}, void 0, false, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\studio.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(Studio, \"zsKxWRXxy01FDIPlHX/iVVbylbQ=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore\n    ];\n});\n_c = Studio;\nvar _c;\n$RefreshReg$(_c, \"Studio\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/studio/studio.tsx\n"));

/***/ })

});