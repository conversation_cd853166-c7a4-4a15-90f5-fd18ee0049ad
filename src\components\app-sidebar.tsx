"use client"

import { useEffect, useState } from 'react'
import { Menu, X, Info, File, Home } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useStore } from '@/store/useStore'
import Link from 'next/link'

interface AppSidebarProps {
  className?: string
}

export function AppSidebar({ className }: AppSidebarProps) {
  const [open, setOpen] = useState(false)
  const { setActiveLayerId } = useStore()
  
  // Close sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      
      // Check if the click is outside the sidebar and not on the menu button
      if (open && !target.closest('[data-sidebar]') && !target.closest('[data-sidebar-trigger]')) {
        setOpen(false)
      }
    }
    
    // When canvas is clicked, deselect active text layer and close sidebar
    const handleCanvasClick = () => {
      setOpen(false)
    }
    
    document.addEventListener('click', handleClickOutside)
    
    // Find and add event listener to the canvas element
    const canvasElement = document.querySelector('[data-canvas]')
    if (canvasElement) {
      canvasElement.addEventListener('click', handleCanvasClick)
    }
    
    return () => {
      document.removeEventListener('click', handleClickOutside)
      if (canvasElement) {
        canvasElement.removeEventListener('click', handleCanvasClick)
      }
    }
  }, [open, setActiveLayerId])
  
  return (
    <>
      {/* Sidebar Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setOpen(!open)}
        className="absolute top-1 left-2 z-50 h-8 w-8 text-white hover:bg-white/10"
        data-sidebar-trigger="true"
      >
        <Menu className="h-4 w-4" />
        <span className="sr-only">Toggle Menu</span>
      </Button>
      
      {/* Sidebar Overlay */}
      {open && (
        <div 
          className="fixed inset-0 z-40 bg-black/50" 
          onClick={() => setOpen(false)}
        />
      )}
      
      {/* Sidebar Content */}
      <div
        data-sidebar="true"
        className={cn(
          "fixed top-0 left-0 z-50 h-full w-64 bg-black border-r border-white/10 p-4 shadow-xl transition-transform duration-200 ease-in-out",
          open ? "translate-x-0" : "-translate-x-full",
          className
        )}
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-bold text-white">Image-Text Studio</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setOpen(false)}
            className="h-8 w-8 text-white hover:bg-white/10"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </div>

        {/* Free Tool Info */}
        <div className="mb-6 py-4 border-b border-white/10">
          <div className="text-center">
            <p className="text-sm text-white font-medium mb-2">✨ Completely Free Tool</p>
            <p className="text-xs text-white/60">
              No signup required • Unlimited use • All features available
            </p>
          </div>
        </div>
        
        {/* Navigation Links */}
        <nav className="space-y-1">
          <Link href="/" className="block w-full">
            <Button
              variant="ghost"
              className="w-full justify-start text-white hover:bg-white/10"
            >
              <Home className="mr-2 h-4 w-4" />
              Home
            </Button>
          </Link>
          
          <Link href="/about" className="block w-full">
            <Button 
              variant="ghost" 
              className="w-full justify-start text-white hover:bg-white/10"
            >
              <Info className="mr-2 h-4 w-4" />
              About Us
            </Button>
          </Link>

          {/* Legal Documents */}
          <div className="pt-4 mt-4 border-t border-white/10">
            <p className="text-xs text-white/60 mb-2 px-2">Legal & Policies</p>
            
            <Link href="/terms" className="block w-full">
              <Button 
                variant="ghost"
                className="w-full justify-start text-white hover:bg-white/10"
              >
                <File className="mr-2 h-4 w-4" />
                Terms of Use
              </Button>
            </Link>

            <Link href="/privacy-policy" className="block w-full">
              <Button 
                variant="ghost"
                className="w-full justify-start text-white hover:bg-white/10"
              >
                <File className="mr-2 h-4 w-4" />
                Privacy Policy
              </Button>
            </Link>

            <Link href="/disclaimer" className="block w-full">
              <Button 
                variant="ghost"
                className="w-full justify-start text-white hover:bg-white/10"
              >
                <File className="mr-2 h-4 w-4" />
                Disclaimer
              </Button>
            </Link>


          </div>
        </nav>
        
        {/* Footer Section */}
        <div className="absolute bottom-4 left-0 right-0 px-4 text-white/60 text-xs">
          <p>Image-Text Studio v2.0 - Free Edition</p>
          <p>© 2025 Image-Text Studio. Crafted with ❤️ by Bhanu</p>
        </div>
      </div>
    </>
  )
} 