export function alphamask(image: any, configuration: any): Promise<any>;
export function applySegmentationMask(image: any, mask: any, config: any): Promise<any>;
export function preload(configuration: any): Promise<void>;
export function removeBackground(image: any, configuration: any): Promise<any>;
export function removeForeground(image: any, configuration: any): Promise<any>;
export function segmentForeground(image: any, configuration: any): Promise<any>;
//# sourceMappingURL=index.d.mts.map