/*!
 * ONNX Runtime Web v1.21.0-dev.20250206-d981b153d3
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */ import { createRequire } from "module";
const require = createRequire(import.meta.url);
var ue = Object.defineProperty;
var pt = Object.getOwnPropertyDescriptor;
var mt = Object.getOwnPropertyNames;
var gt = Object.prototype.hasOwnProperty;
var ce = ((e)=>typeof require < "u" ? require : typeof Proxy < "u" ? new Proxy(e, {
        get: (t, n)=>(typeof require < "u" ? require : t)[n]
    }) : e)(function(e) {
    if (typeof require < "u") return require.apply(this, arguments);
    throw Error('Dynamic require of "' + e + '" is not supported');
});
var A = (e, t)=>()=>(e && (t = e(e = 0)), t);
var bt = (e, t)=>{
    for(var n in t)ue(e, n, {
        get: t[n],
        enumerable: !0
    });
}, yt = (e, t, n, s)=>{
    if (t && typeof t == "object" || typeof t == "function") for (let r of mt(t))!gt.call(e, r) && r !== n && ue(e, r, {
        get: ()=>t[r],
        enumerable: !(s = pt(t, r)) || s.enumerable
    });
    return e;
};
var wt = (e)=>yt(ue({}, "__esModule", {
        value: !0
    }), e);
var U, Q = A(()=>{
    "use strict";
    U = !!(typeof process < "u" && process.versions && process.versions.node);
});
var Pe, ht, k, xe, Te, St, Et, Ot, vt, Le, Ie, le = A(()=>{
    "use strict";
    Q();
    Pe = U || typeof location > "u" ? void 0 : location.origin, ht = ()=>{
        if (!U) return import.meta.url?.startsWith("file:") ? new URL(new URL("ort.node.min.mjs", import.meta.url).href, Pe).href : import.meta.url;
    }, k = ht(), xe = ()=>{
        if (k && !k.startsWith("blob:")) return k.substring(0, k.lastIndexOf("/") + 1);
    }, Te = (e, t)=>{
        try {
            let n = t ?? k;
            return (n ? new URL(e, n) : new URL(e)).origin === Pe;
        } catch  {
            return !1;
        }
    }, St = (e, t)=>{
        let n = t ?? k;
        try {
            return (n ? new URL(e, n) : new URL(e)).href;
        } catch  {
            return;
        }
    }, Et = (e, t)=>`${t ?? "./"}${e}`, Ot = async (e)=>{
        let n = await (await fetch(e, {
            credentials: "same-origin"
        })).blob();
        return URL.createObjectURL(n);
    }, vt = async (e)=>(await import(/*webpackIgnore:true*/ e)).default, Le = void 0, Ie = async (e, t, n)=>{
        if (!e && !t && Le && k && Te(k)) return [
            void 0,
            Le
        ];
        {
            let s = "ort-wasm-simd-threaded.mjs", r = e ?? St(s, t), i = !U && n && r && !Te(r, t), o = i ? await Ot(r) : r ?? Et(s, t);
            return [
                i ? o : void 0,
                await vt(o)
            ];
        }
    };
});
var fe, de, ee, _e, Tt, Lt, Ae, S, j = A(()=>{
    "use strict";
    le();
    de = !1, ee = !1, _e = !1, Tt = ()=>{
        if (typeof SharedArrayBuffer > "u") return !1;
        try {
            return typeof MessageChannel < "u" && new MessageChannel().port1.postMessage(new SharedArrayBuffer(1)), WebAssembly.validate(new Uint8Array([
                0,
                97,
                115,
                109,
                1,
                0,
                0,
                0,
                1,
                4,
                1,
                96,
                0,
                0,
                3,
                2,
                1,
                0,
                5,
                4,
                1,
                3,
                1,
                1,
                10,
                11,
                1,
                9,
                0,
                65,
                0,
                254,
                16,
                2,
                0,
                26,
                11
            ]));
        } catch  {
            return !1;
        }
    }, Lt = ()=>{
        try {
            return WebAssembly.validate(new Uint8Array([
                0,
                97,
                115,
                109,
                1,
                0,
                0,
                0,
                1,
                4,
                1,
                96,
                0,
                0,
                3,
                2,
                1,
                0,
                10,
                30,
                1,
                28,
                0,
                65,
                0,
                253,
                15,
                253,
                12,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                253,
                186,
                1,
                26,
                11
            ]));
        } catch  {
            return !1;
        }
    }, Ae = async (e)=>{
        if (de) return Promise.resolve();
        if (ee) throw new Error("multiple calls to 'initializeWebAssembly()' detected.");
        if (_e) throw new Error("previous call to 'initializeWebAssembly()' failed.");
        ee = !0;
        let t = e.initTimeout, n = e.numThreads;
        if (!Lt()) throw new Error("WebAssembly SIMD is not supported in the current environment.");
        let s = Tt();
        n > 1 && !s && (typeof self < "u" && !self.crossOriginIsolated && console.warn("env.wasm.numThreads is set to " + n + ", but this will not work unless you enable crossOriginIsolated mode. See https://web.dev/cross-origin-isolation-guide/ for more info."), console.warn("WebAssembly multi-threading is not supported in the current environment. Falling back to single-threading."), e.numThreads = n = 1);
        let r = e.wasmPaths, i = typeof r == "string" ? r : void 0, o = r?.mjs, a = o?.href ?? o, c = r?.wasm, d = c?.href ?? c, w = e.wasmBinary, [l, u] = await Ie(a, i, n > 1), b = !1, v = [];
        if (t > 0 && v.push(new Promise((f)=>{
            setTimeout(()=>{
                b = !0, f();
            }, t);
        })), v.push(new Promise((f, y)=>{
            let P = {
                numThreads: n
            };
            if (w) P.wasmBinary = w;
            else if (d || i) P.locateFile = (p)=>d ?? i + p;
            else if (a && a.indexOf("blob:") !== 0) P.locateFile = (p)=>new URL(p, a).href;
            else if (l) {
                let p = xe();
                p && (P.locateFile = (h)=>p + h);
            }
            u(P).then((p)=>{
                ee = !1, de = !0, fe = p, f(), l && URL.revokeObjectURL(l);
            }, (p)=>{
                ee = !1, _e = !0, y(p);
            });
        })), await Promise.race(v), b) throw new Error(`WebAssembly backend initializing failed due to timeout: ${t}ms`);
    }, S = ()=>{
        if (de && fe) return fe;
        throw new Error("WebAssembly is not initialized yet.");
    };
});
var E, G, g, te = A(()=>{
    "use strict";
    j();
    E = (e, t)=>{
        let n = S(), s = n.lengthBytesUTF8(e) + 1, r = n._malloc(s);
        return n.stringToUTF8(e, r, s), t.push(r), r;
    }, G = (e, t, n, s)=>{
        if (typeof e == "object" && e !== null) {
            if (n.has(e)) throw new Error("Circular reference in options");
            n.add(e);
        }
        Object.entries(e).forEach(([r, i])=>{
            let o = t ? t + r : r;
            if (typeof i == "object") G(i, o + ".", n, s);
            else if (typeof i == "string" || typeof i == "number") s(o, i.toString());
            else if (typeof i == "boolean") s(o, i ? "1" : "0");
            else throw new Error(`Can't handle extra config type: ${typeof i}`);
        });
    }, g = (e)=>{
        let t = S(), n = t.stackSave();
        try {
            let s = t.PTR_SIZE, r = t.stackAlloc(2 * s);
            t._OrtGetLastError(r, r + s);
            let i = Number(t.getValue(r, s === 4 ? "i32" : "i64")), o = t.getValue(r + s, "*"), a = o ? t.UTF8ToString(o) : "";
            throw new Error(`${e} ERROR_CODE: ${i}, ERROR_MESSAGE: ${a}`);
        } finally{
            t.stackRestore(n);
        }
    };
});
var Ce, Be = A(()=>{
    "use strict";
    j();
    te();
    Ce = (e)=>{
        let t = S(), n = 0, s = [], r = e || {};
        try {
            if (e?.logSeverityLevel === void 0) r.logSeverityLevel = 2;
            else if (typeof e.logSeverityLevel != "number" || !Number.isInteger(e.logSeverityLevel) || e.logSeverityLevel < 0 || e.logSeverityLevel > 4) throw new Error(`log serverity level is not valid: ${e.logSeverityLevel}`);
            if (e?.logVerbosityLevel === void 0) r.logVerbosityLevel = 0;
            else if (typeof e.logVerbosityLevel != "number" || !Number.isInteger(e.logVerbosityLevel)) throw new Error(`log verbosity level is not valid: ${e.logVerbosityLevel}`);
            e?.terminate === void 0 && (r.terminate = !1);
            let i = 0;
            return e?.tag !== void 0 && (i = E(e.tag, s)), n = t._OrtCreateRunOptions(r.logSeverityLevel, r.logVerbosityLevel, !!r.terminate, i), n === 0 && g("Can't create run options."), e?.extra !== void 0 && G(e.extra, "", new WeakSet, (o, a)=>{
                let c = E(o, s), d = E(a, s);
                t._OrtAddRunConfigEntry(n, c, d) !== 0 && g(`Can't set a run config entry: ${o} - ${a}.`);
            }), [
                n,
                s
            ];
        } catch (i) {
            throw n !== 0 && t._OrtReleaseRunOptions(n), s.forEach((o)=>t._free(o)), i;
        }
    };
});
var Pt, xt, It, _t, Me, Ue = A(()=>{
    "use strict";
    j();
    te();
    Pt = (e)=>{
        switch(e){
            case "disabled":
                return 0;
            case "basic":
                return 1;
            case "extended":
                return 2;
            case "all":
                return 99;
            default:
                throw new Error(`unsupported graph optimization level: ${e}`);
        }
    }, xt = (e)=>{
        switch(e){
            case "sequential":
                return 0;
            case "parallel":
                return 1;
            default:
                throw new Error(`unsupported execution mode: ${e}`);
        }
    }, It = (e)=>{
        e.extra || (e.extra = {}), e.extra.session || (e.extra.session = {});
        let t = e.extra.session;
        t.use_ort_model_bytes_directly || (t.use_ort_model_bytes_directly = "1"), e.executionProviders && e.executionProviders.some((n)=>(typeof n == "string" ? n : n.name) === "webgpu") && (e.enableMemPattern = !1);
    }, _t = (e, t, n)=>{
        for (let s of t){
            let r = typeof s == "string" ? s : s.name;
            switch(r){
                case "webnn":
                    if (r = "WEBNN", typeof s != "string") {
                        let a = s?.deviceType;
                        if (a) {
                            let c = E("deviceType", n), d = E(a, n);
                            S()._OrtAddSessionConfigEntry(e, c, d) !== 0 && g(`Can't set a session config entry: 'deviceType' - ${a}.`);
                        }
                    }
                    break;
                case "webgpu":
                    if (r = "JS", typeof s != "string") {
                        let o = s;
                        if (o?.preferredLayout) {
                            if (o.preferredLayout !== "NCHW" && o.preferredLayout !== "NHWC") throw new Error(`preferredLayout must be either 'NCHW' or 'NHWC': ${o.preferredLayout}`);
                            let a = E("preferredLayout", n), c = E(o.preferredLayout, n);
                            S()._OrtAddSessionConfigEntry(e, a, c) !== 0 && g(`Can't set a session config entry: 'preferredLayout' - ${o.preferredLayout}.`);
                        }
                    }
                    break;
                case "wasm":
                case "cpu":
                    continue;
                default:
                    throw new Error(`not supported execution provider: ${r}`);
            }
            let i = E(r, n);
            S()._OrtAppendExecutionProvider(e, i) !== 0 && g(`Can't append execution provider: ${r}.`);
        }
    }, Me = (e)=>{
        let t = S(), n = 0, s = [], r = e || {};
        It(r);
        try {
            let i = Pt(r.graphOptimizationLevel ?? "all"), o = xt(r.executionMode ?? "sequential"), a = typeof r.logId == "string" ? E(r.logId, s) : 0, c = r.logSeverityLevel ?? 2;
            if (!Number.isInteger(c) || c < 0 || c > 4) throw new Error(`log serverity level is not valid: ${c}`);
            let d = r.logVerbosityLevel ?? 0;
            if (!Number.isInteger(d) || d < 0 || d > 4) throw new Error(`log verbosity level is not valid: ${d}`);
            let w = typeof r.optimizedModelFilePath == "string" ? E(r.optimizedModelFilePath, s) : 0;
            if (n = t._OrtCreateSessionOptions(i, !!r.enableCpuMemArena, !!r.enableMemPattern, o, !!r.enableProfiling, 0, a, c, d, w), n === 0 && g("Can't create session options."), r.executionProviders && _t(n, r.executionProviders, s), r.enableGraphCapture !== void 0) {
                if (typeof r.enableGraphCapture != "boolean") throw new Error(`enableGraphCapture must be a boolean value: ${r.enableGraphCapture}`);
                let l = E("enableGraphCapture", s), u = E(r.enableGraphCapture.toString(), s);
                t._OrtAddSessionConfigEntry(n, l, u) !== 0 && g(`Can't set a session config entry: 'enableGraphCapture' - ${r.enableGraphCapture}.`);
            }
            if (r.freeDimensionOverrides) for (let [l, u] of Object.entries(r.freeDimensionOverrides)){
                if (typeof l != "string") throw new Error(`free dimension override name must be a string: ${l}`);
                if (typeof u != "number" || !Number.isInteger(u) || u < 0) throw new Error(`free dimension override value must be a non-negative integer: ${u}`);
                let b = E(l, s);
                t._OrtAddFreeDimensionOverride(n, b, u) !== 0 && g(`Can't set a free dimension override: ${l} - ${u}.`);
            }
            return r.extra !== void 0 && G(r.extra, "", new WeakSet, (l, u)=>{
                let b = E(l, s), v = E(u, s);
                t._OrtAddSessionConfigEntry(n, b, v) !== 0 && g(`Can't set a session config entry: ${l} - ${u}.`);
            }), [
                n,
                s
            ];
        } catch (i) {
            throw n !== 0 && t._OrtReleaseSessionOptions(n) !== 0 && g("Can't release session options."), s.forEach((o)=>t._free(o)), i;
        }
    };
});
var H, ke, V, De, We, re, ne, Fe, pe = A(()=>{
    "use strict";
    H = (e)=>{
        switch(e){
            case "int8":
                return 3;
            case "uint8":
                return 2;
            case "bool":
                return 9;
            case "int16":
                return 5;
            case "uint16":
                return 4;
            case "int32":
                return 6;
            case "uint32":
                return 12;
            case "float16":
                return 10;
            case "float32":
                return 1;
            case "float64":
                return 11;
            case "string":
                return 8;
            case "int64":
                return 7;
            case "uint64":
                return 13;
            case "int4":
                return 22;
            case "uint4":
                return 21;
            default:
                throw new Error(`unsupported data type: ${e}`);
        }
    }, ke = (e)=>{
        switch(e){
            case 3:
                return "int8";
            case 2:
                return "uint8";
            case 9:
                return "bool";
            case 5:
                return "int16";
            case 4:
                return "uint16";
            case 6:
                return "int32";
            case 12:
                return "uint32";
            case 10:
                return "float16";
            case 1:
                return "float32";
            case 11:
                return "float64";
            case 8:
                return "string";
            case 7:
                return "int64";
            case 13:
                return "uint64";
            case 22:
                return "int4";
            case 21:
                return "uint4";
            default:
                throw new Error(`unsupported data type: ${e}`);
        }
    }, V = (e, t)=>{
        let n = [
            -1,
            4,
            1,
            1,
            2,
            2,
            4,
            8,
            -1,
            1,
            2,
            8,
            4,
            8,
            -1,
            -1,
            -1,
            -1,
            -1,
            -1,
            -1,
            .5,
            .5
        ][e], s = typeof t == "number" ? t : t.reduce((r, i)=>r * i, 1);
        return n > 0 ? Math.ceil(s * n) : void 0;
    }, De = (e)=>{
        switch(e){
            case "float16":
                return typeof Float16Array < "u" && Float16Array.from ? Float16Array : Uint16Array;
            case "float32":
                return Float32Array;
            case "uint8":
                return Uint8Array;
            case "int8":
                return Int8Array;
            case "uint16":
                return Uint16Array;
            case "int16":
                return Int16Array;
            case "int32":
                return Int32Array;
            case "bool":
                return Uint8Array;
            case "float64":
                return Float64Array;
            case "uint32":
                return Uint32Array;
            case "int64":
                return BigInt64Array;
            case "uint64":
                return BigUint64Array;
            default:
                throw new Error(`unsupported type: ${e}`);
        }
    }, We = (e)=>{
        switch(e){
            case "verbose":
                return 0;
            case "info":
                return 1;
            case "warning":
                return 2;
            case "error":
                return 3;
            case "fatal":
                return 4;
            default:
                throw new Error(`unsupported logging level: ${e}`);
        }
    }, re = (e)=>e === "float32" || e === "float16" || e === "int32" || e === "int64" || e === "uint32" || e === "uint8" || e === "bool" || e === "uint4" || e === "int4", ne = (e)=>e === "float32" || e === "float16" || e === "int32" || e === "int64" || e === "uint32" || e === "uint64" || e === "int8" || e === "uint8" || e === "bool" || e === "uint4" || e === "int4", Fe = (e)=>{
        switch(e){
            case "none":
                return 0;
            case "cpu":
                return 1;
            case "cpu-pinned":
                return 2;
            case "texture":
                return 3;
            case "gpu-buffer":
                return 4;
            case "ml-tensor":
                return 5;
            default:
                throw new Error(`unsupported data location: ${e}`);
        }
    };
});
var q, me = A(()=>{
    "use strict";
    Q();
    q = async (e)=>{
        if (typeof e == "string") if (U) try {
            let { readFile: t } = ce("node:fs/promises");
            return new Uint8Array(await t(e));
        } catch (t) {
            if (t.code === "ERR_FS_FILE_TOO_LARGE") {
                let { createReadStream: n } = ce("node:fs"), s = n(e), r = [];
                for await (let i of s)r.push(i);
                return new Uint8Array(Buffer.concat(r));
            }
            throw t;
        }
        else {
            let t = await fetch(e);
            if (!t.ok) throw new Error(`failed to load external data file: ${e}`);
            let n = t.headers.get("Content-Length"), s = n ? parseInt(n, 10) : 0;
            if (s < 1073741824) return new Uint8Array(await t.arrayBuffer());
            {
                if (!t.body) throw new Error(`failed to load external data file: ${e}, no response body.`);
                let r = t.body.getReader(), i;
                try {
                    i = new ArrayBuffer(s);
                } catch (a) {
                    if (a instanceof RangeError) {
                        let c = Math.ceil(s / 65536);
                        i = new WebAssembly.Memory({
                            initial: c,
                            maximum: c
                        }).buffer;
                    } else throw a;
                }
                let o = 0;
                for(;;){
                    let { done: a, value: c } = await r.read();
                    if (a) break;
                    let d = c.byteLength;
                    new Uint8Array(i, o, d).set(c), o += d;
                }
                return new Uint8Array(i, 0, s);
            }
        }
        else return e instanceof Blob ? new Uint8Array(await e.arrayBuffer()) : e instanceof Uint8Array ? e : new Uint8Array(e);
    };
});
var At, Ne, je, $, Ct, ge, $e, ze, Re, Ge, He, Ve = A(()=>{
    "use strict";
    Be();
    Ue();
    pe();
    j();
    te();
    me();
    At = (e, t)=>{
        S()._OrtInit(e, t) !== 0 && g("Can't initialize onnxruntime.");
    }, Ne = async (e)=>{
        At(e.wasm.numThreads, We(e.logLevel));
    }, je = async (e, t)=>{}, $ = new Map, Ct = (e)=>{
        let t = S(), n = t.stackSave();
        try {
            let s = t.PTR_SIZE, r = t.stackAlloc(2 * s);
            t._OrtGetInputOutputCount(e, r, r + s) !== 0 && g("Can't get session input/output count.");
            let o = s === 4 ? "i32" : "i64";
            return [
                Number(t.getValue(r, o)),
                Number(t.getValue(r + s, o))
            ];
        } finally{
            t.stackRestore(n);
        }
    }, ge = (e)=>{
        let t = S(), n = t._malloc(e.byteLength);
        if (n === 0) throw new Error(`Can't create a session. failed to allocate a buffer of size ${e.byteLength}.`);
        return t.HEAPU8.set(e, n), [
            n,
            e.byteLength
        ];
    }, $e = async (e, t)=>{
        let n, s, r = S();
        Array.isArray(e) ? [n, s] = e : e.buffer === r.HEAPU8.buffer ? [n, s] = [
            e.byteOffset,
            e.byteLength
        ] : [n, s] = ge(e);
        let i = 0, o = 0, a = 0, c = [], d = [], w = [];
        try {
            if ([o, c] = Me(t), t?.externalData && r.mountExternalData) {
                let p = [];
                for (let h of t.externalData){
                    let x = typeof h == "string" ? h : h.path;
                    p.push(q(typeof h == "string" ? h : h.data).then((M)=>{
                        r.mountExternalData(x, M);
                    }));
                }
                await Promise.all(p);
            }
            for (let p of t?.executionProviders ?? [])if ((typeof p == "string" ? p : p.name) === "webnn") {
                if (r.shouldTransferToMLTensor = !1, typeof p != "string") {
                    let x = p, M = x?.context, J = x?.gpuDevice, Y = x?.deviceType, X = x?.powerPreference;
                    M ? r.currentContext = M : J ? r.currentContext = await r.jsepCreateMLContext(J) : r.currentContext = await r.jsepCreateMLContext({
                        deviceType: Y,
                        powerPreference: X
                    });
                } else r.currentContext = await r.jsepCreateMLContext();
                break;
            }
            i = await r._OrtCreateSession(n, s, o), i === 0 && g("Can't create a session."), r.jsepOnCreateSession?.(), r.currentContext && (r.jsepRegisterMLContext(i, r.currentContext), r.currentContext = void 0, r.shouldTransferToMLTensor = !0);
            let [l, u] = Ct(i), b = !!t?.enableGraphCapture, v = [], f = [], y = [];
            for(let p = 0; p < l; p++){
                let h = r._OrtGetInputName(i, p);
                h === 0 && g("Can't get an input name."), d.push(h), v.push(r.UTF8ToString(h));
            }
            for(let p = 0; p < u; p++){
                let h = r._OrtGetOutputName(i, p);
                h === 0 && g("Can't get an output name."), w.push(h);
                let x = r.UTF8ToString(h);
                f.push(x);
            }
            let P = null;
            return $.set(i, [
                i,
                d,
                w,
                P,
                b,
                !1
            ]), [
                i,
                v,
                f
            ];
        } catch (l) {
            throw d.forEach((u)=>r._OrtFree(u)), w.forEach((u)=>r._OrtFree(u)), a !== 0 && r._OrtReleaseBinding(a) !== 0 && g("Can't release IO binding."), i !== 0 && r._OrtReleaseSession(i) !== 0 && g("Can't release session."), l;
        } finally{
            r._free(n), o !== 0 && r._OrtReleaseSessionOptions(o) !== 0 && g("Can't release session options."), c.forEach((l)=>r._free(l)), r.unmountExternalData?.();
        }
    }, ze = (e)=>{
        let t = S(), n = $.get(e);
        if (!n) throw new Error(`cannot release session. invalid session id: ${e}`);
        let [s, r, i, o, a] = n;
        o && (a && t._OrtClearBoundOutputs(o.handle) !== 0 && g("Can't clear bound outputs."), t._OrtReleaseBinding(o.handle) !== 0 && g("Can't release IO binding.")), t.jsepOnReleaseSession?.(e), r.forEach((c)=>t._OrtFree(c)), i.forEach((c)=>t._OrtFree(c)), t._OrtReleaseSession(s) !== 0 && g("Can't release session."), $.delete(e);
    }, Re = (e, t, n, s, r, i = !1)=>{
        if (!e) {
            t.push(0);
            return;
        }
        let o = S(), a = o.PTR_SIZE, c = e[0], d = e[1], w = e[3], l, u;
        if (c === "string" && (w === "gpu-buffer" || w === "ml-tensor")) throw new Error("String tensor is not supported on GPU.");
        if (i && w !== "gpu-buffer") throw new Error(`External buffer must be provided for input/output index ${r} when enableGraphCapture is true.`);
        if (w === "gpu-buffer") {
            let f = e[2].gpuBuffer;
            u = V(H(c), d);
            let y = o.jsepRegisterBuffer;
            if (!y) throw new Error('Tensor location "gpu-buffer" is not supported without using WebGPU.');
            l = y(s, r, f, u);
        } else if (w === "ml-tensor") {
            let f = e[2].mlTensor;
            u = V(H(c), d);
            let y = o.jsepRegisterMLTensor;
            if (!y) throw new Error('Tensor location "ml-tensor" is not supported without using WebNN.');
            l = y(f, H(c), d);
        } else {
            let f = e[2];
            if (Array.isArray(f)) {
                u = a * f.length, l = o._malloc(u), n.push(l);
                for(let y = 0; y < f.length; y++){
                    if (typeof f[y] != "string") throw new TypeError(`tensor data at index ${y} is not a string`);
                    o.setValue(l + y * a, E(f[y], n), "*");
                }
            } else u = f.byteLength, l = o._malloc(u), n.push(l), o.HEAPU8.set(new Uint8Array(f.buffer, f.byteOffset, u), l);
        }
        let b = o.stackSave(), v = o.stackAlloc(4 * d.length);
        try {
            d.forEach((y, P)=>o.setValue(v + P * a, y, a === 4 ? "i32" : "i64"));
            let f = o._OrtCreateTensor(H(c), l, u, v, d.length, Fe(w));
            f === 0 && g(`Can't create tensor for input/output. session=${s}, index=${r}.`), t.push(f);
        } finally{
            o.stackRestore(b);
        }
    }, Ge = async (e, t, n, s, r, i)=>{
        let o = S(), a = o.PTR_SIZE, c = $.get(e);
        if (!c) throw new Error(`cannot run inference. invalid session id: ${e}`);
        let d = c[0], w = c[1], l = c[2], u = c[3], b = c[4], v = c[5], f = t.length, y = s.length, P = 0, p = [], h = [], x = [], M = [], J = o.stackSave(), Y = o.stackAlloc(f * a), X = o.stackAlloc(f * a), ie = o.stackAlloc(y * a), Se = o.stackAlloc(y * a);
        try {
            o.jsepOnRunStart?.(d), [P, p] = Ce(i);
            for(let m = 0; m < f; m++)Re(n[m], h, M, e, t[m], b);
            for(let m = 0; m < y; m++)Re(r[m], x, M, e, f + s[m], b);
            for(let m = 0; m < f; m++)o.setValue(Y + m * a, h[m], "*"), o.setValue(X + m * a, w[t[m]], "*");
            for(let m = 0; m < y; m++)o.setValue(ie + m * a, x[m], "*"), o.setValue(Se + m * a, l[s[m]], "*");
            let C;
            C = await o._OrtRun(d, X, Y, f, Se, y, ie, P), C !== 0 && g("failed to call OrtRun().");
            let R = [];
            for(let m = 0; m < y; m++){
                let N = Number(o.getValue(ie + m * a, "*"));
                if (N === x[m]) {
                    R.push(r[m]);
                    continue;
                }
                let Ee = o.stackSave(), B = o.stackAlloc(4 * a), z = !1, T, I = 0;
                try {
                    o._OrtGetTensorData(N, B, B + a, B + 2 * a, B + 3 * a) !== 0 && g(`Can't access output tensor data on index ${m}.`);
                    let ae = a === 4 ? "i32" : "i64", Z = Number(o.getValue(B, ae));
                    I = o.getValue(B + a, "*");
                    let Oe = o.getValue(B + a * 2, "*"), lt = Number(o.getValue(B + a * 3, ae)), D = [];
                    for(let L = 0; L < lt; L++)D.push(Number(o.getValue(Oe + L * a, ae)));
                    o._OrtFree(Oe) !== 0 && g("Can't free memory for tensor dims.");
                    let W = D.reduce((L, O)=>L * O, 1);
                    T = ke(Z);
                    let K = u?.outputPreferredLocations[s[m]];
                    if (T === "string") {
                        if (K === "gpu-buffer" || K === "ml-tensor") throw new Error("String tensor is not supported on GPU.");
                        let L = [];
                        for(let O = 0; O < W; O++){
                            let F = o.getValue(I + O * a, "*"), ft = o.getValue(I + (O + 1) * a, "*"), dt = O === W - 1 ? void 0 : ft - F;
                            L.push(o.UTF8ToString(F, dt));
                        }
                        R.push([
                            T,
                            D,
                            L,
                            "cpu"
                        ]);
                    } else if (K === "gpu-buffer" && W > 0) {
                        let L = o.jsepGetBuffer;
                        if (!L) throw new Error('preferredLocation "gpu-buffer" is not supported without using WebGPU.');
                        let O = L(I), F = V(Z, W);
                        if (F === void 0 || !re(T)) throw new Error(`Unsupported data type: ${T}`);
                        z = !0, R.push([
                            T,
                            D,
                            {
                                gpuBuffer: O,
                                download: o.jsepCreateDownloader(O, F, T),
                                dispose: ()=>{
                                    o._OrtReleaseTensor(N) !== 0 && g("Can't release tensor.");
                                }
                            },
                            "gpu-buffer"
                        ]);
                    } else if (K === "ml-tensor" && W > 0) {
                        let L = o.jsepEnsureTensor;
                        if (!L) throw new Error('preferredLocation "ml-tensor" is not supported without using WebNN.');
                        if (V(Z, W) === void 0 || !ne(T)) throw new Error(`Unsupported data type: ${T}`);
                        let F = await L(I, Z, D, !1);
                        z = !0, R.push([
                            T,
                            D,
                            {
                                mlTensor: F,
                                download: o.jsepCreateMLTensorDownloader(I, T),
                                dispose: ()=>{
                                    o.jsepReleaseTensorId(I), o._OrtReleaseTensor(N);
                                }
                            },
                            "ml-tensor"
                        ]);
                    } else {
                        let L = De(T), O = new L(W);
                        new Uint8Array(O.buffer, O.byteOffset, O.byteLength).set(o.HEAPU8.subarray(I, I + O.byteLength)), R.push([
                            T,
                            D,
                            O,
                            "cpu"
                        ]);
                    }
                } finally{
                    o.stackRestore(Ee), T === "string" && I && o._free(I), z || o._OrtReleaseTensor(N);
                }
            }
            return u && !b && (o._OrtClearBoundOutputs(u.handle) !== 0 && g("Can't clear bound outputs."), $.set(e, [
                d,
                w,
                l,
                u,
                b,
                !1
            ])), R;
        } finally{
            o.stackRestore(J), h.forEach((C)=>o._OrtReleaseTensor(C)), x.forEach((C)=>o._OrtReleaseTensor(C)), M.forEach((C)=>o._free(C)), P !== 0 && o._OrtReleaseRunOptions(P), p.forEach((C)=>o._free(C));
        }
    }, He = (e)=>{
        let t = S(), n = $.get(e);
        if (!n) throw new Error("invalid session id");
        let s = n[0], r = t._OrtEndProfiling(s);
        r === 0 && g("Can't get an profile file name."), t._OrtFree(r);
    };
});
import { env as ye } from "onnxruntime-common";
var be, qe, Je, Ye, Xe, Ze, Ke, Qe, et, tt, we = A(()=>{
    "use strict";
    Ve();
    j();
    le();
    be = !1, qe = !1, Je = !1, Ye = async ()=>{
        if (!qe) {
            if (be) throw new Error("multiple calls to 'initWasm()' detected.");
            if (Je) throw new Error("previous call to 'initWasm()' failed.");
            be = !0;
            try {
                await Ae(ye.wasm), await Ne(ye), qe = !0;
            } catch (e) {
                throw Je = !0, e;
            } finally{
                be = !1;
            }
        }
    }, Xe = async (e)=>{
        await je(ye, e);
    }, Ze = async (e)=>ge(e), Ke = async (e, t)=>$e(e, t), Qe = async (e)=>{
        ze(e);
    }, et = async (e, t, n, s, r, i)=>Ge(e, t, n, s, r, i), tt = async (e)=>{
        He(e);
    };
});
import { Tensor as he, TRACE_FUNC_BEGIN as rt, TRACE_FUNC_END as nt } from "onnxruntime-common";
var ot, Mt, oe, st = A(()=>{
    "use strict";
    we();
    pe();
    Q();
    me();
    ot = (e, t)=>{
        switch(e.location){
            case "cpu":
                return [
                    e.type,
                    e.dims,
                    e.data,
                    "cpu"
                ];
            case "gpu-buffer":
                return [
                    e.type,
                    e.dims,
                    {
                        gpuBuffer: e.gpuBuffer
                    },
                    "gpu-buffer"
                ];
            case "ml-tensor":
                return [
                    e.type,
                    e.dims,
                    {
                        mlTensor: e.mlTensor
                    },
                    "ml-tensor"
                ];
            default:
                throw new Error(`invalid data location: ${e.location} for ${t()}`);
        }
    }, Mt = (e)=>{
        switch(e[3]){
            case "cpu":
                return new he(e[0], e[2], e[1]);
            case "gpu-buffer":
                {
                    let t = e[0];
                    if (!re(t)) throw new Error(`not supported data type: ${t} for deserializing GPU tensor`);
                    let { gpuBuffer: n, download: s, dispose: r } = e[2];
                    return he.fromGpuBuffer(n, {
                        dataType: t,
                        dims: e[1],
                        download: s,
                        dispose: r
                    });
                }
            case "ml-tensor":
                {
                    let t = e[0];
                    if (!ne(t)) throw new Error(`not supported data type: ${t} for deserializing MLTensor tensor`);
                    let { mlTensor: n, download: s, dispose: r } = e[2];
                    return he.fromMLTensor(n, {
                        dataType: t,
                        dims: e[1],
                        download: s,
                        dispose: r
                    });
                }
            default:
                throw new Error(`invalid data location: ${e[3]}`);
        }
    }, oe = class {
        async fetchModelAndCopyToWasmMemory(t) {
            return Ze(await q(t));
        }
        async loadModel(t, n) {
            rt();
            let s;
            typeof t == "string" ? U ? s = await q(t) : s = await this.fetchModelAndCopyToWasmMemory(t) : s = t, [this.sessionId, this.inputNames, this.outputNames] = await Ke(s, n), nt();
        }
        async dispose() {
            return Qe(this.sessionId);
        }
        async run(t, n, s) {
            rt();
            let r = [], i = [];
            Object.entries(t).forEach((u)=>{
                let b = u[0], v = u[1], f = this.inputNames.indexOf(b);
                if (f === -1) throw new Error(`invalid input '${b}'`);
                r.push(v), i.push(f);
            });
            let o = [], a = [];
            Object.entries(n).forEach((u)=>{
                let b = u[0], v = u[1], f = this.outputNames.indexOf(b);
                if (f === -1) throw new Error(`invalid output '${b}'`);
                o.push(v), a.push(f);
            });
            let c = r.map((u, b)=>ot(u, ()=>`input "${this.inputNames[i[b]]}"`)), d = o.map((u, b)=>u ? ot(u, ()=>`output "${this.outputNames[a[b]]}"`) : null), w = await et(this.sessionId, i, c, a, d, s), l = {};
            for(let u = 0; u < w.length; u++)l[this.outputNames[a[u]]] = o[u] ?? Mt(w[u]);
            return nt(), l;
        }
        startProfiling() {}
        endProfiling() {
            tt(this.sessionId);
        }
    };
});
var at = {};
bt(at, {
    OnnxruntimeWebAssemblyBackend: ()=>se,
    initializeFlags: ()=>it,
    wasmBackend: ()=>Ut
});
import { env as _ } from "onnxruntime-common";
var it, se, Ut, ut = A(()=>{
    "use strict";
    we();
    st();
    it = ()=>{
        if ((typeof _.wasm.initTimeout != "number" || _.wasm.initTimeout < 0) && (_.wasm.initTimeout = 0), _.wasm.simd === !1 && console.warn('Deprecated property "env.wasm.simd" is set to false. non-SIMD build is no longer provided, and this setting will be ignored.'), typeof _.wasm.proxy != "boolean" && (_.wasm.proxy = !1), typeof _.wasm.trace != "boolean" && (_.wasm.trace = !1), typeof _.wasm.numThreads != "number" || !Number.isInteger(_.wasm.numThreads) || _.wasm.numThreads <= 0) if (typeof self < "u" && !self.crossOriginIsolated) _.wasm.numThreads = 1;
        else {
            let e = typeof navigator > "u" ? ce("node:os").cpus().length : navigator.hardwareConcurrency;
            _.wasm.numThreads = Math.min(4, Math.ceil((e || 1) / 2));
        }
    }, se = class {
        async init(t) {
            it(), await Ye(), await Xe(t);
        }
        async createInferenceSessionHandler(t, n) {
            let s = new oe;
            return await s.loadModel(t, n), Promise.resolve(s);
        }
    }, Ut = new se;
});
export * from "onnxruntime-common";
import * as kt from "onnxruntime-common";
import { registerBackend as ct, env as Dt } from "onnxruntime-common";
var ve = "1.21.0-dev.20250206-d981b153d3";
var Cr = kt;
{
    let e = (ut(), wt(at)).wasmBackend;
    ct("cpu", e, 10), ct("wasm", e, 10);
}Object.defineProperty(Dt.versions, "web", {
    value: ve,
    enumerable: !0
});
export { Cr as default }; //# sourceMappingURL=ort.node.min.mjs.map
