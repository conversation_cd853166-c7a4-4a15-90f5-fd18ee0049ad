{"name": "@prisma/engines", "version": "6.5.0", "description": "This package is intended for Prisma's internal use", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/engines"}, "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "devDependencies": {"@swc/core": "1.11.5", "@swc/jest": "0.2.37", "@types/jest": "29.5.14", "@types/node": "18.19.76", "execa": "5.1.1", "jest": "29.7.0", "typescript": "5.4.5"}, "dependencies": {"@prisma/engines-version": "6.5.0-73.173f8d54f8d52e692c7e27e72a88314ec7aeff60", "@prisma/debug": "6.5.0", "@prisma/fetch-engine": "6.5.0", "@prisma/get-platform": "6.5.0"}, "files": ["dist", "download", "scripts"], "sideEffects": false, "scripts": {"dev": "DEV=true tsx helpers/build.ts", "build": "tsx helpers/build.ts", "test": "jest --passWithNoTests", "postinstall": "node scripts/postinstall.js"}}