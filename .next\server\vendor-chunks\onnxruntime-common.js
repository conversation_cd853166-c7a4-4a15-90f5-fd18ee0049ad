"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/onnxruntime-common";
exports.ids = ["vendor-chunks/onnxruntime-common"];
exports.modules = {

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/backend-impl.js":
/*!******************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/backend-impl.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerBackend: () => (/* binding */ registerBackend),\n/* harmony export */   resolveBackendAndExecutionProviders: () => (/* binding */ resolveBackendAndExecutionProviders)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nconst backends = new Map();\nconst backendsSortedByPriority = [];\n/**\n * Register a backend.\n *\n * @param name - the name as a key to lookup as an execution provider.\n * @param backend - the backend object.\n * @param priority - an integer indicating the priority of the backend. Higher number means higher priority. if priority\n * < 0, it will be considered as a 'beta' version and will not be used as a fallback backend by default.\n *\n * @ignore\n */ const registerBackend = (name, backend, priority)=>{\n    if (backend && typeof backend.init === \"function\" && typeof backend.createInferenceSessionHandler === \"function\") {\n        const currentBackend = backends.get(name);\n        if (currentBackend === undefined) {\n            backends.set(name, {\n                backend,\n                priority\n            });\n        } else if (currentBackend.priority > priority) {\n            // same name is already registered with a higher priority. skip registeration.\n            return;\n        } else if (currentBackend.priority === priority) {\n            if (currentBackend.backend !== backend) {\n                throw new Error(`cannot register backend \"${name}\" using priority ${priority}`);\n            }\n        }\n        if (priority >= 0) {\n            const i = backendsSortedByPriority.indexOf(name);\n            if (i !== -1) {\n                backendsSortedByPriority.splice(i, 1);\n            }\n            for(let i = 0; i < backendsSortedByPriority.length; i++){\n                if (backends.get(backendsSortedByPriority[i]).priority <= priority) {\n                    backendsSortedByPriority.splice(i, 0, name);\n                    return;\n                }\n            }\n            backendsSortedByPriority.push(name);\n        }\n        return;\n    }\n    throw new TypeError(\"not a valid backend\");\n};\n/**\n * Try to resolve and initialize a backend.\n *\n * @param backendName - the name of the backend.\n * @returns the backend instance if resolved and initialized successfully, or an error message if failed.\n */ const tryResolveAndInitializeBackend = async (backendName)=>{\n    const backendInfo = backends.get(backendName);\n    if (!backendInfo) {\n        return \"backend not found.\";\n    }\n    if (backendInfo.initialized) {\n        return backendInfo.backend;\n    } else if (backendInfo.aborted) {\n        return backendInfo.error;\n    } else {\n        const isInitializing = !!backendInfo.initPromise;\n        try {\n            if (!isInitializing) {\n                backendInfo.initPromise = backendInfo.backend.init(backendName);\n            }\n            await backendInfo.initPromise;\n            backendInfo.initialized = true;\n            return backendInfo.backend;\n        } catch (e) {\n            if (!isInitializing) {\n                backendInfo.error = `${e}`;\n                backendInfo.aborted = true;\n            }\n            return backendInfo.error;\n        } finally{\n            delete backendInfo.initPromise;\n        }\n    }\n};\n/**\n * Resolve execution providers from the specific session options.\n *\n * @param options - the session options object.\n * @returns a promise that resolves to a tuple of an initialized backend instance and a session options object with\n * filtered EP list.\n *\n * @ignore\n */ const resolveBackendAndExecutionProviders = async (options)=>{\n    // extract backend hints from session options\n    const eps = options.executionProviders || [];\n    const backendHints = eps.map((i)=>typeof i === \"string\" ? i : i.name);\n    const backendNames = backendHints.length === 0 ? backendsSortedByPriority : backendHints;\n    // try to resolve and initialize all requested backends\n    let backend;\n    const errors = [];\n    const availableBackendNames = new Set();\n    for (const backendName of backendNames){\n        const resolveResult = await tryResolveAndInitializeBackend(backendName);\n        if (typeof resolveResult === \"string\") {\n            errors.push({\n                name: backendName,\n                err: resolveResult\n            });\n        } else {\n            if (!backend) {\n                backend = resolveResult;\n            }\n            if (backend === resolveResult) {\n                availableBackendNames.add(backendName);\n            }\n        }\n    }\n    // if no backend is available, throw error.\n    if (!backend) {\n        throw new Error(`no available backend found. ERR: ${errors.map((e)=>`[${e.name}] ${e.err}`).join(\", \")}`);\n    }\n    // for each explicitly requested backend, if it's not available, output warning message.\n    for (const { name, err } of errors){\n        if (backendHints.includes(name)) {\n            // eslint-disable-next-line no-console\n            console.warn(`removing requested execution provider \"${name}\" from session options because it is not available: ${err}`);\n        }\n    }\n    const filteredEps = eps.filter((i)=>availableBackendNames.has(typeof i === \"string\" ? i : i.name));\n    return [\n        backend,\n        new Proxy(options, {\n            get: (target, prop)=>{\n                if (prop === \"executionProviders\") {\n                    return filteredEps;\n                }\n                return Reflect.get(target, prop);\n            }\n        })\n    ];\n}; //# sourceMappingURL=backend-impl.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/backend-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/backend.js":
/*!*************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/backend.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerBackend: () => (/* reexport safe */ _backend_impl_js__WEBPACK_IMPORTED_MODULE_0__.registerBackend)\n/* harmony export */ });\n/* harmony import */ var _backend_impl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./backend-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/backend-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n //# sourceMappingURL=backend.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL2JhY2tlbmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSw0REFBNEQ7QUFDNUQsa0NBQWtDO0FBQ2tCLENBQ3BELG1DQUFtQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ltYWdlLXRleHQtc3R1ZGlvLTAzLy4vbm9kZV9tb2R1bGVzL29ubnhydW50aW1lLWNvbW1vbi9kaXN0L2VzbS9iYWNrZW5kLmpzPzQwN2IiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG5leHBvcnQgeyByZWdpc3RlckJhY2tlbmQgfSBmcm9tICcuL2JhY2tlbmQtaW1wbC5qcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1iYWNrZW5kLmpzLm1hcCJdLCJuYW1lcyI6WyJyZWdpc3RlckJhY2tlbmQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/backend.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/env-impl.js":
/*!**************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/env-impl.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/version.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nlet logLevelValue = \"warning\";\nconst env = {\n    wasm: {},\n    webgl: {},\n    webgpu: {},\n    versions: {\n        common: _version_js__WEBPACK_IMPORTED_MODULE_0__.version\n    },\n    set logLevel (value){\n        if (value === undefined) {\n            return;\n        }\n        if (typeof value !== \"string\" || [\n            \"verbose\",\n            \"info\",\n            \"warning\",\n            \"error\",\n            \"fatal\"\n        ].indexOf(value) === -1) {\n            throw new Error(`Unsupported logging level: ${value}`);\n        }\n        logLevelValue = value;\n    },\n    get logLevel () {\n        return logLevelValue;\n    }\n};\n// set property 'logLevel' so that they can be correctly transferred to worker by `postMessage()`.\nObject.defineProperty(env, \"logLevel\", {\n    enumerable: true\n}); //# sourceMappingURL=env-impl.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/env-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/env.js":
/*!*********************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/env.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var _env_impl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./env-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/env-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n/**\n * Represent a set of flags as a global singleton.\n */ const env = _env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env; //# sourceMappingURL=env.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL2Vudi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLDREQUE0RDtBQUM1RCxrQ0FBa0M7QUFDYTtBQUMvQzs7Q0FFQyxHQUNNLE1BQU1BLE1BQU1DLDZDQUFPQSxDQUFDLENBQzNCLCtCQUErQiIsInNvdXJjZXMiOlsid2VicGFjazovL2ltYWdlLXRleHQtc3R1ZGlvLTAzLy4vbm9kZV9tb2R1bGVzL29ubnhydW50aW1lLWNvbW1vbi9kaXN0L2VzbS9lbnYuanM/Mzg0YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS5cbmltcG9ydCB7IGVudiBhcyBlbnZJbXBsIH0gZnJvbSAnLi9lbnYtaW1wbC5qcyc7XG4vKipcbiAqIFJlcHJlc2VudCBhIHNldCBvZiBmbGFncyBhcyBhIGdsb2JhbCBzaW5nbGV0b24uXG4gKi9cbmV4cG9ydCBjb25zdCBlbnYgPSBlbnZJbXBsO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW52LmpzLm1hcCJdLCJuYW1lcyI6WyJlbnYiLCJlbnZJbXBsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/env.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InferenceSession: () => (/* reexport safe */ _inference_session_js__WEBPACK_IMPORTED_MODULE_2__.InferenceSession),\n/* harmony export */   TRACE: () => (/* reexport safe */ _trace_js__WEBPACK_IMPORTED_MODULE_6__.TRACE),\n/* harmony export */   TRACE_FUNC_BEGIN: () => (/* reexport safe */ _trace_js__WEBPACK_IMPORTED_MODULE_6__.TRACE_FUNC_BEGIN),\n/* harmony export */   TRACE_FUNC_END: () => (/* reexport safe */ _trace_js__WEBPACK_IMPORTED_MODULE_6__.TRACE_FUNC_END),\n/* harmony export */   Tensor: () => (/* reexport safe */ _tensor_js__WEBPACK_IMPORTED_MODULE_3__.Tensor),\n/* harmony export */   env: () => (/* reexport safe */ _env_js__WEBPACK_IMPORTED_MODULE_1__.env),\n/* harmony export */   registerBackend: () => (/* reexport safe */ _backend_js__WEBPACK_IMPORTED_MODULE_0__.registerBackend)\n/* harmony export */ });\n/* harmony import */ var _backend_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./backend.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/backend.js\");\n/* harmony import */ var _env_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./env.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/env.js\");\n/* harmony import */ var _inference_session_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./inference-session.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/inference-session.js\");\n/* harmony import */ var _tensor_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tensor.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor.js\");\n/* harmony import */ var _tensor_conversion_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tensor-conversion.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-conversion.js\");\n/* harmony import */ var _tensor_factory_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tensor-factory.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-factory.js\");\n/* harmony import */ var _trace_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./trace.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/trace.js\");\n/* harmony import */ var _onnx_model_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./onnx-model.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/onnx-model.js\");\n/* harmony import */ var _onnx_value_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./onnx-value.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/onnx-value.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n/**\n * # ONNX Runtime JavaScript API\n *\n * ONNX Runtime JavaScript API is a unified API for all JavaScript usages, including the following NPM packages:\n *\n * - [onnxruntime-node](https://www.npmjs.com/package/onnxruntime-node)\n * - [onnxruntime-web](https://www.npmjs.com/package/onnxruntime-web)\n * - [onnxruntime-react-native](https://www.npmjs.com/package/onnxruntime-react-native)\n *\n * See also:\n * - [Get Started](https://onnxruntime.ai/docs/get-started/with-javascript/)\n * - [Inference examples](https://github.com/microsoft/onnxruntime-inference-examples/tree/main/js)\n *\n * @packageDocumentation\n */ \n\n\n\n\n\n\n\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSw0REFBNEQ7QUFDNUQsa0NBQWtDO0FBQ2xDOzs7Ozs7Ozs7Ozs7OztDQWNDLEdBQzRCO0FBQ0o7QUFDYztBQUNYO0FBQ1c7QUFDSDtBQUNUO0FBQ0s7QUFDQSxDQUNoQyxpQ0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWFnZS10ZXh0LXN0dWRpby0wMy8uL25vZGVfbW9kdWxlcy9vbm54cnVudGltZS1jb21tb24vZGlzdC9lc20vaW5kZXguanM/ZmU1YSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS5cbi8qKlxuICogIyBPTk5YIFJ1bnRpbWUgSmF2YVNjcmlwdCBBUElcbiAqXG4gKiBPTk5YIFJ1bnRpbWUgSmF2YVNjcmlwdCBBUEkgaXMgYSB1bmlmaWVkIEFQSSBmb3IgYWxsIEphdmFTY3JpcHQgdXNhZ2VzLCBpbmNsdWRpbmcgdGhlIGZvbGxvd2luZyBOUE0gcGFja2FnZXM6XG4gKlxuICogLSBbb25ueHJ1bnRpbWUtbm9kZV0oaHR0cHM6Ly93d3cubnBtanMuY29tL3BhY2thZ2Uvb25ueHJ1bnRpbWUtbm9kZSlcbiAqIC0gW29ubnhydW50aW1lLXdlYl0oaHR0cHM6Ly93d3cubnBtanMuY29tL3BhY2thZ2Uvb25ueHJ1bnRpbWUtd2ViKVxuICogLSBbb25ueHJ1bnRpbWUtcmVhY3QtbmF0aXZlXShodHRwczovL3d3dy5ucG1qcy5jb20vcGFja2FnZS9vbm54cnVudGltZS1yZWFjdC1uYXRpdmUpXG4gKlxuICogU2VlIGFsc286XG4gKiAtIFtHZXQgU3RhcnRlZF0oaHR0cHM6Ly9vbm54cnVudGltZS5haS9kb2NzL2dldC1zdGFydGVkL3dpdGgtamF2YXNjcmlwdC8pXG4gKiAtIFtJbmZlcmVuY2UgZXhhbXBsZXNdKGh0dHBzOi8vZ2l0aHViLmNvbS9taWNyb3NvZnQvb25ueHJ1bnRpbWUtaW5mZXJlbmNlLWV4YW1wbGVzL3RyZWUvbWFpbi9qcylcbiAqXG4gKiBAcGFja2FnZURvY3VtZW50YXRpb25cbiAqL1xuZXhwb3J0ICogZnJvbSAnLi9iYWNrZW5kLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vZW52LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vaW5mZXJlbmNlLXNlc3Npb24uanMnO1xuZXhwb3J0ICogZnJvbSAnLi90ZW5zb3IuanMnO1xuZXhwb3J0ICogZnJvbSAnLi90ZW5zb3ItY29udmVyc2lvbi5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3RlbnNvci1mYWN0b3J5LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHJhY2UuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9vbm54LW1vZGVsLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vb25ueC12YWx1ZS5qcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/inference-session-impl.js":
/*!****************************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/inference-session-impl.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InferenceSession: () => (/* binding */ InferenceSession)\n/* harmony export */ });\n/* harmony import */ var _backend_impl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./backend-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/backend-impl.js\");\n/* harmony import */ var _tensor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tensor.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor.js\");\n/* harmony import */ var _trace_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./trace.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/trace.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n\n\nclass InferenceSession {\n    constructor(handler){\n        this.handler = handler;\n    }\n    async run(feeds, arg1, arg2) {\n        (0,_trace_js__WEBPACK_IMPORTED_MODULE_2__.TRACE_FUNC_BEGIN)();\n        const fetches = {};\n        let options = {};\n        // check inputs\n        if (typeof feeds !== \"object\" || feeds === null || feeds instanceof _tensor_js__WEBPACK_IMPORTED_MODULE_1__.Tensor || Array.isArray(feeds)) {\n            throw new TypeError(\"'feeds' must be an object that use input names as keys and OnnxValue as corresponding values.\");\n        }\n        let isFetchesEmpty = true;\n        // determine which override is being used\n        if (typeof arg1 === \"object\") {\n            if (arg1 === null) {\n                throw new TypeError(\"Unexpected argument[1]: cannot be null.\");\n            }\n            if (arg1 instanceof _tensor_js__WEBPACK_IMPORTED_MODULE_1__.Tensor) {\n                throw new TypeError(\"'fetches' cannot be a Tensor\");\n            }\n            if (Array.isArray(arg1)) {\n                if (arg1.length === 0) {\n                    throw new TypeError(\"'fetches' cannot be an empty array.\");\n                }\n                isFetchesEmpty = false;\n                // output names\n                for (const name of arg1){\n                    if (typeof name !== \"string\") {\n                        throw new TypeError(\"'fetches' must be a string array or an object.\");\n                    }\n                    if (this.outputNames.indexOf(name) === -1) {\n                        throw new RangeError(`'fetches' contains invalid output name: ${name}.`);\n                    }\n                    fetches[name] = null;\n                }\n                if (typeof arg2 === \"object\" && arg2 !== null) {\n                    options = arg2;\n                } else if (typeof arg2 !== \"undefined\") {\n                    throw new TypeError(\"'options' must be an object.\");\n                }\n            } else {\n                // decide whether arg1 is fetches or options\n                // if any output name is present and its value is valid OnnxValue, we consider it fetches\n                let isFetches = false;\n                const arg1Keys = Object.getOwnPropertyNames(arg1);\n                for (const name of this.outputNames){\n                    if (arg1Keys.indexOf(name) !== -1) {\n                        const v = arg1[name];\n                        if (v === null || v instanceof _tensor_js__WEBPACK_IMPORTED_MODULE_1__.Tensor) {\n                            isFetches = true;\n                            isFetchesEmpty = false;\n                            fetches[name] = v;\n                        }\n                    }\n                }\n                if (isFetches) {\n                    if (typeof arg2 === \"object\" && arg2 !== null) {\n                        options = arg2;\n                    } else if (typeof arg2 !== \"undefined\") {\n                        throw new TypeError(\"'options' must be an object.\");\n                    }\n                } else {\n                    options = arg1;\n                }\n            }\n        } else if (typeof arg1 !== \"undefined\") {\n            throw new TypeError(\"Unexpected argument[1]: must be 'fetches' or 'options'.\");\n        }\n        // check if all inputs are in feed\n        for (const name of this.inputNames){\n            if (typeof feeds[name] === \"undefined\") {\n                throw new Error(`input '${name}' is missing in 'feeds'.`);\n            }\n        }\n        // if no fetches is specified, we use the full output names list\n        if (isFetchesEmpty) {\n            for (const name of this.outputNames){\n                fetches[name] = null;\n            }\n        }\n        // feeds, fetches and options are prepared\n        const results = await this.handler.run(feeds, fetches, options);\n        const returnValue = {};\n        for(const key in results){\n            if (Object.hasOwnProperty.call(results, key)) {\n                const result = results[key];\n                if (result instanceof _tensor_js__WEBPACK_IMPORTED_MODULE_1__.Tensor) {\n                    returnValue[key] = result;\n                } else {\n                    returnValue[key] = new _tensor_js__WEBPACK_IMPORTED_MODULE_1__.Tensor(result.type, result.data, result.dims);\n                }\n            }\n        }\n        (0,_trace_js__WEBPACK_IMPORTED_MODULE_2__.TRACE_FUNC_END)();\n        return returnValue;\n    }\n    async release() {\n        return this.handler.dispose();\n    }\n    static async create(arg0, arg1, arg2, arg3) {\n        (0,_trace_js__WEBPACK_IMPORTED_MODULE_2__.TRACE_FUNC_BEGIN)();\n        // either load from a file or buffer\n        let filePathOrUint8Array;\n        let options = {};\n        if (typeof arg0 === \"string\") {\n            filePathOrUint8Array = arg0;\n            if (typeof arg1 === \"object\" && arg1 !== null) {\n                options = arg1;\n            } else if (typeof arg1 !== \"undefined\") {\n                throw new TypeError(\"'options' must be an object.\");\n            }\n        } else if (arg0 instanceof Uint8Array) {\n            filePathOrUint8Array = arg0;\n            if (typeof arg1 === \"object\" && arg1 !== null) {\n                options = arg1;\n            } else if (typeof arg1 !== \"undefined\") {\n                throw new TypeError(\"'options' must be an object.\");\n            }\n        } else if (arg0 instanceof ArrayBuffer || typeof SharedArrayBuffer !== \"undefined\" && arg0 instanceof SharedArrayBuffer) {\n            const buffer = arg0;\n            let byteOffset = 0;\n            let byteLength = arg0.byteLength;\n            if (typeof arg1 === \"object\" && arg1 !== null) {\n                options = arg1;\n            } else if (typeof arg1 === \"number\") {\n                byteOffset = arg1;\n                if (!Number.isSafeInteger(byteOffset)) {\n                    throw new RangeError(\"'byteOffset' must be an integer.\");\n                }\n                if (byteOffset < 0 || byteOffset >= buffer.byteLength) {\n                    throw new RangeError(`'byteOffset' is out of range [0, ${buffer.byteLength}).`);\n                }\n                byteLength = arg0.byteLength - byteOffset;\n                if (typeof arg2 === \"number\") {\n                    byteLength = arg2;\n                    if (!Number.isSafeInteger(byteLength)) {\n                        throw new RangeError(\"'byteLength' must be an integer.\");\n                    }\n                    if (byteLength <= 0 || byteOffset + byteLength > buffer.byteLength) {\n                        throw new RangeError(`'byteLength' is out of range (0, ${buffer.byteLength - byteOffset}].`);\n                    }\n                    if (typeof arg3 === \"object\" && arg3 !== null) {\n                        options = arg3;\n                    } else if (typeof arg3 !== \"undefined\") {\n                        throw new TypeError(\"'options' must be an object.\");\n                    }\n                } else if (typeof arg2 !== \"undefined\") {\n                    throw new TypeError(\"'byteLength' must be a number.\");\n                }\n            } else if (typeof arg1 !== \"undefined\") {\n                throw new TypeError(\"'options' must be an object.\");\n            }\n            filePathOrUint8Array = new Uint8Array(buffer, byteOffset, byteLength);\n        } else {\n            throw new TypeError(\"Unexpected argument[0]: must be 'path' or 'buffer'.\");\n        }\n        // resolve backend, update session options with validated EPs, and create session handler\n        const [backend, optionsWithValidatedEPs] = await (0,_backend_impl_js__WEBPACK_IMPORTED_MODULE_0__.resolveBackendAndExecutionProviders)(options);\n        const handler = await backend.createInferenceSessionHandler(filePathOrUint8Array, optionsWithValidatedEPs);\n        (0,_trace_js__WEBPACK_IMPORTED_MODULE_2__.TRACE_FUNC_END)();\n        return new InferenceSession(handler);\n    }\n    startProfiling() {\n        this.handler.startProfiling();\n    }\n    endProfiling() {\n        this.handler.endProfiling();\n    }\n    get inputNames() {\n        return this.handler.inputNames;\n    }\n    get outputNames() {\n        return this.handler.outputNames;\n    }\n} //# sourceMappingURL=inference-session-impl.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/inference-session-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/inference-session.js":
/*!***********************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/inference-session.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InferenceSession: () => (/* binding */ InferenceSession)\n/* harmony export */ });\n/* harmony import */ var _inference_session_impl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./inference-session-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/inference-session-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nconst InferenceSession = _inference_session_impl_js__WEBPACK_IMPORTED_MODULE_0__.InferenceSession; //# sourceMappingURL=inference-session.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL2luZmVyZW5jZS1zZXNzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNERBQTREO0FBQzVELGtDQUFrQztBQUNxRDtBQUN2RixnRUFBZ0U7QUFDekQsTUFBTUEsbUJBQW1CQyx3RUFBb0JBLENBQUMsQ0FDckQsNkNBQTZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1hZ2UtdGV4dC1zdHVkaW8tMDMvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL2luZmVyZW5jZS1zZXNzaW9uLmpzP2EzM2EiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG5pbXBvcnQgeyBJbmZlcmVuY2VTZXNzaW9uIGFzIEluZmVyZW5jZVNlc3Npb25JbXBsIH0gZnJvbSAnLi9pbmZlcmVuY2Utc2Vzc2lvbi1pbXBsLmpzJztcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbmFtaW5nLWNvbnZlbnRpb25cbmV4cG9ydCBjb25zdCBJbmZlcmVuY2VTZXNzaW9uID0gSW5mZXJlbmNlU2Vzc2lvbkltcGw7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmZlcmVuY2Utc2Vzc2lvbi5qcy5tYXAiXSwibmFtZXMiOlsiSW5mZXJlbmNlU2Vzc2lvbiIsIkluZmVyZW5jZVNlc3Npb25JbXBsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/inference-session.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/onnx-model.js":
/*!****************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/onnx-model.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n //# sourceMappingURL=onnx-model.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL29ubngtbW9kZWwuanMiLCJtYXBwaW5ncyI6IjtBQUFBLDREQUE0RDtBQUM1RCxrQ0FBa0M7QUFDeEIsQ0FDVixzQ0FBc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWFnZS10ZXh0LXN0dWRpby0wMy8uL25vZGVfbW9kdWxlcy9vbm54cnVudGltZS1jb21tb24vZGlzdC9lc20vb25ueC1tb2RlbC5qcz9jZDc1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9b25ueC1tb2RlbC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/onnx-model.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/onnx-value.js":
/*!****************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/onnx-value.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n //# sourceMappingURL=onnx-value.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL29ubngtdmFsdWUuanMiLCJtYXBwaW5ncyI6IjtBQUFBLDREQUE0RDtBQUM1RCxrQ0FBa0M7QUFDeEIsQ0FDVixzQ0FBc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWFnZS10ZXh0LXN0dWRpby0wMy8uL25vZGVfbW9kdWxlcy9vbm54cnVudGltZS1jb21tb24vZGlzdC9lc20vb25ueC12YWx1ZS5qcz85MmM4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9b25ueC12YWx1ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/onnx-value.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-conversion-impl.js":
/*!****************************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/tensor-conversion-impl.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tensorToDataURL: () => (/* binding */ tensorToDataURL),\n/* harmony export */   tensorToImageData: () => (/* binding */ tensorToImageData)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n/**\n * implementation of Tensor.toDataURL()\n */ const tensorToDataURL = (tensor, options)=>{\n    const canvas = typeof document !== \"undefined\" ? document.createElement(\"canvas\") : new OffscreenCanvas(1, 1);\n    canvas.width = tensor.dims[3];\n    canvas.height = tensor.dims[2];\n    const pixels2DContext = canvas.getContext(\"2d\");\n    if (pixels2DContext != null) {\n        // Default values for height and width & format\n        let width;\n        let height;\n        if (options?.tensorLayout !== undefined && options.tensorLayout === \"NHWC\") {\n            width = tensor.dims[2];\n            height = tensor.dims[3];\n        } else {\n            // Default layout is NCWH\n            width = tensor.dims[3];\n            height = tensor.dims[2];\n        }\n        const inputformat = options?.format !== undefined ? options.format : \"RGB\";\n        const norm = options?.norm;\n        let normMean;\n        let normBias;\n        if (norm === undefined || norm.mean === undefined) {\n            normMean = [\n                255,\n                255,\n                255,\n                255\n            ];\n        } else {\n            if (typeof norm.mean === \"number\") {\n                normMean = [\n                    norm.mean,\n                    norm.mean,\n                    norm.mean,\n                    norm.mean\n                ];\n            } else {\n                normMean = [\n                    norm.mean[0],\n                    norm.mean[1],\n                    norm.mean[2],\n                    0\n                ];\n                if (norm.mean[3] !== undefined) {\n                    normMean[3] = norm.mean[3];\n                }\n            }\n        }\n        if (norm === undefined || norm.bias === undefined) {\n            normBias = [\n                0,\n                0,\n                0,\n                0\n            ];\n        } else {\n            if (typeof norm.bias === \"number\") {\n                normBias = [\n                    norm.bias,\n                    norm.bias,\n                    norm.bias,\n                    norm.bias\n                ];\n            } else {\n                normBias = [\n                    norm.bias[0],\n                    norm.bias[1],\n                    norm.bias[2],\n                    0\n                ];\n                if (norm.bias[3] !== undefined) {\n                    normBias[3] = norm.bias[3];\n                }\n            }\n        }\n        const stride = height * width;\n        // Default pointer assignments\n        let rTensorPointer = 0, gTensorPointer = stride, bTensorPointer = stride * 2, aTensorPointer = -1;\n        // Updating the pointer assignments based on the input image format\n        if (inputformat === \"RGBA\") {\n            rTensorPointer = 0;\n            gTensorPointer = stride;\n            bTensorPointer = stride * 2;\n            aTensorPointer = stride * 3;\n        } else if (inputformat === \"RGB\") {\n            rTensorPointer = 0;\n            gTensorPointer = stride;\n            bTensorPointer = stride * 2;\n        } else if (inputformat === \"RBG\") {\n            rTensorPointer = 0;\n            bTensorPointer = stride;\n            gTensorPointer = stride * 2;\n        }\n        for(let i = 0; i < height; i++){\n            for(let j = 0; j < width; j++){\n                const R = (tensor.data[rTensorPointer++] - normBias[0]) * normMean[0]; // R value\n                const G = (tensor.data[gTensorPointer++] - normBias[1]) * normMean[1]; // G value\n                const B = (tensor.data[bTensorPointer++] - normBias[2]) * normMean[2]; // B value\n                const A = aTensorPointer === -1 ? 255 : (tensor.data[aTensorPointer++] - normBias[3]) * normMean[3]; // A value\n                // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n                pixels2DContext.fillStyle = \"rgba(\" + R + \",\" + G + \",\" + B + \",\" + A + \")\";\n                pixels2DContext.fillRect(j, i, 1, 1);\n            }\n        }\n        if (\"toDataURL\" in canvas) {\n            return canvas.toDataURL();\n        } else {\n            throw new Error(\"toDataURL is not supported\");\n        }\n    } else {\n        throw new Error(\"Can not access image data\");\n    }\n};\n/**\n * implementation of Tensor.toImageData()\n */ const tensorToImageData = (tensor, options)=>{\n    const pixels2DContext = typeof document !== \"undefined\" ? document.createElement(\"canvas\").getContext(\"2d\") : new OffscreenCanvas(1, 1).getContext(\"2d\");\n    let image;\n    if (pixels2DContext != null) {\n        // Default values for height and width & format\n        let width;\n        let height;\n        let channels;\n        if (options?.tensorLayout !== undefined && options.tensorLayout === \"NHWC\") {\n            width = tensor.dims[2];\n            height = tensor.dims[1];\n            channels = tensor.dims[3];\n        } else {\n            // Default layout is NCWH\n            width = tensor.dims[3];\n            height = tensor.dims[2];\n            channels = tensor.dims[1];\n        }\n        const inputformat = options !== undefined ? options.format !== undefined ? options.format : \"RGB\" : \"RGB\";\n        const norm = options?.norm;\n        let normMean;\n        let normBias;\n        if (norm === undefined || norm.mean === undefined) {\n            normMean = [\n                255,\n                255,\n                255,\n                255\n            ];\n        } else {\n            if (typeof norm.mean === \"number\") {\n                normMean = [\n                    norm.mean,\n                    norm.mean,\n                    norm.mean,\n                    norm.mean\n                ];\n            } else {\n                normMean = [\n                    norm.mean[0],\n                    norm.mean[1],\n                    norm.mean[2],\n                    255\n                ];\n                if (norm.mean[3] !== undefined) {\n                    normMean[3] = norm.mean[3];\n                }\n            }\n        }\n        if (norm === undefined || norm.bias === undefined) {\n            normBias = [\n                0,\n                0,\n                0,\n                0\n            ];\n        } else {\n            if (typeof norm.bias === \"number\") {\n                normBias = [\n                    norm.bias,\n                    norm.bias,\n                    norm.bias,\n                    norm.bias\n                ];\n            } else {\n                normBias = [\n                    norm.bias[0],\n                    norm.bias[1],\n                    norm.bias[2],\n                    0\n                ];\n                if (norm.bias[3] !== undefined) {\n                    normBias[3] = norm.bias[3];\n                }\n            }\n        }\n        const stride = height * width;\n        if (options !== undefined) {\n            if (options.format !== undefined && channels === 4 && options.format !== \"RGBA\" || channels === 3 && options.format !== \"RGB\" && options.format !== \"BGR\") {\n                throw new Error(\"Tensor format doesn't match input tensor dims\");\n            }\n        }\n        // Default pointer assignments\n        const step = 4;\n        let rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n        let rTensorPointer = 0, gTensorPointer = stride, bTensorPointer = stride * 2, aTensorPointer = -1;\n        // Updating the pointer assignments based on the input image format\n        if (inputformat === \"RGBA\") {\n            rTensorPointer = 0;\n            gTensorPointer = stride;\n            bTensorPointer = stride * 2;\n            aTensorPointer = stride * 3;\n        } else if (inputformat === \"RGB\") {\n            rTensorPointer = 0;\n            gTensorPointer = stride;\n            bTensorPointer = stride * 2;\n        } else if (inputformat === \"RBG\") {\n            rTensorPointer = 0;\n            bTensorPointer = stride;\n            gTensorPointer = stride * 2;\n        }\n        image = pixels2DContext.createImageData(width, height);\n        for(let i = 0; i < height * width; rImagePointer += step, gImagePointer += step, bImagePointer += step, aImagePointer += step, i++){\n            image.data[rImagePointer] = (tensor.data[rTensorPointer++] - normBias[0]) * normMean[0]; // R value\n            image.data[gImagePointer] = (tensor.data[gTensorPointer++] - normBias[1]) * normMean[1]; // G value\n            image.data[bImagePointer] = (tensor.data[bTensorPointer++] - normBias[2]) * normMean[2]; // B value\n            image.data[aImagePointer] = aTensorPointer === -1 ? 255 : (tensor.data[aTensorPointer++] - normBias[3]) * normMean[3]; // A value\n        }\n    } else {\n        throw new Error(\"Can not access image data\");\n    }\n    return image;\n}; //# sourceMappingURL=tensor-conversion-impl.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL3RlbnNvci1jb252ZXJzaW9uLWltcGwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSw0REFBNEQ7QUFDNUQsa0NBQWtDO0FBQ2xDOztDQUVDLEdBQ00sTUFBTUEsa0JBQWtCLENBQUNDLFFBQVFDO0lBQ3BDLE1BQU1DLFNBQVMsT0FBT0MsYUFBYSxjQUFjQSxTQUFTQyxhQUFhLENBQUMsWUFBWSxJQUFJQyxnQkFBZ0IsR0FBRztJQUMzR0gsT0FBT0ksS0FBSyxHQUFHTixPQUFPTyxJQUFJLENBQUMsRUFBRTtJQUM3QkwsT0FBT00sTUFBTSxHQUFHUixPQUFPTyxJQUFJLENBQUMsRUFBRTtJQUM5QixNQUFNRSxrQkFBa0JQLE9BQU9RLFVBQVUsQ0FBQztJQUMxQyxJQUFJRCxtQkFBbUIsTUFBTTtRQUN6QiwrQ0FBK0M7UUFDL0MsSUFBSUg7UUFDSixJQUFJRTtRQUNKLElBQUlQLFNBQVNVLGlCQUFpQkMsYUFBYVgsUUFBUVUsWUFBWSxLQUFLLFFBQVE7WUFDeEVMLFFBQVFOLE9BQU9PLElBQUksQ0FBQyxFQUFFO1lBQ3RCQyxTQUFTUixPQUFPTyxJQUFJLENBQUMsRUFBRTtRQUMzQixPQUNLO1lBQ0QseUJBQXlCO1lBQ3pCRCxRQUFRTixPQUFPTyxJQUFJLENBQUMsRUFBRTtZQUN0QkMsU0FBU1IsT0FBT08sSUFBSSxDQUFDLEVBQUU7UUFDM0I7UUFDQSxNQUFNTSxjQUFjWixTQUFTYSxXQUFXRixZQUFZWCxRQUFRYSxNQUFNLEdBQUc7UUFDckUsTUFBTUMsT0FBT2QsU0FBU2M7UUFDdEIsSUFBSUM7UUFDSixJQUFJQztRQUNKLElBQUlGLFNBQVNILGFBQWFHLEtBQUtHLElBQUksS0FBS04sV0FBVztZQUMvQ0ksV0FBVztnQkFBQztnQkFBSztnQkFBSztnQkFBSzthQUFJO1FBQ25DLE9BQ0s7WUFDRCxJQUFJLE9BQU9ELEtBQUtHLElBQUksS0FBSyxVQUFVO2dCQUMvQkYsV0FBVztvQkFBQ0QsS0FBS0csSUFBSTtvQkFBRUgsS0FBS0csSUFBSTtvQkFBRUgsS0FBS0csSUFBSTtvQkFBRUgsS0FBS0csSUFBSTtpQkFBQztZQUMzRCxPQUNLO2dCQUNERixXQUFXO29CQUFDRCxLQUFLRyxJQUFJLENBQUMsRUFBRTtvQkFBRUgsS0FBS0csSUFBSSxDQUFDLEVBQUU7b0JBQUVILEtBQUtHLElBQUksQ0FBQyxFQUFFO29CQUFFO2lCQUFFO2dCQUN4RCxJQUFJSCxLQUFLRyxJQUFJLENBQUMsRUFBRSxLQUFLTixXQUFXO29CQUM1QkksUUFBUSxDQUFDLEVBQUUsR0FBR0QsS0FBS0csSUFBSSxDQUFDLEVBQUU7Z0JBQzlCO1lBQ0o7UUFDSjtRQUNBLElBQUlILFNBQVNILGFBQWFHLEtBQUtJLElBQUksS0FBS1AsV0FBVztZQUMvQ0ssV0FBVztnQkFBQztnQkFBRztnQkFBRztnQkFBRzthQUFFO1FBQzNCLE9BQ0s7WUFDRCxJQUFJLE9BQU9GLEtBQUtJLElBQUksS0FBSyxVQUFVO2dCQUMvQkYsV0FBVztvQkFBQ0YsS0FBS0ksSUFBSTtvQkFBRUosS0FBS0ksSUFBSTtvQkFBRUosS0FBS0ksSUFBSTtvQkFBRUosS0FBS0ksSUFBSTtpQkFBQztZQUMzRCxPQUNLO2dCQUNERixXQUFXO29CQUFDRixLQUFLSSxJQUFJLENBQUMsRUFBRTtvQkFBRUosS0FBS0ksSUFBSSxDQUFDLEVBQUU7b0JBQUVKLEtBQUtJLElBQUksQ0FBQyxFQUFFO29CQUFFO2lCQUFFO2dCQUN4RCxJQUFJSixLQUFLSSxJQUFJLENBQUMsRUFBRSxLQUFLUCxXQUFXO29CQUM1QkssUUFBUSxDQUFDLEVBQUUsR0FBR0YsS0FBS0ksSUFBSSxDQUFDLEVBQUU7Z0JBQzlCO1lBQ0o7UUFDSjtRQUNBLE1BQU1DLFNBQVNaLFNBQVNGO1FBQ3hCLDhCQUE4QjtRQUM5QixJQUFJZSxpQkFBaUIsR0FBR0MsaUJBQWlCRixRQUFRRyxpQkFBaUJILFNBQVMsR0FBR0ksaUJBQWlCLENBQUM7UUFDaEcsbUVBQW1FO1FBQ25FLElBQUlYLGdCQUFnQixRQUFRO1lBQ3hCUSxpQkFBaUI7WUFDakJDLGlCQUFpQkY7WUFDakJHLGlCQUFpQkgsU0FBUztZQUMxQkksaUJBQWlCSixTQUFTO1FBQzlCLE9BQ0ssSUFBSVAsZ0JBQWdCLE9BQU87WUFDNUJRLGlCQUFpQjtZQUNqQkMsaUJBQWlCRjtZQUNqQkcsaUJBQWlCSCxTQUFTO1FBQzlCLE9BQ0ssSUFBSVAsZ0JBQWdCLE9BQU87WUFDNUJRLGlCQUFpQjtZQUNqQkUsaUJBQWlCSDtZQUNqQkUsaUJBQWlCRixTQUFTO1FBQzlCO1FBQ0EsSUFBSyxJQUFJSyxJQUFJLEdBQUdBLElBQUlqQixRQUFRaUIsSUFBSztZQUM3QixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSXBCLE9BQU9vQixJQUFLO2dCQUM1QixNQUFNQyxJQUFJLENBQUMzQixPQUFPNEIsSUFBSSxDQUFDUCxpQkFBaUIsR0FBR0osUUFBUSxDQUFDLEVBQUUsSUFBSUQsUUFBUSxDQUFDLEVBQUUsRUFBRSxVQUFVO2dCQUNqRixNQUFNYSxJQUFJLENBQUM3QixPQUFPNEIsSUFBSSxDQUFDTixpQkFBaUIsR0FBR0wsUUFBUSxDQUFDLEVBQUUsSUFBSUQsUUFBUSxDQUFDLEVBQUUsRUFBRSxVQUFVO2dCQUNqRixNQUFNYyxJQUFJLENBQUM5QixPQUFPNEIsSUFBSSxDQUFDTCxpQkFBaUIsR0FBR04sUUFBUSxDQUFDLEVBQUUsSUFBSUQsUUFBUSxDQUFDLEVBQUUsRUFBRSxVQUFVO2dCQUNqRixNQUFNZSxJQUFJUCxtQkFBbUIsQ0FBQyxJQUFJLE1BQU0sQ0FBQ3hCLE9BQU80QixJQUFJLENBQUNKLGlCQUFpQixHQUFHUCxRQUFRLENBQUMsRUFBRSxJQUFJRCxRQUFRLENBQUMsRUFBRSxFQUFFLFVBQVU7Z0JBQy9HLHFFQUFxRTtnQkFDckVQLGdCQUFnQnVCLFNBQVMsR0FBRyxVQUFVTCxJQUFJLE1BQU1FLElBQUksTUFBTUMsSUFBSSxNQUFNQyxJQUFJO2dCQUN4RXRCLGdCQUFnQndCLFFBQVEsQ0FBQ1AsR0FBR0QsR0FBRyxHQUFHO1lBQ3RDO1FBQ0o7UUFDQSxJQUFJLGVBQWV2QixRQUFRO1lBQ3ZCLE9BQU9BLE9BQU9nQyxTQUFTO1FBQzNCLE9BQ0s7WUFDRCxNQUFNLElBQUlDLE1BQU07UUFDcEI7SUFDSixPQUNLO1FBQ0QsTUFBTSxJQUFJQSxNQUFNO0lBQ3BCO0FBQ0osRUFBRTtBQUNGOztDQUVDLEdBQ00sTUFBTUMsb0JBQW9CLENBQUNwQyxRQUFRQztJQUN0QyxNQUFNUSxrQkFBa0IsT0FBT04sYUFBYSxjQUN0Q0EsU0FBU0MsYUFBYSxDQUFDLFVBQVVNLFVBQVUsQ0FBQyxRQUM1QyxJQUFJTCxnQkFBZ0IsR0FBRyxHQUFHSyxVQUFVLENBQUM7SUFDM0MsSUFBSTJCO0lBQ0osSUFBSTVCLG1CQUFtQixNQUFNO1FBQ3pCLCtDQUErQztRQUMvQyxJQUFJSDtRQUNKLElBQUlFO1FBQ0osSUFBSThCO1FBQ0osSUFBSXJDLFNBQVNVLGlCQUFpQkMsYUFBYVgsUUFBUVUsWUFBWSxLQUFLLFFBQVE7WUFDeEVMLFFBQVFOLE9BQU9PLElBQUksQ0FBQyxFQUFFO1lBQ3RCQyxTQUFTUixPQUFPTyxJQUFJLENBQUMsRUFBRTtZQUN2QitCLFdBQVd0QyxPQUFPTyxJQUFJLENBQUMsRUFBRTtRQUM3QixPQUNLO1lBQ0QseUJBQXlCO1lBQ3pCRCxRQUFRTixPQUFPTyxJQUFJLENBQUMsRUFBRTtZQUN0QkMsU0FBU1IsT0FBT08sSUFBSSxDQUFDLEVBQUU7WUFDdkIrQixXQUFXdEMsT0FBT08sSUFBSSxDQUFDLEVBQUU7UUFDN0I7UUFDQSxNQUFNTSxjQUFjWixZQUFZVyxZQUFhWCxRQUFRYSxNQUFNLEtBQUtGLFlBQVlYLFFBQVFhLE1BQU0sR0FBRyxRQUFTO1FBQ3RHLE1BQU1DLE9BQU9kLFNBQVNjO1FBQ3RCLElBQUlDO1FBQ0osSUFBSUM7UUFDSixJQUFJRixTQUFTSCxhQUFhRyxLQUFLRyxJQUFJLEtBQUtOLFdBQVc7WUFDL0NJLFdBQVc7Z0JBQUM7Z0JBQUs7Z0JBQUs7Z0JBQUs7YUFBSTtRQUNuQyxPQUNLO1lBQ0QsSUFBSSxPQUFPRCxLQUFLRyxJQUFJLEtBQUssVUFBVTtnQkFDL0JGLFdBQVc7b0JBQUNELEtBQUtHLElBQUk7b0JBQUVILEtBQUtHLElBQUk7b0JBQUVILEtBQUtHLElBQUk7b0JBQUVILEtBQUtHLElBQUk7aUJBQUM7WUFDM0QsT0FDSztnQkFDREYsV0FBVztvQkFBQ0QsS0FBS0csSUFBSSxDQUFDLEVBQUU7b0JBQUVILEtBQUtHLElBQUksQ0FBQyxFQUFFO29CQUFFSCxLQUFLRyxJQUFJLENBQUMsRUFBRTtvQkFBRTtpQkFBSTtnQkFDMUQsSUFBSUgsS0FBS0csSUFBSSxDQUFDLEVBQUUsS0FBS04sV0FBVztvQkFDNUJJLFFBQVEsQ0FBQyxFQUFFLEdBQUdELEtBQUtHLElBQUksQ0FBQyxFQUFFO2dCQUM5QjtZQUNKO1FBQ0o7UUFDQSxJQUFJSCxTQUFTSCxhQUFhRyxLQUFLSSxJQUFJLEtBQUtQLFdBQVc7WUFDL0NLLFdBQVc7Z0JBQUM7Z0JBQUc7Z0JBQUc7Z0JBQUc7YUFBRTtRQUMzQixPQUNLO1lBQ0QsSUFBSSxPQUFPRixLQUFLSSxJQUFJLEtBQUssVUFBVTtnQkFDL0JGLFdBQVc7b0JBQUNGLEtBQUtJLElBQUk7b0JBQUVKLEtBQUtJLElBQUk7b0JBQUVKLEtBQUtJLElBQUk7b0JBQUVKLEtBQUtJLElBQUk7aUJBQUM7WUFDM0QsT0FDSztnQkFDREYsV0FBVztvQkFBQ0YsS0FBS0ksSUFBSSxDQUFDLEVBQUU7b0JBQUVKLEtBQUtJLElBQUksQ0FBQyxFQUFFO29CQUFFSixLQUFLSSxJQUFJLENBQUMsRUFBRTtvQkFBRTtpQkFBRTtnQkFDeEQsSUFBSUosS0FBS0ksSUFBSSxDQUFDLEVBQUUsS0FBS1AsV0FBVztvQkFDNUJLLFFBQVEsQ0FBQyxFQUFFLEdBQUdGLEtBQUtJLElBQUksQ0FBQyxFQUFFO2dCQUM5QjtZQUNKO1FBQ0o7UUFDQSxNQUFNQyxTQUFTWixTQUFTRjtRQUN4QixJQUFJTCxZQUFZVyxXQUFXO1lBQ3ZCLElBQUksUUFBU0UsTUFBTSxLQUFLRixhQUFhMEIsYUFBYSxLQUFLckMsUUFBUWEsTUFBTSxLQUFLLFVBQ3JFd0IsYUFBYSxLQUFLckMsUUFBUWEsTUFBTSxLQUFLLFNBQVNiLFFBQVFhLE1BQU0sS0FBSyxPQUFRO2dCQUMxRSxNQUFNLElBQUlxQixNQUFNO1lBQ3BCO1FBQ0o7UUFDQSw4QkFBOEI7UUFDOUIsTUFBTUksT0FBTztRQUNiLElBQUlDLGdCQUFnQixHQUFHQyxnQkFBZ0IsR0FBR0MsZ0JBQWdCLEdBQUdDLGdCQUFnQjtRQUM3RSxJQUFJdEIsaUJBQWlCLEdBQUdDLGlCQUFpQkYsUUFBUUcsaUJBQWlCSCxTQUFTLEdBQUdJLGlCQUFpQixDQUFDO1FBQ2hHLG1FQUFtRTtRQUNuRSxJQUFJWCxnQkFBZ0IsUUFBUTtZQUN4QlEsaUJBQWlCO1lBQ2pCQyxpQkFBaUJGO1lBQ2pCRyxpQkFBaUJILFNBQVM7WUFDMUJJLGlCQUFpQkosU0FBUztRQUM5QixPQUNLLElBQUlQLGdCQUFnQixPQUFPO1lBQzVCUSxpQkFBaUI7WUFDakJDLGlCQUFpQkY7WUFDakJHLGlCQUFpQkgsU0FBUztRQUM5QixPQUNLLElBQUlQLGdCQUFnQixPQUFPO1lBQzVCUSxpQkFBaUI7WUFDakJFLGlCQUFpQkg7WUFDakJFLGlCQUFpQkYsU0FBUztRQUM5QjtRQUNBaUIsUUFBUTVCLGdCQUFnQm1DLGVBQWUsQ0FBQ3RDLE9BQU9FO1FBQy9DLElBQUssSUFBSWlCLElBQUksR0FBR0EsSUFBSWpCLFNBQVNGLE9BQU9rQyxpQkFBaUJELE1BQU1FLGlCQUFpQkYsTUFBTUcsaUJBQWlCSCxNQUFNSSxpQkFBaUJKLE1BQU1kLElBQUs7WUFDaklZLE1BQU1ULElBQUksQ0FBQ1ksY0FBYyxHQUFHLENBQUN4QyxPQUFPNEIsSUFBSSxDQUFDUCxpQkFBaUIsR0FBR0osUUFBUSxDQUFDLEVBQUUsSUFBSUQsUUFBUSxDQUFDLEVBQUUsRUFBRSxVQUFVO1lBQ25HcUIsTUFBTVQsSUFBSSxDQUFDYSxjQUFjLEdBQUcsQ0FBQ3pDLE9BQU80QixJQUFJLENBQUNOLGlCQUFpQixHQUFHTCxRQUFRLENBQUMsRUFBRSxJQUFJRCxRQUFRLENBQUMsRUFBRSxFQUFFLFVBQVU7WUFDbkdxQixNQUFNVCxJQUFJLENBQUNjLGNBQWMsR0FBRyxDQUFDMUMsT0FBTzRCLElBQUksQ0FBQ0wsaUJBQWlCLEdBQUdOLFFBQVEsQ0FBQyxFQUFFLElBQUlELFFBQVEsQ0FBQyxFQUFFLEVBQUUsVUFBVTtZQUNuR3FCLE1BQU1ULElBQUksQ0FBQ2UsY0FBYyxHQUNyQm5CLG1CQUFtQixDQUFDLElBQUksTUFBTSxDQUFDeEIsT0FBTzRCLElBQUksQ0FBQ0osaUJBQWlCLEdBQUdQLFFBQVEsQ0FBQyxFQUFFLElBQUlELFFBQVEsQ0FBQyxFQUFFLEVBQUUsVUFBVTtRQUM3RztJQUNKLE9BQ0s7UUFDRCxNQUFNLElBQUltQixNQUFNO0lBQ3BCO0lBQ0EsT0FBT0U7QUFDWCxFQUFFLENBQ0Ysa0RBQWtEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1hZ2UtdGV4dC1zdHVkaW8tMDMvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL3RlbnNvci1jb252ZXJzaW9uLWltcGwuanM/YzM0MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS5cbi8qKlxuICogaW1wbGVtZW50YXRpb24gb2YgVGVuc29yLnRvRGF0YVVSTCgpXG4gKi9cbmV4cG9ydCBjb25zdCB0ZW5zb3JUb0RhdGFVUkwgPSAodGVuc29yLCBvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgY2FudmFzID0gdHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJyA/IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2NhbnZhcycpIDogbmV3IE9mZnNjcmVlbkNhbnZhcygxLCAxKTtcbiAgICBjYW52YXMud2lkdGggPSB0ZW5zb3IuZGltc1szXTtcbiAgICBjYW52YXMuaGVpZ2h0ID0gdGVuc29yLmRpbXNbMl07XG4gICAgY29uc3QgcGl4ZWxzMkRDb250ZXh0ID0gY2FudmFzLmdldENvbnRleHQoJzJkJyk7XG4gICAgaWYgKHBpeGVsczJEQ29udGV4dCAhPSBudWxsKSB7XG4gICAgICAgIC8vIERlZmF1bHQgdmFsdWVzIGZvciBoZWlnaHQgYW5kIHdpZHRoICYgZm9ybWF0XG4gICAgICAgIGxldCB3aWR0aDtcbiAgICAgICAgbGV0IGhlaWdodDtcbiAgICAgICAgaWYgKG9wdGlvbnM/LnRlbnNvckxheW91dCAhPT0gdW5kZWZpbmVkICYmIG9wdGlvbnMudGVuc29yTGF5b3V0ID09PSAnTkhXQycpIHtcbiAgICAgICAgICAgIHdpZHRoID0gdGVuc29yLmRpbXNbMl07XG4gICAgICAgICAgICBoZWlnaHQgPSB0ZW5zb3IuZGltc1szXTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIC8vIERlZmF1bHQgbGF5b3V0IGlzIE5DV0hcbiAgICAgICAgICAgIHdpZHRoID0gdGVuc29yLmRpbXNbM107XG4gICAgICAgICAgICBoZWlnaHQgPSB0ZW5zb3IuZGltc1syXTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBpbnB1dGZvcm1hdCA9IG9wdGlvbnM/LmZvcm1hdCAhPT0gdW5kZWZpbmVkID8gb3B0aW9ucy5mb3JtYXQgOiAnUkdCJztcbiAgICAgICAgY29uc3Qgbm9ybSA9IG9wdGlvbnM/Lm5vcm07XG4gICAgICAgIGxldCBub3JtTWVhbjtcbiAgICAgICAgbGV0IG5vcm1CaWFzO1xuICAgICAgICBpZiAobm9ybSA9PT0gdW5kZWZpbmVkIHx8IG5vcm0ubWVhbiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBub3JtTWVhbiA9IFsyNTUsIDI1NSwgMjU1LCAyNTVdO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBub3JtLm1lYW4gPT09ICdudW1iZXInKSB7XG4gICAgICAgICAgICAgICAgbm9ybU1lYW4gPSBbbm9ybS5tZWFuLCBub3JtLm1lYW4sIG5vcm0ubWVhbiwgbm9ybS5tZWFuXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIG5vcm1NZWFuID0gW25vcm0ubWVhblswXSwgbm9ybS5tZWFuWzFdLCBub3JtLm1lYW5bMl0sIDBdO1xuICAgICAgICAgICAgICAgIGlmIChub3JtLm1lYW5bM10gIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICBub3JtTWVhblszXSA9IG5vcm0ubWVhblszXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG5vcm0gPT09IHVuZGVmaW5lZCB8fCBub3JtLmJpYXMgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgbm9ybUJpYXMgPSBbMCwgMCwgMCwgMF07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIG5vcm0uYmlhcyA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgICAgICBub3JtQmlhcyA9IFtub3JtLmJpYXMsIG5vcm0uYmlhcywgbm9ybS5iaWFzLCBub3JtLmJpYXNdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgbm9ybUJpYXMgPSBbbm9ybS5iaWFzWzBdLCBub3JtLmJpYXNbMV0sIG5vcm0uYmlhc1syXSwgMF07XG4gICAgICAgICAgICAgICAgaWYgKG5vcm0uYmlhc1szXSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIG5vcm1CaWFzWzNdID0gbm9ybS5iaWFzWzNdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBzdHJpZGUgPSBoZWlnaHQgKiB3aWR0aDtcbiAgICAgICAgLy8gRGVmYXVsdCBwb2ludGVyIGFzc2lnbm1lbnRzXG4gICAgICAgIGxldCByVGVuc29yUG9pbnRlciA9IDAsIGdUZW5zb3JQb2ludGVyID0gc3RyaWRlLCBiVGVuc29yUG9pbnRlciA9IHN0cmlkZSAqIDIsIGFUZW5zb3JQb2ludGVyID0gLTE7XG4gICAgICAgIC8vIFVwZGF0aW5nIHRoZSBwb2ludGVyIGFzc2lnbm1lbnRzIGJhc2VkIG9uIHRoZSBpbnB1dCBpbWFnZSBmb3JtYXRcbiAgICAgICAgaWYgKGlucHV0Zm9ybWF0ID09PSAnUkdCQScpIHtcbiAgICAgICAgICAgIHJUZW5zb3JQb2ludGVyID0gMDtcbiAgICAgICAgICAgIGdUZW5zb3JQb2ludGVyID0gc3RyaWRlO1xuICAgICAgICAgICAgYlRlbnNvclBvaW50ZXIgPSBzdHJpZGUgKiAyO1xuICAgICAgICAgICAgYVRlbnNvclBvaW50ZXIgPSBzdHJpZGUgKiAzO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGlucHV0Zm9ybWF0ID09PSAnUkdCJykge1xuICAgICAgICAgICAgclRlbnNvclBvaW50ZXIgPSAwO1xuICAgICAgICAgICAgZ1RlbnNvclBvaW50ZXIgPSBzdHJpZGU7XG4gICAgICAgICAgICBiVGVuc29yUG9pbnRlciA9IHN0cmlkZSAqIDI7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoaW5wdXRmb3JtYXQgPT09ICdSQkcnKSB7XG4gICAgICAgICAgICByVGVuc29yUG9pbnRlciA9IDA7XG4gICAgICAgICAgICBiVGVuc29yUG9pbnRlciA9IHN0cmlkZTtcbiAgICAgICAgICAgIGdUZW5zb3JQb2ludGVyID0gc3RyaWRlICogMjtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGhlaWdodDsgaSsrKSB7XG4gICAgICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IHdpZHRoOyBqKyspIHtcbiAgICAgICAgICAgICAgICBjb25zdCBSID0gKHRlbnNvci5kYXRhW3JUZW5zb3JQb2ludGVyKytdIC0gbm9ybUJpYXNbMF0pICogbm9ybU1lYW5bMF07IC8vIFIgdmFsdWVcbiAgICAgICAgICAgICAgICBjb25zdCBHID0gKHRlbnNvci5kYXRhW2dUZW5zb3JQb2ludGVyKytdIC0gbm9ybUJpYXNbMV0pICogbm9ybU1lYW5bMV07IC8vIEcgdmFsdWVcbiAgICAgICAgICAgICAgICBjb25zdCBCID0gKHRlbnNvci5kYXRhW2JUZW5zb3JQb2ludGVyKytdIC0gbm9ybUJpYXNbMl0pICogbm9ybU1lYW5bMl07IC8vIEIgdmFsdWVcbiAgICAgICAgICAgICAgICBjb25zdCBBID0gYVRlbnNvclBvaW50ZXIgPT09IC0xID8gMjU1IDogKHRlbnNvci5kYXRhW2FUZW5zb3JQb2ludGVyKytdIC0gbm9ybUJpYXNbM10pICogbm9ybU1lYW5bM107IC8vIEEgdmFsdWVcbiAgICAgICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L3Jlc3RyaWN0LXBsdXMtb3BlcmFuZHNcbiAgICAgICAgICAgICAgICBwaXhlbHMyRENvbnRleHQuZmlsbFN0eWxlID0gJ3JnYmEoJyArIFIgKyAnLCcgKyBHICsgJywnICsgQiArICcsJyArIEEgKyAnKSc7XG4gICAgICAgICAgICAgICAgcGl4ZWxzMkRDb250ZXh0LmZpbGxSZWN0KGosIGksIDEsIDEpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmICgndG9EYXRhVVJMJyBpbiBjYW52YXMpIHtcbiAgICAgICAgICAgIHJldHVybiBjYW52YXMudG9EYXRhVVJMKCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ3RvRGF0YVVSTCBpcyBub3Qgc3VwcG9ydGVkJyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQ2FuIG5vdCBhY2Nlc3MgaW1hZ2UgZGF0YScpO1xuICAgIH1cbn07XG4vKipcbiAqIGltcGxlbWVudGF0aW9uIG9mIFRlbnNvci50b0ltYWdlRGF0YSgpXG4gKi9cbmV4cG9ydCBjb25zdCB0ZW5zb3JUb0ltYWdlRGF0YSA9ICh0ZW5zb3IsIG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCBwaXhlbHMyRENvbnRleHQgPSB0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnXG4gICAgICAgID8gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnY2FudmFzJykuZ2V0Q29udGV4dCgnMmQnKVxuICAgICAgICA6IG5ldyBPZmZzY3JlZW5DYW52YXMoMSwgMSkuZ2V0Q29udGV4dCgnMmQnKTtcbiAgICBsZXQgaW1hZ2U7XG4gICAgaWYgKHBpeGVsczJEQ29udGV4dCAhPSBudWxsKSB7XG4gICAgICAgIC8vIERlZmF1bHQgdmFsdWVzIGZvciBoZWlnaHQgYW5kIHdpZHRoICYgZm9ybWF0XG4gICAgICAgIGxldCB3aWR0aDtcbiAgICAgICAgbGV0IGhlaWdodDtcbiAgICAgICAgbGV0IGNoYW5uZWxzO1xuICAgICAgICBpZiAob3B0aW9ucz8udGVuc29yTGF5b3V0ICE9PSB1bmRlZmluZWQgJiYgb3B0aW9ucy50ZW5zb3JMYXlvdXQgPT09ICdOSFdDJykge1xuICAgICAgICAgICAgd2lkdGggPSB0ZW5zb3IuZGltc1syXTtcbiAgICAgICAgICAgIGhlaWdodCA9IHRlbnNvci5kaW1zWzFdO1xuICAgICAgICAgICAgY2hhbm5lbHMgPSB0ZW5zb3IuZGltc1szXTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIC8vIERlZmF1bHQgbGF5b3V0IGlzIE5DV0hcbiAgICAgICAgICAgIHdpZHRoID0gdGVuc29yLmRpbXNbM107XG4gICAgICAgICAgICBoZWlnaHQgPSB0ZW5zb3IuZGltc1syXTtcbiAgICAgICAgICAgIGNoYW5uZWxzID0gdGVuc29yLmRpbXNbMV07XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaW5wdXRmb3JtYXQgPSBvcHRpb25zICE9PSB1bmRlZmluZWQgPyAob3B0aW9ucy5mb3JtYXQgIT09IHVuZGVmaW5lZCA/IG9wdGlvbnMuZm9ybWF0IDogJ1JHQicpIDogJ1JHQic7XG4gICAgICAgIGNvbnN0IG5vcm0gPSBvcHRpb25zPy5ub3JtO1xuICAgICAgICBsZXQgbm9ybU1lYW47XG4gICAgICAgIGxldCBub3JtQmlhcztcbiAgICAgICAgaWYgKG5vcm0gPT09IHVuZGVmaW5lZCB8fCBub3JtLm1lYW4gPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgbm9ybU1lYW4gPSBbMjU1LCAyNTUsIDI1NSwgMjU1XTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygbm9ybS5tZWFuID09PSAnbnVtYmVyJykge1xuICAgICAgICAgICAgICAgIG5vcm1NZWFuID0gW25vcm0ubWVhbiwgbm9ybS5tZWFuLCBub3JtLm1lYW4sIG5vcm0ubWVhbl07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBub3JtTWVhbiA9IFtub3JtLm1lYW5bMF0sIG5vcm0ubWVhblsxXSwgbm9ybS5tZWFuWzJdLCAyNTVdO1xuICAgICAgICAgICAgICAgIGlmIChub3JtLm1lYW5bM10gIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICBub3JtTWVhblszXSA9IG5vcm0ubWVhblszXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG5vcm0gPT09IHVuZGVmaW5lZCB8fCBub3JtLmJpYXMgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgbm9ybUJpYXMgPSBbMCwgMCwgMCwgMF07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIG5vcm0uYmlhcyA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgICAgICBub3JtQmlhcyA9IFtub3JtLmJpYXMsIG5vcm0uYmlhcywgbm9ybS5iaWFzLCBub3JtLmJpYXNdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgbm9ybUJpYXMgPSBbbm9ybS5iaWFzWzBdLCBub3JtLmJpYXNbMV0sIG5vcm0uYmlhc1syXSwgMF07XG4gICAgICAgICAgICAgICAgaWYgKG5vcm0uYmlhc1szXSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIG5vcm1CaWFzWzNdID0gbm9ybS5iaWFzWzNdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBzdHJpZGUgPSBoZWlnaHQgKiB3aWR0aDtcbiAgICAgICAgaWYgKG9wdGlvbnMgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgaWYgKChvcHRpb25zLmZvcm1hdCAhPT0gdW5kZWZpbmVkICYmIGNoYW5uZWxzID09PSA0ICYmIG9wdGlvbnMuZm9ybWF0ICE9PSAnUkdCQScpIHx8XG4gICAgICAgICAgICAgICAgKGNoYW5uZWxzID09PSAzICYmIG9wdGlvbnMuZm9ybWF0ICE9PSAnUkdCJyAmJiBvcHRpb25zLmZvcm1hdCAhPT0gJ0JHUicpKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGVuc29yIGZvcm1hdCBkb2Vzbid0IG1hdGNoIGlucHV0IHRlbnNvciBkaW1zXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIC8vIERlZmF1bHQgcG9pbnRlciBhc3NpZ25tZW50c1xuICAgICAgICBjb25zdCBzdGVwID0gNDtcbiAgICAgICAgbGV0IHJJbWFnZVBvaW50ZXIgPSAwLCBnSW1hZ2VQb2ludGVyID0gMSwgYkltYWdlUG9pbnRlciA9IDIsIGFJbWFnZVBvaW50ZXIgPSAzO1xuICAgICAgICBsZXQgclRlbnNvclBvaW50ZXIgPSAwLCBnVGVuc29yUG9pbnRlciA9IHN0cmlkZSwgYlRlbnNvclBvaW50ZXIgPSBzdHJpZGUgKiAyLCBhVGVuc29yUG9pbnRlciA9IC0xO1xuICAgICAgICAvLyBVcGRhdGluZyB0aGUgcG9pbnRlciBhc3NpZ25tZW50cyBiYXNlZCBvbiB0aGUgaW5wdXQgaW1hZ2UgZm9ybWF0XG4gICAgICAgIGlmIChpbnB1dGZvcm1hdCA9PT0gJ1JHQkEnKSB7XG4gICAgICAgICAgICByVGVuc29yUG9pbnRlciA9IDA7XG4gICAgICAgICAgICBnVGVuc29yUG9pbnRlciA9IHN0cmlkZTtcbiAgICAgICAgICAgIGJUZW5zb3JQb2ludGVyID0gc3RyaWRlICogMjtcbiAgICAgICAgICAgIGFUZW5zb3JQb2ludGVyID0gc3RyaWRlICogMztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpbnB1dGZvcm1hdCA9PT0gJ1JHQicpIHtcbiAgICAgICAgICAgIHJUZW5zb3JQb2ludGVyID0gMDtcbiAgICAgICAgICAgIGdUZW5zb3JQb2ludGVyID0gc3RyaWRlO1xuICAgICAgICAgICAgYlRlbnNvclBvaW50ZXIgPSBzdHJpZGUgKiAyO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGlucHV0Zm9ybWF0ID09PSAnUkJHJykge1xuICAgICAgICAgICAgclRlbnNvclBvaW50ZXIgPSAwO1xuICAgICAgICAgICAgYlRlbnNvclBvaW50ZXIgPSBzdHJpZGU7XG4gICAgICAgICAgICBnVGVuc29yUG9pbnRlciA9IHN0cmlkZSAqIDI7XG4gICAgICAgIH1cbiAgICAgICAgaW1hZ2UgPSBwaXhlbHMyRENvbnRleHQuY3JlYXRlSW1hZ2VEYXRhKHdpZHRoLCBoZWlnaHQpO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGhlaWdodCAqIHdpZHRoOyBySW1hZ2VQb2ludGVyICs9IHN0ZXAsIGdJbWFnZVBvaW50ZXIgKz0gc3RlcCwgYkltYWdlUG9pbnRlciArPSBzdGVwLCBhSW1hZ2VQb2ludGVyICs9IHN0ZXAsIGkrKykge1xuICAgICAgICAgICAgaW1hZ2UuZGF0YVtySW1hZ2VQb2ludGVyXSA9ICh0ZW5zb3IuZGF0YVtyVGVuc29yUG9pbnRlcisrXSAtIG5vcm1CaWFzWzBdKSAqIG5vcm1NZWFuWzBdOyAvLyBSIHZhbHVlXG4gICAgICAgICAgICBpbWFnZS5kYXRhW2dJbWFnZVBvaW50ZXJdID0gKHRlbnNvci5kYXRhW2dUZW5zb3JQb2ludGVyKytdIC0gbm9ybUJpYXNbMV0pICogbm9ybU1lYW5bMV07IC8vIEcgdmFsdWVcbiAgICAgICAgICAgIGltYWdlLmRhdGFbYkltYWdlUG9pbnRlcl0gPSAodGVuc29yLmRhdGFbYlRlbnNvclBvaW50ZXIrK10gLSBub3JtQmlhc1syXSkgKiBub3JtTWVhblsyXTsgLy8gQiB2YWx1ZVxuICAgICAgICAgICAgaW1hZ2UuZGF0YVthSW1hZ2VQb2ludGVyXSA9XG4gICAgICAgICAgICAgICAgYVRlbnNvclBvaW50ZXIgPT09IC0xID8gMjU1IDogKHRlbnNvci5kYXRhW2FUZW5zb3JQb2ludGVyKytdIC0gbm9ybUJpYXNbM10pICogbm9ybU1lYW5bM107IC8vIEEgdmFsdWVcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYW4gbm90IGFjY2VzcyBpbWFnZSBkYXRhJyk7XG4gICAgfVxuICAgIHJldHVybiBpbWFnZTtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD10ZW5zb3ItY29udmVyc2lvbi1pbXBsLmpzLm1hcCJdLCJuYW1lcyI6WyJ0ZW5zb3JUb0RhdGFVUkwiLCJ0ZW5zb3IiLCJvcHRpb25zIiwiY2FudmFzIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiT2Zmc2NyZWVuQ2FudmFzIiwid2lkdGgiLCJkaW1zIiwiaGVpZ2h0IiwicGl4ZWxzMkRDb250ZXh0IiwiZ2V0Q29udGV4dCIsInRlbnNvckxheW91dCIsInVuZGVmaW5lZCIsImlucHV0Zm9ybWF0IiwiZm9ybWF0Iiwibm9ybSIsIm5vcm1NZWFuIiwibm9ybUJpYXMiLCJtZWFuIiwiYmlhcyIsInN0cmlkZSIsInJUZW5zb3JQb2ludGVyIiwiZ1RlbnNvclBvaW50ZXIiLCJiVGVuc29yUG9pbnRlciIsImFUZW5zb3JQb2ludGVyIiwiaSIsImoiLCJSIiwiZGF0YSIsIkciLCJCIiwiQSIsImZpbGxTdHlsZSIsImZpbGxSZWN0IiwidG9EYXRhVVJMIiwiRXJyb3IiLCJ0ZW5zb3JUb0ltYWdlRGF0YSIsImltYWdlIiwiY2hhbm5lbHMiLCJzdGVwIiwickltYWdlUG9pbnRlciIsImdJbWFnZVBvaW50ZXIiLCJiSW1hZ2VQb2ludGVyIiwiYUltYWdlUG9pbnRlciIsImNyZWF0ZUltYWdlRGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-conversion-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-conversion.js":
/*!***********************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/tensor-conversion.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n //# sourceMappingURL=tensor-conversion.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL3RlbnNvci1jb252ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7QUFBQSw0REFBNEQ7QUFDNUQsa0NBQWtDO0FBQ3hCLENBQ1YsNkNBQTZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1hZ2UtdGV4dC1zdHVkaW8tMDMvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL3RlbnNvci1jb252ZXJzaW9uLmpzPzAzYjMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG5leHBvcnQge307XG4vLyMgc291cmNlTWFwcGluZ1VSTD10ZW5zb3ItY29udmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-conversion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-factory-impl.js":
/*!*************************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/tensor-factory-impl.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bufferToTensor: () => (/* binding */ bufferToTensor),\n/* harmony export */   tensorFromGpuBuffer: () => (/* binding */ tensorFromGpuBuffer),\n/* harmony export */   tensorFromImage: () => (/* binding */ tensorFromImage),\n/* harmony export */   tensorFromMLTensor: () => (/* binding */ tensorFromMLTensor),\n/* harmony export */   tensorFromPinnedBuffer: () => (/* binding */ tensorFromPinnedBuffer),\n/* harmony export */   tensorFromTexture: () => (/* binding */ tensorFromTexture)\n/* harmony export */ });\n/* harmony import */ var _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tensor-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n/**\n * Create a new tensor object from image object\n *\n * @param buffer - Extracted image buffer data - assuming RGBA format\n * @param imageFormat - input image configuration - required configurations height, width, format\n * @param tensorFormat - output tensor configuration - Default is RGB format\n */ const bufferToTensor = (buffer, options)=>{\n    if (buffer === undefined) {\n        throw new Error(\"Image buffer must be defined\");\n    }\n    if (options.height === undefined || options.width === undefined) {\n        throw new Error(\"Image height and width must be defined\");\n    }\n    if (options.tensorLayout === \"NHWC\") {\n        throw new Error(\"NHWC Tensor layout is not supported yet\");\n    }\n    const { height, width } = options;\n    const norm = options.norm ?? {\n        mean: 255,\n        bias: 0\n    };\n    let normMean;\n    let normBias;\n    if (typeof norm.mean === \"number\") {\n        normMean = [\n            norm.mean,\n            norm.mean,\n            norm.mean,\n            norm.mean\n        ];\n    } else {\n        normMean = [\n            norm.mean[0],\n            norm.mean[1],\n            norm.mean[2],\n            norm.mean[3] ?? 255\n        ];\n    }\n    if (typeof norm.bias === \"number\") {\n        normBias = [\n            norm.bias,\n            norm.bias,\n            norm.bias,\n            norm.bias\n        ];\n    } else {\n        normBias = [\n            norm.bias[0],\n            norm.bias[1],\n            norm.bias[2],\n            norm.bias[3] ?? 0\n        ];\n    }\n    const inputformat = options.format !== undefined ? options.format : \"RGBA\";\n    // default value is RGBA since imagedata and HTMLImageElement uses it\n    const outputformat = options.tensorFormat !== undefined ? options.tensorFormat !== undefined ? options.tensorFormat : \"RGB\" : \"RGB\";\n    const stride = height * width;\n    const float32Data = outputformat === \"RGBA\" ? new Float32Array(stride * 4) : new Float32Array(stride * 3);\n    // Default pointer assignments\n    let step = 4, rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n    let rTensorPointer = 0, gTensorPointer = stride, bTensorPointer = stride * 2, aTensorPointer = -1;\n    // Updating the pointer assignments based on the input image format\n    if (inputformat === \"RGB\") {\n        step = 3;\n        rImagePointer = 0;\n        gImagePointer = 1;\n        bImagePointer = 2;\n        aImagePointer = -1;\n    }\n    // Updating the pointer assignments based on the output tensor format\n    if (outputformat === \"RGBA\") {\n        aTensorPointer = stride * 3;\n    } else if (outputformat === \"RBG\") {\n        rTensorPointer = 0;\n        bTensorPointer = stride;\n        gTensorPointer = stride * 2;\n    } else if (outputformat === \"BGR\") {\n        bTensorPointer = 0;\n        gTensorPointer = stride;\n        rTensorPointer = stride * 2;\n    }\n    for(let i = 0; i < stride; i++, rImagePointer += step, bImagePointer += step, gImagePointer += step, aImagePointer += step){\n        float32Data[rTensorPointer++] = (buffer[rImagePointer] + normBias[0]) / normMean[0];\n        float32Data[gTensorPointer++] = (buffer[gImagePointer] + normBias[1]) / normMean[1];\n        float32Data[bTensorPointer++] = (buffer[bImagePointer] + normBias[2]) / normMean[2];\n        if (aTensorPointer !== -1 && aImagePointer !== -1) {\n            float32Data[aTensorPointer++] = (buffer[aImagePointer] + normBias[3]) / normMean[3];\n        }\n    }\n    // Float32Array -> ort.Tensor\n    const outputTensor = outputformat === \"RGBA\" ? new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor(\"float32\", float32Data, [\n        1,\n        4,\n        height,\n        width\n    ]) : new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor(\"float32\", float32Data, [\n        1,\n        3,\n        height,\n        width\n    ]);\n    return outputTensor;\n};\n/**\n * implementation of Tensor.fromImage().\n */ const tensorFromImage = async (image, options)=>{\n    // checking the type of image object\n    const isHTMLImageEle = typeof HTMLImageElement !== \"undefined\" && image instanceof HTMLImageElement;\n    const isImageDataEle = typeof ImageData !== \"undefined\" && image instanceof ImageData;\n    const isImageBitmap = typeof ImageBitmap !== \"undefined\" && image instanceof ImageBitmap;\n    const isString = typeof image === \"string\";\n    let data;\n    let bufferToTensorOptions = options ?? {};\n    const createCanvas = ()=>{\n        if (typeof document !== \"undefined\") {\n            return document.createElement(\"canvas\");\n        } else if (typeof OffscreenCanvas !== \"undefined\") {\n            return new OffscreenCanvas(1, 1);\n        } else {\n            throw new Error(\"Canvas is not supported\");\n        }\n    };\n    const createCanvasContext = (canvas)=>{\n        if (typeof HTMLCanvasElement !== \"undefined\" && canvas instanceof HTMLCanvasElement) {\n            return canvas.getContext(\"2d\");\n        } else if (canvas instanceof OffscreenCanvas) {\n            return canvas.getContext(\"2d\");\n        } else {\n            return null;\n        }\n    };\n    // filling and checking image configuration options\n    if (isHTMLImageEle) {\n        // HTMLImageElement - image object - format is RGBA by default\n        const canvas = createCanvas();\n        canvas.width = image.width;\n        canvas.height = image.height;\n        const pixels2DContext = createCanvasContext(canvas);\n        if (pixels2DContext != null) {\n            let height = image.height;\n            let width = image.width;\n            if (options !== undefined && options.resizedHeight !== undefined && options.resizedWidth !== undefined) {\n                height = options.resizedHeight;\n                width = options.resizedWidth;\n            }\n            if (options !== undefined) {\n                bufferToTensorOptions = options;\n                if (options.tensorFormat !== undefined) {\n                    throw new Error(\"Image input config format must be RGBA for HTMLImageElement\");\n                } else {\n                    bufferToTensorOptions.tensorFormat = \"RGBA\";\n                }\n                bufferToTensorOptions.height = height;\n                bufferToTensorOptions.width = width;\n            } else {\n                bufferToTensorOptions.tensorFormat = \"RGBA\";\n                bufferToTensorOptions.height = height;\n                bufferToTensorOptions.width = width;\n            }\n            pixels2DContext.drawImage(image, 0, 0);\n            data = pixels2DContext.getImageData(0, 0, width, height).data;\n        } else {\n            throw new Error(\"Can not access image data\");\n        }\n    } else if (isImageDataEle) {\n        let height;\n        let width;\n        if (options !== undefined && options.resizedWidth !== undefined && options.resizedHeight !== undefined) {\n            height = options.resizedHeight;\n            width = options.resizedWidth;\n        } else {\n            height = image.height;\n            width = image.width;\n        }\n        if (options !== undefined) {\n            bufferToTensorOptions = options;\n        }\n        bufferToTensorOptions.format = \"RGBA\";\n        bufferToTensorOptions.height = height;\n        bufferToTensorOptions.width = width;\n        if (options !== undefined) {\n            const tempCanvas = createCanvas();\n            tempCanvas.width = width;\n            tempCanvas.height = height;\n            const pixels2DContext = createCanvasContext(tempCanvas);\n            if (pixels2DContext != null) {\n                pixels2DContext.putImageData(image, 0, 0);\n                data = pixels2DContext.getImageData(0, 0, width, height).data;\n            } else {\n                throw new Error(\"Can not access image data\");\n            }\n        } else {\n            data = image.data;\n        }\n    } else if (isImageBitmap) {\n        // ImageBitmap - image object - format must be provided by user\n        if (options === undefined) {\n            throw new Error(\"Please provide image config with format for Imagebitmap\");\n        }\n        const canvas = createCanvas();\n        canvas.width = image.width;\n        canvas.height = image.height;\n        const pixels2DContext = createCanvasContext(canvas);\n        if (pixels2DContext != null) {\n            const height = image.height;\n            const width = image.width;\n            pixels2DContext.drawImage(image, 0, 0, width, height);\n            data = pixels2DContext.getImageData(0, 0, width, height).data;\n            bufferToTensorOptions.height = height;\n            bufferToTensorOptions.width = width;\n            return bufferToTensor(data, bufferToTensorOptions);\n        } else {\n            throw new Error(\"Can not access image data\");\n        }\n    } else if (isString) {\n        return new Promise((resolve, reject)=>{\n            const canvas = createCanvas();\n            const context = createCanvasContext(canvas);\n            if (!image || !context) {\n                return reject();\n            }\n            const newImage = new Image();\n            newImage.crossOrigin = \"Anonymous\";\n            newImage.src = image;\n            newImage.onload = ()=>{\n                canvas.width = newImage.width;\n                canvas.height = newImage.height;\n                context.drawImage(newImage, 0, 0, canvas.width, canvas.height);\n                const img = context.getImageData(0, 0, canvas.width, canvas.height);\n                bufferToTensorOptions.height = canvas.height;\n                bufferToTensorOptions.width = canvas.width;\n                resolve(bufferToTensor(img.data, bufferToTensorOptions));\n            };\n        });\n    } else {\n        throw new Error(\"Input data provided is not supported - aborted tensor creation\");\n    }\n    if (data !== undefined) {\n        return bufferToTensor(data, bufferToTensorOptions);\n    } else {\n        throw new Error(\"Input data provided is not supported - aborted tensor creation\");\n    }\n};\n/**\n * implementation of Tensor.fromTexture().\n */ const tensorFromTexture = (texture, options)=>{\n    const { width, height, download, dispose } = options;\n    // Always assume RGBAF32. TODO: support different texture format\n    const dims = [\n        1,\n        height,\n        width,\n        4\n    ];\n    return new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor({\n        location: \"texture\",\n        type: \"float32\",\n        texture,\n        dims,\n        download,\n        dispose\n    });\n};\n/**\n * implementation of Tensor.fromGpuBuffer().\n */ const tensorFromGpuBuffer = (gpuBuffer, options)=>{\n    const { dataType, dims, download, dispose } = options;\n    return new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor({\n        location: \"gpu-buffer\",\n        type: dataType ?? \"float32\",\n        gpuBuffer,\n        dims,\n        download,\n        dispose\n    });\n};\n/**\n * implementation of Tensor.fromMLTensor().\n */ const tensorFromMLTensor = (mlTensor, options)=>{\n    const { dataType, dims, download, dispose } = options;\n    return new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor({\n        location: \"ml-tensor\",\n        type: dataType ?? \"float32\",\n        mlTensor,\n        dims,\n        download,\n        dispose\n    });\n};\n/**\n * implementation of Tensor.fromPinnedBuffer().\n */ const tensorFromPinnedBuffer = (type, buffer, dims)=>new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor({\n        location: \"cpu-pinned\",\n        type,\n        data: buffer,\n        dims: dims ?? [\n            buffer.length\n        ]\n    }); //# sourceMappingURL=tensor-factory-impl.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL3RlbnNvci1mYWN0b3J5LWltcGwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBLDREQUE0RDtBQUM1RCxrQ0FBa0M7QUFDUTtBQUMxQzs7Ozs7O0NBTUMsR0FDTSxNQUFNQyxpQkFBaUIsQ0FBQ0MsUUFBUUM7SUFDbkMsSUFBSUQsV0FBV0UsV0FBVztRQUN0QixNQUFNLElBQUlDLE1BQU07SUFDcEI7SUFDQSxJQUFJRixRQUFRRyxNQUFNLEtBQUtGLGFBQWFELFFBQVFJLEtBQUssS0FBS0gsV0FBVztRQUM3RCxNQUFNLElBQUlDLE1BQU07SUFDcEI7SUFDQSxJQUFJRixRQUFRSyxZQUFZLEtBQUssUUFBUTtRQUNqQyxNQUFNLElBQUlILE1BQU07SUFDcEI7SUFDQSxNQUFNLEVBQUVDLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUdKO0lBQzFCLE1BQU1NLE9BQU9OLFFBQVFNLElBQUksSUFBSTtRQUFFQyxNQUFNO1FBQUtDLE1BQU07SUFBRTtJQUNsRCxJQUFJQztJQUNKLElBQUlDO0lBQ0osSUFBSSxPQUFPSixLQUFLQyxJQUFJLEtBQUssVUFBVTtRQUMvQkUsV0FBVztZQUFDSCxLQUFLQyxJQUFJO1lBQUVELEtBQUtDLElBQUk7WUFBRUQsS0FBS0MsSUFBSTtZQUFFRCxLQUFLQyxJQUFJO1NBQUM7SUFDM0QsT0FDSztRQUNERSxXQUFXO1lBQUNILEtBQUtDLElBQUksQ0FBQyxFQUFFO1lBQUVELEtBQUtDLElBQUksQ0FBQyxFQUFFO1lBQUVELEtBQUtDLElBQUksQ0FBQyxFQUFFO1lBQUVELEtBQUtDLElBQUksQ0FBQyxFQUFFLElBQUk7U0FBSTtJQUM5RTtJQUNBLElBQUksT0FBT0QsS0FBS0UsSUFBSSxLQUFLLFVBQVU7UUFDL0JFLFdBQVc7WUFBQ0osS0FBS0UsSUFBSTtZQUFFRixLQUFLRSxJQUFJO1lBQUVGLEtBQUtFLElBQUk7WUFBRUYsS0FBS0UsSUFBSTtTQUFDO0lBQzNELE9BQ0s7UUFDREUsV0FBVztZQUFDSixLQUFLRSxJQUFJLENBQUMsRUFBRTtZQUFFRixLQUFLRSxJQUFJLENBQUMsRUFBRTtZQUFFRixLQUFLRSxJQUFJLENBQUMsRUFBRTtZQUFFRixLQUFLRSxJQUFJLENBQUMsRUFBRSxJQUFJO1NBQUU7SUFDNUU7SUFDQSxNQUFNRyxjQUFjWCxRQUFRWSxNQUFNLEtBQUtYLFlBQVlELFFBQVFZLE1BQU0sR0FBRztJQUNwRSxxRUFBcUU7SUFDckUsTUFBTUMsZUFBZWIsUUFBUWMsWUFBWSxLQUFLYixZQUFhRCxRQUFRYyxZQUFZLEtBQUtiLFlBQVlELFFBQVFjLFlBQVksR0FBRyxRQUFTO0lBQ2hJLE1BQU1DLFNBQVNaLFNBQVNDO0lBQ3hCLE1BQU1ZLGNBQWNILGlCQUFpQixTQUFTLElBQUlJLGFBQWFGLFNBQVMsS0FBSyxJQUFJRSxhQUFhRixTQUFTO0lBQ3ZHLDhCQUE4QjtJQUM5QixJQUFJRyxPQUFPLEdBQUdDLGdCQUFnQixHQUFHQyxnQkFBZ0IsR0FBR0MsZ0JBQWdCLEdBQUdDLGdCQUFnQjtJQUN2RixJQUFJQyxpQkFBaUIsR0FBR0MsaUJBQWlCVCxRQUFRVSxpQkFBaUJWLFNBQVMsR0FBR1csaUJBQWlCLENBQUM7SUFDaEcsbUVBQW1FO0lBQ25FLElBQUlmLGdCQUFnQixPQUFPO1FBQ3ZCTyxPQUFPO1FBQ1BDLGdCQUFnQjtRQUNoQkMsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQixDQUFDO0lBQ3JCO0lBQ0EscUVBQXFFO0lBQ3JFLElBQUlULGlCQUFpQixRQUFRO1FBQ3pCYSxpQkFBaUJYLFNBQVM7SUFDOUIsT0FDSyxJQUFJRixpQkFBaUIsT0FBTztRQUM3QlUsaUJBQWlCO1FBQ2pCRSxpQkFBaUJWO1FBQ2pCUyxpQkFBaUJULFNBQVM7SUFDOUIsT0FDSyxJQUFJRixpQkFBaUIsT0FBTztRQUM3QlksaUJBQWlCO1FBQ2pCRCxpQkFBaUJUO1FBQ2pCUSxpQkFBaUJSLFNBQVM7SUFDOUI7SUFDQSxJQUFLLElBQUlZLElBQUksR0FBR0EsSUFBSVosUUFBUVksS0FBS1IsaUJBQWlCRCxNQUFNRyxpQkFBaUJILE1BQU1FLGlCQUFpQkYsTUFBTUksaUJBQWlCSixLQUFNO1FBQ3pIRixXQUFXLENBQUNPLGlCQUFpQixHQUFHLENBQUN4QixNQUFNLENBQUNvQixjQUFjLEdBQUdULFFBQVEsQ0FBQyxFQUFFLElBQUlELFFBQVEsQ0FBQyxFQUFFO1FBQ25GTyxXQUFXLENBQUNRLGlCQUFpQixHQUFHLENBQUN6QixNQUFNLENBQUNxQixjQUFjLEdBQUdWLFFBQVEsQ0FBQyxFQUFFLElBQUlELFFBQVEsQ0FBQyxFQUFFO1FBQ25GTyxXQUFXLENBQUNTLGlCQUFpQixHQUFHLENBQUMxQixNQUFNLENBQUNzQixjQUFjLEdBQUdYLFFBQVEsQ0FBQyxFQUFFLElBQUlELFFBQVEsQ0FBQyxFQUFFO1FBQ25GLElBQUlpQixtQkFBbUIsQ0FBQyxLQUFLSixrQkFBa0IsQ0FBQyxHQUFHO1lBQy9DTixXQUFXLENBQUNVLGlCQUFpQixHQUFHLENBQUMzQixNQUFNLENBQUN1QixjQUFjLEdBQUdaLFFBQVEsQ0FBQyxFQUFFLElBQUlELFFBQVEsQ0FBQyxFQUFFO1FBQ3ZGO0lBQ0o7SUFDQSw2QkFBNkI7SUFDN0IsTUFBTW1CLGVBQWVmLGlCQUFpQixTQUNoQyxJQUFJaEIsbURBQU1BLENBQUMsV0FBV21CLGFBQWE7UUFBQztRQUFHO1FBQUdiO1FBQVFDO0tBQU0sSUFDeEQsSUFBSVAsbURBQU1BLENBQUMsV0FBV21CLGFBQWE7UUFBQztRQUFHO1FBQUdiO1FBQVFDO0tBQU07SUFDOUQsT0FBT3dCO0FBQ1gsRUFBRTtBQUNGOztDQUVDLEdBQ00sTUFBTUMsa0JBQWtCLE9BQU9DLE9BQU85QjtJQUN6QyxvQ0FBb0M7SUFDcEMsTUFBTStCLGlCQUFpQixPQUFPQyxxQkFBcUIsZUFBZUYsaUJBQWlCRTtJQUNuRixNQUFNQyxpQkFBaUIsT0FBT0MsY0FBYyxlQUFlSixpQkFBaUJJO0lBQzVFLE1BQU1DLGdCQUFnQixPQUFPQyxnQkFBZ0IsZUFBZU4saUJBQWlCTTtJQUM3RSxNQUFNQyxXQUFXLE9BQU9QLFVBQVU7SUFDbEMsSUFBSVE7SUFDSixJQUFJQyx3QkFBd0J2QyxXQUFXLENBQUM7SUFDeEMsTUFBTXdDLGVBQWU7UUFDakIsSUFBSSxPQUFPQyxhQUFhLGFBQWE7WUFDakMsT0FBT0EsU0FBU0MsYUFBYSxDQUFDO1FBQ2xDLE9BQ0ssSUFBSSxPQUFPQyxvQkFBb0IsYUFBYTtZQUM3QyxPQUFPLElBQUlBLGdCQUFnQixHQUFHO1FBQ2xDLE9BQ0s7WUFDRCxNQUFNLElBQUl6QyxNQUFNO1FBQ3BCO0lBQ0o7SUFDQSxNQUFNMEMsc0JBQXNCLENBQUNDO1FBQ3pCLElBQUksT0FBT0Msc0JBQXNCLGVBQWVELGtCQUFrQkMsbUJBQW1CO1lBQ2pGLE9BQU9ELE9BQU9FLFVBQVUsQ0FBQztRQUM3QixPQUNLLElBQUlGLGtCQUFrQkYsaUJBQWlCO1lBQ3hDLE9BQU9FLE9BQU9FLFVBQVUsQ0FBQztRQUM3QixPQUNLO1lBQ0QsT0FBTztRQUNYO0lBQ0o7SUFDQSxtREFBbUQ7SUFDbkQsSUFBSWhCLGdCQUFnQjtRQUNoQiw4REFBOEQ7UUFDOUQsTUFBTWMsU0FBU0w7UUFDZkssT0FBT3pDLEtBQUssR0FBRzBCLE1BQU0xQixLQUFLO1FBQzFCeUMsT0FBTzFDLE1BQU0sR0FBRzJCLE1BQU0zQixNQUFNO1FBQzVCLE1BQU02QyxrQkFBa0JKLG9CQUFvQkM7UUFDNUMsSUFBSUcsbUJBQW1CLE1BQU07WUFDekIsSUFBSTdDLFNBQVMyQixNQUFNM0IsTUFBTTtZQUN6QixJQUFJQyxRQUFRMEIsTUFBTTFCLEtBQUs7WUFDdkIsSUFBSUosWUFBWUMsYUFBYUQsUUFBUWlELGFBQWEsS0FBS2hELGFBQWFELFFBQVFrRCxZQUFZLEtBQUtqRCxXQUFXO2dCQUNwR0UsU0FBU0gsUUFBUWlELGFBQWE7Z0JBQzlCN0MsUUFBUUosUUFBUWtELFlBQVk7WUFDaEM7WUFDQSxJQUFJbEQsWUFBWUMsV0FBVztnQkFDdkJzQyx3QkFBd0J2QztnQkFDeEIsSUFBSUEsUUFBUWMsWUFBWSxLQUFLYixXQUFXO29CQUNwQyxNQUFNLElBQUlDLE1BQU07Z0JBQ3BCLE9BQ0s7b0JBQ0RxQyxzQkFBc0J6QixZQUFZLEdBQUc7Z0JBQ3pDO2dCQUNBeUIsc0JBQXNCcEMsTUFBTSxHQUFHQTtnQkFDL0JvQyxzQkFBc0JuQyxLQUFLLEdBQUdBO1lBQ2xDLE9BQ0s7Z0JBQ0RtQyxzQkFBc0J6QixZQUFZLEdBQUc7Z0JBQ3JDeUIsc0JBQXNCcEMsTUFBTSxHQUFHQTtnQkFDL0JvQyxzQkFBc0JuQyxLQUFLLEdBQUdBO1lBQ2xDO1lBQ0E0QyxnQkFBZ0JHLFNBQVMsQ0FBQ3JCLE9BQU8sR0FBRztZQUNwQ1EsT0FBT1UsZ0JBQWdCSSxZQUFZLENBQUMsR0FBRyxHQUFHaEQsT0FBT0QsUUFBUW1DLElBQUk7UUFDakUsT0FDSztZQUNELE1BQU0sSUFBSXBDLE1BQU07UUFDcEI7SUFDSixPQUNLLElBQUkrQixnQkFBZ0I7UUFDckIsSUFBSTlCO1FBQ0osSUFBSUM7UUFDSixJQUFJSixZQUFZQyxhQUFhRCxRQUFRa0QsWUFBWSxLQUFLakQsYUFBYUQsUUFBUWlELGFBQWEsS0FBS2hELFdBQVc7WUFDcEdFLFNBQVNILFFBQVFpRCxhQUFhO1lBQzlCN0MsUUFBUUosUUFBUWtELFlBQVk7UUFDaEMsT0FDSztZQUNEL0MsU0FBUzJCLE1BQU0zQixNQUFNO1lBQ3JCQyxRQUFRMEIsTUFBTTFCLEtBQUs7UUFDdkI7UUFDQSxJQUFJSixZQUFZQyxXQUFXO1lBQ3ZCc0Msd0JBQXdCdkM7UUFDNUI7UUFDQXVDLHNCQUFzQjNCLE1BQU0sR0FBRztRQUMvQjJCLHNCQUFzQnBDLE1BQU0sR0FBR0E7UUFDL0JvQyxzQkFBc0JuQyxLQUFLLEdBQUdBO1FBQzlCLElBQUlKLFlBQVlDLFdBQVc7WUFDdkIsTUFBTW9ELGFBQWFiO1lBQ25CYSxXQUFXakQsS0FBSyxHQUFHQTtZQUNuQmlELFdBQVdsRCxNQUFNLEdBQUdBO1lBQ3BCLE1BQU02QyxrQkFBa0JKLG9CQUFvQlM7WUFDNUMsSUFBSUwsbUJBQW1CLE1BQU07Z0JBQ3pCQSxnQkFBZ0JNLFlBQVksQ0FBQ3hCLE9BQU8sR0FBRztnQkFDdkNRLE9BQU9VLGdCQUFnQkksWUFBWSxDQUFDLEdBQUcsR0FBR2hELE9BQU9ELFFBQVFtQyxJQUFJO1lBQ2pFLE9BQ0s7Z0JBQ0QsTUFBTSxJQUFJcEMsTUFBTTtZQUNwQjtRQUNKLE9BQ0s7WUFDRG9DLE9BQU9SLE1BQU1RLElBQUk7UUFDckI7SUFDSixPQUNLLElBQUlILGVBQWU7UUFDcEIsK0RBQStEO1FBQy9ELElBQUluQyxZQUFZQyxXQUFXO1lBQ3ZCLE1BQU0sSUFBSUMsTUFBTTtRQUNwQjtRQUNBLE1BQU0yQyxTQUFTTDtRQUNmSyxPQUFPekMsS0FBSyxHQUFHMEIsTUFBTTFCLEtBQUs7UUFDMUJ5QyxPQUFPMUMsTUFBTSxHQUFHMkIsTUFBTTNCLE1BQU07UUFDNUIsTUFBTTZDLGtCQUFrQkosb0JBQW9CQztRQUM1QyxJQUFJRyxtQkFBbUIsTUFBTTtZQUN6QixNQUFNN0MsU0FBUzJCLE1BQU0zQixNQUFNO1lBQzNCLE1BQU1DLFFBQVEwQixNQUFNMUIsS0FBSztZQUN6QjRDLGdCQUFnQkcsU0FBUyxDQUFDckIsT0FBTyxHQUFHLEdBQUcxQixPQUFPRDtZQUM5Q21DLE9BQU9VLGdCQUFnQkksWUFBWSxDQUFDLEdBQUcsR0FBR2hELE9BQU9ELFFBQVFtQyxJQUFJO1lBQzdEQyxzQkFBc0JwQyxNQUFNLEdBQUdBO1lBQy9Cb0Msc0JBQXNCbkMsS0FBSyxHQUFHQTtZQUM5QixPQUFPTixlQUFld0MsTUFBTUM7UUFDaEMsT0FDSztZQUNELE1BQU0sSUFBSXJDLE1BQU07UUFDcEI7SUFDSixPQUNLLElBQUltQyxVQUFVO1FBQ2YsT0FBTyxJQUFJa0IsUUFBUSxDQUFDQyxTQUFTQztZQUN6QixNQUFNWixTQUFTTDtZQUNmLE1BQU1rQixVQUFVZCxvQkFBb0JDO1lBQ3BDLElBQUksQ0FBQ2YsU0FBUyxDQUFDNEIsU0FBUztnQkFDcEIsT0FBT0Q7WUFDWDtZQUNBLE1BQU1FLFdBQVcsSUFBSUM7WUFDckJELFNBQVNFLFdBQVcsR0FBRztZQUN2QkYsU0FBU0csR0FBRyxHQUFHaEM7WUFDZjZCLFNBQVNJLE1BQU0sR0FBRztnQkFDZGxCLE9BQU96QyxLQUFLLEdBQUd1RCxTQUFTdkQsS0FBSztnQkFDN0J5QyxPQUFPMUMsTUFBTSxHQUFHd0QsU0FBU3hELE1BQU07Z0JBQy9CdUQsUUFBUVAsU0FBUyxDQUFDUSxVQUFVLEdBQUcsR0FBR2QsT0FBT3pDLEtBQUssRUFBRXlDLE9BQU8xQyxNQUFNO2dCQUM3RCxNQUFNNkQsTUFBTU4sUUFBUU4sWUFBWSxDQUFDLEdBQUcsR0FBR1AsT0FBT3pDLEtBQUssRUFBRXlDLE9BQU8xQyxNQUFNO2dCQUNsRW9DLHNCQUFzQnBDLE1BQU0sR0FBRzBDLE9BQU8xQyxNQUFNO2dCQUM1Q29DLHNCQUFzQm5DLEtBQUssR0FBR3lDLE9BQU96QyxLQUFLO2dCQUMxQ29ELFFBQVExRCxlQUFla0UsSUFBSTFCLElBQUksRUFBRUM7WUFDckM7UUFDSjtJQUNKLE9BQ0s7UUFDRCxNQUFNLElBQUlyQyxNQUFNO0lBQ3BCO0lBQ0EsSUFBSW9DLFNBQVNyQyxXQUFXO1FBQ3BCLE9BQU9ILGVBQWV3QyxNQUFNQztJQUNoQyxPQUNLO1FBQ0QsTUFBTSxJQUFJckMsTUFBTTtJQUNwQjtBQUNKLEVBQUU7QUFDRjs7Q0FFQyxHQUNNLE1BQU0rRCxvQkFBb0IsQ0FBQ0MsU0FBU2xFO0lBQ3ZDLE1BQU0sRUFBRUksS0FBSyxFQUFFRCxNQUFNLEVBQUVnRSxRQUFRLEVBQUVDLE9BQU8sRUFBRSxHQUFHcEU7SUFDN0MsZ0VBQWdFO0lBQ2hFLE1BQU1xRSxPQUFPO1FBQUM7UUFBR2xFO1FBQVFDO1FBQU87S0FBRTtJQUNsQyxPQUFPLElBQUlQLG1EQUFNQSxDQUFDO1FBQUV5RSxVQUFVO1FBQVdDLE1BQU07UUFBV0w7UUFBU0c7UUFBTUY7UUFBVUM7SUFBUTtBQUMvRixFQUFFO0FBQ0Y7O0NBRUMsR0FDTSxNQUFNSSxzQkFBc0IsQ0FBQ0MsV0FBV3pFO0lBQzNDLE1BQU0sRUFBRTBFLFFBQVEsRUFBRUwsSUFBSSxFQUFFRixRQUFRLEVBQUVDLE9BQU8sRUFBRSxHQUFHcEU7SUFDOUMsT0FBTyxJQUFJSCxtREFBTUEsQ0FBQztRQUFFeUUsVUFBVTtRQUFjQyxNQUFNRyxZQUFZO1FBQVdEO1FBQVdKO1FBQU1GO1FBQVVDO0lBQVE7QUFDaEgsRUFBRTtBQUNGOztDQUVDLEdBQ00sTUFBTU8scUJBQXFCLENBQUNDLFVBQVU1RTtJQUN6QyxNQUFNLEVBQUUwRSxRQUFRLEVBQUVMLElBQUksRUFBRUYsUUFBUSxFQUFFQyxPQUFPLEVBQUUsR0FBR3BFO0lBQzlDLE9BQU8sSUFBSUgsbURBQU1BLENBQUM7UUFBRXlFLFVBQVU7UUFBYUMsTUFBTUcsWUFBWTtRQUFXRTtRQUFVUDtRQUFNRjtRQUFVQztJQUFRO0FBQzlHLEVBQUU7QUFDRjs7Q0FFQyxHQUNNLE1BQU1TLHlCQUF5QixDQUFDTixNQUFNeEUsUUFBUXNFLE9BQVMsSUFBSXhFLG1EQUFNQSxDQUFDO1FBQUV5RSxVQUFVO1FBQWNDO1FBQU1qQyxNQUFNdkM7UUFBUXNFLE1BQU1BLFFBQVE7WUFBQ3RFLE9BQU8rRSxNQUFNO1NBQUM7SUFBQyxHQUFHLENBQ3hKLCtDQUErQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ltYWdlLXRleHQtc3R1ZGlvLTAzLy4vbm9kZV9tb2R1bGVzL29ubnhydW50aW1lLWNvbW1vbi9kaXN0L2VzbS90ZW5zb3ItZmFjdG9yeS1pbXBsLmpzPzQ5MTciXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG5pbXBvcnQgeyBUZW5zb3IgfSBmcm9tICcuL3RlbnNvci1pbXBsLmpzJztcbi8qKlxuICogQ3JlYXRlIGEgbmV3IHRlbnNvciBvYmplY3QgZnJvbSBpbWFnZSBvYmplY3RcbiAqXG4gKiBAcGFyYW0gYnVmZmVyIC0gRXh0cmFjdGVkIGltYWdlIGJ1ZmZlciBkYXRhIC0gYXNzdW1pbmcgUkdCQSBmb3JtYXRcbiAqIEBwYXJhbSBpbWFnZUZvcm1hdCAtIGlucHV0IGltYWdlIGNvbmZpZ3VyYXRpb24gLSByZXF1aXJlZCBjb25maWd1cmF0aW9ucyBoZWlnaHQsIHdpZHRoLCBmb3JtYXRcbiAqIEBwYXJhbSB0ZW5zb3JGb3JtYXQgLSBvdXRwdXQgdGVuc29yIGNvbmZpZ3VyYXRpb24gLSBEZWZhdWx0IGlzIFJHQiBmb3JtYXRcbiAqL1xuZXhwb3J0IGNvbnN0IGJ1ZmZlclRvVGVuc29yID0gKGJ1ZmZlciwgb3B0aW9ucykgPT4ge1xuICAgIGlmIChidWZmZXIgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ltYWdlIGJ1ZmZlciBtdXN0IGJlIGRlZmluZWQnKTtcbiAgICB9XG4gICAgaWYgKG9wdGlvbnMuaGVpZ2h0ID09PSB1bmRlZmluZWQgfHwgb3B0aW9ucy53aWR0aCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW1hZ2UgaGVpZ2h0IGFuZCB3aWR0aCBtdXN0IGJlIGRlZmluZWQnKTtcbiAgICB9XG4gICAgaWYgKG9wdGlvbnMudGVuc29yTGF5b3V0ID09PSAnTkhXQycpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdOSFdDIFRlbnNvciBsYXlvdXQgaXMgbm90IHN1cHBvcnRlZCB5ZXQnKTtcbiAgICB9XG4gICAgY29uc3QgeyBoZWlnaHQsIHdpZHRoIH0gPSBvcHRpb25zO1xuICAgIGNvbnN0IG5vcm0gPSBvcHRpb25zLm5vcm0gPz8geyBtZWFuOiAyNTUsIGJpYXM6IDAgfTtcbiAgICBsZXQgbm9ybU1lYW47XG4gICAgbGV0IG5vcm1CaWFzO1xuICAgIGlmICh0eXBlb2Ygbm9ybS5tZWFuID09PSAnbnVtYmVyJykge1xuICAgICAgICBub3JtTWVhbiA9IFtub3JtLm1lYW4sIG5vcm0ubWVhbiwgbm9ybS5tZWFuLCBub3JtLm1lYW5dO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgbm9ybU1lYW4gPSBbbm9ybS5tZWFuWzBdLCBub3JtLm1lYW5bMV0sIG5vcm0ubWVhblsyXSwgbm9ybS5tZWFuWzNdID8/IDI1NV07XG4gICAgfVxuICAgIGlmICh0eXBlb2Ygbm9ybS5iaWFzID09PSAnbnVtYmVyJykge1xuICAgICAgICBub3JtQmlhcyA9IFtub3JtLmJpYXMsIG5vcm0uYmlhcywgbm9ybS5iaWFzLCBub3JtLmJpYXNdO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgbm9ybUJpYXMgPSBbbm9ybS5iaWFzWzBdLCBub3JtLmJpYXNbMV0sIG5vcm0uYmlhc1syXSwgbm9ybS5iaWFzWzNdID8/IDBdO1xuICAgIH1cbiAgICBjb25zdCBpbnB1dGZvcm1hdCA9IG9wdGlvbnMuZm9ybWF0ICE9PSB1bmRlZmluZWQgPyBvcHRpb25zLmZvcm1hdCA6ICdSR0JBJztcbiAgICAvLyBkZWZhdWx0IHZhbHVlIGlzIFJHQkEgc2luY2UgaW1hZ2VkYXRhIGFuZCBIVE1MSW1hZ2VFbGVtZW50IHVzZXMgaXRcbiAgICBjb25zdCBvdXRwdXRmb3JtYXQgPSBvcHRpb25zLnRlbnNvckZvcm1hdCAhPT0gdW5kZWZpbmVkID8gKG9wdGlvbnMudGVuc29yRm9ybWF0ICE9PSB1bmRlZmluZWQgPyBvcHRpb25zLnRlbnNvckZvcm1hdCA6ICdSR0InKSA6ICdSR0InO1xuICAgIGNvbnN0IHN0cmlkZSA9IGhlaWdodCAqIHdpZHRoO1xuICAgIGNvbnN0IGZsb2F0MzJEYXRhID0gb3V0cHV0Zm9ybWF0ID09PSAnUkdCQScgPyBuZXcgRmxvYXQzMkFycmF5KHN0cmlkZSAqIDQpIDogbmV3IEZsb2F0MzJBcnJheShzdHJpZGUgKiAzKTtcbiAgICAvLyBEZWZhdWx0IHBvaW50ZXIgYXNzaWdubWVudHNcbiAgICBsZXQgc3RlcCA9IDQsIHJJbWFnZVBvaW50ZXIgPSAwLCBnSW1hZ2VQb2ludGVyID0gMSwgYkltYWdlUG9pbnRlciA9IDIsIGFJbWFnZVBvaW50ZXIgPSAzO1xuICAgIGxldCByVGVuc29yUG9pbnRlciA9IDAsIGdUZW5zb3JQb2ludGVyID0gc3RyaWRlLCBiVGVuc29yUG9pbnRlciA9IHN0cmlkZSAqIDIsIGFUZW5zb3JQb2ludGVyID0gLTE7XG4gICAgLy8gVXBkYXRpbmcgdGhlIHBvaW50ZXIgYXNzaWdubWVudHMgYmFzZWQgb24gdGhlIGlucHV0IGltYWdlIGZvcm1hdFxuICAgIGlmIChpbnB1dGZvcm1hdCA9PT0gJ1JHQicpIHtcbiAgICAgICAgc3RlcCA9IDM7XG4gICAgICAgIHJJbWFnZVBvaW50ZXIgPSAwO1xuICAgICAgICBnSW1hZ2VQb2ludGVyID0gMTtcbiAgICAgICAgYkltYWdlUG9pbnRlciA9IDI7XG4gICAgICAgIGFJbWFnZVBvaW50ZXIgPSAtMTtcbiAgICB9XG4gICAgLy8gVXBkYXRpbmcgdGhlIHBvaW50ZXIgYXNzaWdubWVudHMgYmFzZWQgb24gdGhlIG91dHB1dCB0ZW5zb3IgZm9ybWF0XG4gICAgaWYgKG91dHB1dGZvcm1hdCA9PT0gJ1JHQkEnKSB7XG4gICAgICAgIGFUZW5zb3JQb2ludGVyID0gc3RyaWRlICogMztcbiAgICB9XG4gICAgZWxzZSBpZiAob3V0cHV0Zm9ybWF0ID09PSAnUkJHJykge1xuICAgICAgICByVGVuc29yUG9pbnRlciA9IDA7XG4gICAgICAgIGJUZW5zb3JQb2ludGVyID0gc3RyaWRlO1xuICAgICAgICBnVGVuc29yUG9pbnRlciA9IHN0cmlkZSAqIDI7XG4gICAgfVxuICAgIGVsc2UgaWYgKG91dHB1dGZvcm1hdCA9PT0gJ0JHUicpIHtcbiAgICAgICAgYlRlbnNvclBvaW50ZXIgPSAwO1xuICAgICAgICBnVGVuc29yUG9pbnRlciA9IHN0cmlkZTtcbiAgICAgICAgclRlbnNvclBvaW50ZXIgPSBzdHJpZGUgKiAyO1xuICAgIH1cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHN0cmlkZTsgaSsrLCBySW1hZ2VQb2ludGVyICs9IHN0ZXAsIGJJbWFnZVBvaW50ZXIgKz0gc3RlcCwgZ0ltYWdlUG9pbnRlciArPSBzdGVwLCBhSW1hZ2VQb2ludGVyICs9IHN0ZXApIHtcbiAgICAgICAgZmxvYXQzMkRhdGFbclRlbnNvclBvaW50ZXIrK10gPSAoYnVmZmVyW3JJbWFnZVBvaW50ZXJdICsgbm9ybUJpYXNbMF0pIC8gbm9ybU1lYW5bMF07XG4gICAgICAgIGZsb2F0MzJEYXRhW2dUZW5zb3JQb2ludGVyKytdID0gKGJ1ZmZlcltnSW1hZ2VQb2ludGVyXSArIG5vcm1CaWFzWzFdKSAvIG5vcm1NZWFuWzFdO1xuICAgICAgICBmbG9hdDMyRGF0YVtiVGVuc29yUG9pbnRlcisrXSA9IChidWZmZXJbYkltYWdlUG9pbnRlcl0gKyBub3JtQmlhc1syXSkgLyBub3JtTWVhblsyXTtcbiAgICAgICAgaWYgKGFUZW5zb3JQb2ludGVyICE9PSAtMSAmJiBhSW1hZ2VQb2ludGVyICE9PSAtMSkge1xuICAgICAgICAgICAgZmxvYXQzMkRhdGFbYVRlbnNvclBvaW50ZXIrK10gPSAoYnVmZmVyW2FJbWFnZVBvaW50ZXJdICsgbm9ybUJpYXNbM10pIC8gbm9ybU1lYW5bM107XG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gRmxvYXQzMkFycmF5IC0+IG9ydC5UZW5zb3JcbiAgICBjb25zdCBvdXRwdXRUZW5zb3IgPSBvdXRwdXRmb3JtYXQgPT09ICdSR0JBJ1xuICAgICAgICA/IG5ldyBUZW5zb3IoJ2Zsb2F0MzInLCBmbG9hdDMyRGF0YSwgWzEsIDQsIGhlaWdodCwgd2lkdGhdKVxuICAgICAgICA6IG5ldyBUZW5zb3IoJ2Zsb2F0MzInLCBmbG9hdDMyRGF0YSwgWzEsIDMsIGhlaWdodCwgd2lkdGhdKTtcbiAgICByZXR1cm4gb3V0cHV0VGVuc29yO1xufTtcbi8qKlxuICogaW1wbGVtZW50YXRpb24gb2YgVGVuc29yLmZyb21JbWFnZSgpLlxuICovXG5leHBvcnQgY29uc3QgdGVuc29yRnJvbUltYWdlID0gYXN5bmMgKGltYWdlLCBvcHRpb25zKSA9PiB7XG4gICAgLy8gY2hlY2tpbmcgdGhlIHR5cGUgb2YgaW1hZ2Ugb2JqZWN0XG4gICAgY29uc3QgaXNIVE1MSW1hZ2VFbGUgPSB0eXBlb2YgSFRNTEltYWdlRWxlbWVudCAhPT0gJ3VuZGVmaW5lZCcgJiYgaW1hZ2UgaW5zdGFuY2VvZiBIVE1MSW1hZ2VFbGVtZW50O1xuICAgIGNvbnN0IGlzSW1hZ2VEYXRhRWxlID0gdHlwZW9mIEltYWdlRGF0YSAhPT0gJ3VuZGVmaW5lZCcgJiYgaW1hZ2UgaW5zdGFuY2VvZiBJbWFnZURhdGE7XG4gICAgY29uc3QgaXNJbWFnZUJpdG1hcCA9IHR5cGVvZiBJbWFnZUJpdG1hcCAhPT0gJ3VuZGVmaW5lZCcgJiYgaW1hZ2UgaW5zdGFuY2VvZiBJbWFnZUJpdG1hcDtcbiAgICBjb25zdCBpc1N0cmluZyA9IHR5cGVvZiBpbWFnZSA9PT0gJ3N0cmluZyc7XG4gICAgbGV0IGRhdGE7XG4gICAgbGV0IGJ1ZmZlclRvVGVuc29yT3B0aW9ucyA9IG9wdGlvbnMgPz8ge307XG4gICAgY29uc3QgY3JlYXRlQ2FudmFzID0gKCkgPT4ge1xuICAgICAgICBpZiAodHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcmV0dXJuIGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2NhbnZhcycpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHR5cGVvZiBPZmZzY3JlZW5DYW52YXMgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IE9mZnNjcmVlbkNhbnZhcygxLCAxKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQ2FudmFzIGlzIG5vdCBzdXBwb3J0ZWQnKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgY3JlYXRlQ2FudmFzQ29udGV4dCA9IChjYW52YXMpID0+IHtcbiAgICAgICAgaWYgKHR5cGVvZiBIVE1MQ2FudmFzRWxlbWVudCAhPT0gJ3VuZGVmaW5lZCcgJiYgY2FudmFzIGluc3RhbmNlb2YgSFRNTENhbnZhc0VsZW1lbnQpIHtcbiAgICAgICAgICAgIHJldHVybiBjYW52YXMuZ2V0Q29udGV4dCgnMmQnKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjYW52YXMgaW5zdGFuY2VvZiBPZmZzY3JlZW5DYW52YXMpIHtcbiAgICAgICAgICAgIHJldHVybiBjYW52YXMuZ2V0Q29udGV4dCgnMmQnKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgfTtcbiAgICAvLyBmaWxsaW5nIGFuZCBjaGVja2luZyBpbWFnZSBjb25maWd1cmF0aW9uIG9wdGlvbnNcbiAgICBpZiAoaXNIVE1MSW1hZ2VFbGUpIHtcbiAgICAgICAgLy8gSFRNTEltYWdlRWxlbWVudCAtIGltYWdlIG9iamVjdCAtIGZvcm1hdCBpcyBSR0JBIGJ5IGRlZmF1bHRcbiAgICAgICAgY29uc3QgY2FudmFzID0gY3JlYXRlQ2FudmFzKCk7XG4gICAgICAgIGNhbnZhcy53aWR0aCA9IGltYWdlLndpZHRoO1xuICAgICAgICBjYW52YXMuaGVpZ2h0ID0gaW1hZ2UuaGVpZ2h0O1xuICAgICAgICBjb25zdCBwaXhlbHMyRENvbnRleHQgPSBjcmVhdGVDYW52YXNDb250ZXh0KGNhbnZhcyk7XG4gICAgICAgIGlmIChwaXhlbHMyRENvbnRleHQgIT0gbnVsbCkge1xuICAgICAgICAgICAgbGV0IGhlaWdodCA9IGltYWdlLmhlaWdodDtcbiAgICAgICAgICAgIGxldCB3aWR0aCA9IGltYWdlLndpZHRoO1xuICAgICAgICAgICAgaWYgKG9wdGlvbnMgIT09IHVuZGVmaW5lZCAmJiBvcHRpb25zLnJlc2l6ZWRIZWlnaHQgIT09IHVuZGVmaW5lZCAmJiBvcHRpb25zLnJlc2l6ZWRXaWR0aCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgaGVpZ2h0ID0gb3B0aW9ucy5yZXNpemVkSGVpZ2h0O1xuICAgICAgICAgICAgICAgIHdpZHRoID0gb3B0aW9ucy5yZXNpemVkV2lkdGg7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAob3B0aW9ucyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgYnVmZmVyVG9UZW5zb3JPcHRpb25zID0gb3B0aW9ucztcbiAgICAgICAgICAgICAgICBpZiAob3B0aW9ucy50ZW5zb3JGb3JtYXQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ltYWdlIGlucHV0IGNvbmZpZyBmb3JtYXQgbXVzdCBiZSBSR0JBIGZvciBIVE1MSW1hZ2VFbGVtZW50Jyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBidWZmZXJUb1RlbnNvck9wdGlvbnMudGVuc29yRm9ybWF0ID0gJ1JHQkEnO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBidWZmZXJUb1RlbnNvck9wdGlvbnMuaGVpZ2h0ID0gaGVpZ2h0O1xuICAgICAgICAgICAgICAgIGJ1ZmZlclRvVGVuc29yT3B0aW9ucy53aWR0aCA9IHdpZHRoO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgYnVmZmVyVG9UZW5zb3JPcHRpb25zLnRlbnNvckZvcm1hdCA9ICdSR0JBJztcbiAgICAgICAgICAgICAgICBidWZmZXJUb1RlbnNvck9wdGlvbnMuaGVpZ2h0ID0gaGVpZ2h0O1xuICAgICAgICAgICAgICAgIGJ1ZmZlclRvVGVuc29yT3B0aW9ucy53aWR0aCA9IHdpZHRoO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcGl4ZWxzMkRDb250ZXh0LmRyYXdJbWFnZShpbWFnZSwgMCwgMCk7XG4gICAgICAgICAgICBkYXRhID0gcGl4ZWxzMkRDb250ZXh0LmdldEltYWdlRGF0YSgwLCAwLCB3aWR0aCwgaGVpZ2h0KS5kYXRhO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYW4gbm90IGFjY2VzcyBpbWFnZSBkYXRhJyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSBpZiAoaXNJbWFnZURhdGFFbGUpIHtcbiAgICAgICAgbGV0IGhlaWdodDtcbiAgICAgICAgbGV0IHdpZHRoO1xuICAgICAgICBpZiAob3B0aW9ucyAhPT0gdW5kZWZpbmVkICYmIG9wdGlvbnMucmVzaXplZFdpZHRoICE9PSB1bmRlZmluZWQgJiYgb3B0aW9ucy5yZXNpemVkSGVpZ2h0ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIGhlaWdodCA9IG9wdGlvbnMucmVzaXplZEhlaWdodDtcbiAgICAgICAgICAgIHdpZHRoID0gb3B0aW9ucy5yZXNpemVkV2lkdGg7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBoZWlnaHQgPSBpbWFnZS5oZWlnaHQ7XG4gICAgICAgICAgICB3aWR0aCA9IGltYWdlLndpZHRoO1xuICAgICAgICB9XG4gICAgICAgIGlmIChvcHRpb25zICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIGJ1ZmZlclRvVGVuc29yT3B0aW9ucyA9IG9wdGlvbnM7XG4gICAgICAgIH1cbiAgICAgICAgYnVmZmVyVG9UZW5zb3JPcHRpb25zLmZvcm1hdCA9ICdSR0JBJztcbiAgICAgICAgYnVmZmVyVG9UZW5zb3JPcHRpb25zLmhlaWdodCA9IGhlaWdodDtcbiAgICAgICAgYnVmZmVyVG9UZW5zb3JPcHRpb25zLndpZHRoID0gd2lkdGg7XG4gICAgICAgIGlmIChvcHRpb25zICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIGNvbnN0IHRlbXBDYW52YXMgPSBjcmVhdGVDYW52YXMoKTtcbiAgICAgICAgICAgIHRlbXBDYW52YXMud2lkdGggPSB3aWR0aDtcbiAgICAgICAgICAgIHRlbXBDYW52YXMuaGVpZ2h0ID0gaGVpZ2h0O1xuICAgICAgICAgICAgY29uc3QgcGl4ZWxzMkRDb250ZXh0ID0gY3JlYXRlQ2FudmFzQ29udGV4dCh0ZW1wQ2FudmFzKTtcbiAgICAgICAgICAgIGlmIChwaXhlbHMyRENvbnRleHQgIT0gbnVsbCkge1xuICAgICAgICAgICAgICAgIHBpeGVsczJEQ29udGV4dC5wdXRJbWFnZURhdGEoaW1hZ2UsIDAsIDApO1xuICAgICAgICAgICAgICAgIGRhdGEgPSBwaXhlbHMyRENvbnRleHQuZ2V0SW1hZ2VEYXRhKDAsIDAsIHdpZHRoLCBoZWlnaHQpLmRhdGE7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NhbiBub3QgYWNjZXNzIGltYWdlIGRhdGEnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGRhdGEgPSBpbWFnZS5kYXRhO1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2UgaWYgKGlzSW1hZ2VCaXRtYXApIHtcbiAgICAgICAgLy8gSW1hZ2VCaXRtYXAgLSBpbWFnZSBvYmplY3QgLSBmb3JtYXQgbXVzdCBiZSBwcm92aWRlZCBieSB1c2VyXG4gICAgICAgIGlmIChvcHRpb25zID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignUGxlYXNlIHByb3ZpZGUgaW1hZ2UgY29uZmlnIHdpdGggZm9ybWF0IGZvciBJbWFnZWJpdG1hcCcpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNhbnZhcyA9IGNyZWF0ZUNhbnZhcygpO1xuICAgICAgICBjYW52YXMud2lkdGggPSBpbWFnZS53aWR0aDtcbiAgICAgICAgY2FudmFzLmhlaWdodCA9IGltYWdlLmhlaWdodDtcbiAgICAgICAgY29uc3QgcGl4ZWxzMkRDb250ZXh0ID0gY3JlYXRlQ2FudmFzQ29udGV4dChjYW52YXMpO1xuICAgICAgICBpZiAocGl4ZWxzMkRDb250ZXh0ICE9IG51bGwpIHtcbiAgICAgICAgICAgIGNvbnN0IGhlaWdodCA9IGltYWdlLmhlaWdodDtcbiAgICAgICAgICAgIGNvbnN0IHdpZHRoID0gaW1hZ2Uud2lkdGg7XG4gICAgICAgICAgICBwaXhlbHMyRENvbnRleHQuZHJhd0ltYWdlKGltYWdlLCAwLCAwLCB3aWR0aCwgaGVpZ2h0KTtcbiAgICAgICAgICAgIGRhdGEgPSBwaXhlbHMyRENvbnRleHQuZ2V0SW1hZ2VEYXRhKDAsIDAsIHdpZHRoLCBoZWlnaHQpLmRhdGE7XG4gICAgICAgICAgICBidWZmZXJUb1RlbnNvck9wdGlvbnMuaGVpZ2h0ID0gaGVpZ2h0O1xuICAgICAgICAgICAgYnVmZmVyVG9UZW5zb3JPcHRpb25zLndpZHRoID0gd2lkdGg7XG4gICAgICAgICAgICByZXR1cm4gYnVmZmVyVG9UZW5zb3IoZGF0YSwgYnVmZmVyVG9UZW5zb3JPcHRpb25zKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQ2FuIG5vdCBhY2Nlc3MgaW1hZ2UgZGF0YScpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2UgaWYgKGlzU3RyaW5nKSB7XG4gICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBjYW52YXMgPSBjcmVhdGVDYW52YXMoKTtcbiAgICAgICAgICAgIGNvbnN0IGNvbnRleHQgPSBjcmVhdGVDYW52YXNDb250ZXh0KGNhbnZhcyk7XG4gICAgICAgICAgICBpZiAoIWltYWdlIHx8ICFjb250ZXh0KSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlamVjdCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgbmV3SW1hZ2UgPSBuZXcgSW1hZ2UoKTtcbiAgICAgICAgICAgIG5ld0ltYWdlLmNyb3NzT3JpZ2luID0gJ0Fub255bW91cyc7XG4gICAgICAgICAgICBuZXdJbWFnZS5zcmMgPSBpbWFnZTtcbiAgICAgICAgICAgIG5ld0ltYWdlLm9ubG9hZCA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICBjYW52YXMud2lkdGggPSBuZXdJbWFnZS53aWR0aDtcbiAgICAgICAgICAgICAgICBjYW52YXMuaGVpZ2h0ID0gbmV3SW1hZ2UuaGVpZ2h0O1xuICAgICAgICAgICAgICAgIGNvbnRleHQuZHJhd0ltYWdlKG5ld0ltYWdlLCAwLCAwLCBjYW52YXMud2lkdGgsIGNhbnZhcy5oZWlnaHQpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGltZyA9IGNvbnRleHQuZ2V0SW1hZ2VEYXRhKDAsIDAsIGNhbnZhcy53aWR0aCwgY2FudmFzLmhlaWdodCk7XG4gICAgICAgICAgICAgICAgYnVmZmVyVG9UZW5zb3JPcHRpb25zLmhlaWdodCA9IGNhbnZhcy5oZWlnaHQ7XG4gICAgICAgICAgICAgICAgYnVmZmVyVG9UZW5zb3JPcHRpb25zLndpZHRoID0gY2FudmFzLndpZHRoO1xuICAgICAgICAgICAgICAgIHJlc29sdmUoYnVmZmVyVG9UZW5zb3IoaW1nLmRhdGEsIGJ1ZmZlclRvVGVuc29yT3B0aW9ucykpO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0lucHV0IGRhdGEgcHJvdmlkZWQgaXMgbm90IHN1cHBvcnRlZCAtIGFib3J0ZWQgdGVuc29yIGNyZWF0aW9uJyk7XG4gICAgfVxuICAgIGlmIChkYXRhICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIGJ1ZmZlclRvVGVuc29yKGRhdGEsIGJ1ZmZlclRvVGVuc29yT3B0aW9ucyk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0lucHV0IGRhdGEgcHJvdmlkZWQgaXMgbm90IHN1cHBvcnRlZCAtIGFib3J0ZWQgdGVuc29yIGNyZWF0aW9uJyk7XG4gICAgfVxufTtcbi8qKlxuICogaW1wbGVtZW50YXRpb24gb2YgVGVuc29yLmZyb21UZXh0dXJlKCkuXG4gKi9cbmV4cG9ydCBjb25zdCB0ZW5zb3JGcm9tVGV4dHVyZSA9ICh0ZXh0dXJlLCBvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgeyB3aWR0aCwgaGVpZ2h0LCBkb3dubG9hZCwgZGlzcG9zZSB9ID0gb3B0aW9ucztcbiAgICAvLyBBbHdheXMgYXNzdW1lIFJHQkFGMzIuIFRPRE86IHN1cHBvcnQgZGlmZmVyZW50IHRleHR1cmUgZm9ybWF0XG4gICAgY29uc3QgZGltcyA9IFsxLCBoZWlnaHQsIHdpZHRoLCA0XTtcbiAgICByZXR1cm4gbmV3IFRlbnNvcih7IGxvY2F0aW9uOiAndGV4dHVyZScsIHR5cGU6ICdmbG9hdDMyJywgdGV4dHVyZSwgZGltcywgZG93bmxvYWQsIGRpc3Bvc2UgfSk7XG59O1xuLyoqXG4gKiBpbXBsZW1lbnRhdGlvbiBvZiBUZW5zb3IuZnJvbUdwdUJ1ZmZlcigpLlxuICovXG5leHBvcnQgY29uc3QgdGVuc29yRnJvbUdwdUJ1ZmZlciA9IChncHVCdWZmZXIsIG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCB7IGRhdGFUeXBlLCBkaW1zLCBkb3dubG9hZCwgZGlzcG9zZSB9ID0gb3B0aW9ucztcbiAgICByZXR1cm4gbmV3IFRlbnNvcih7IGxvY2F0aW9uOiAnZ3B1LWJ1ZmZlcicsIHR5cGU6IGRhdGFUeXBlID8/ICdmbG9hdDMyJywgZ3B1QnVmZmVyLCBkaW1zLCBkb3dubG9hZCwgZGlzcG9zZSB9KTtcbn07XG4vKipcbiAqIGltcGxlbWVudGF0aW9uIG9mIFRlbnNvci5mcm9tTUxUZW5zb3IoKS5cbiAqL1xuZXhwb3J0IGNvbnN0IHRlbnNvckZyb21NTFRlbnNvciA9IChtbFRlbnNvciwgb3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgZGF0YVR5cGUsIGRpbXMsIGRvd25sb2FkLCBkaXNwb3NlIH0gPSBvcHRpb25zO1xuICAgIHJldHVybiBuZXcgVGVuc29yKHsgbG9jYXRpb246ICdtbC10ZW5zb3InLCB0eXBlOiBkYXRhVHlwZSA/PyAnZmxvYXQzMicsIG1sVGVuc29yLCBkaW1zLCBkb3dubG9hZCwgZGlzcG9zZSB9KTtcbn07XG4vKipcbiAqIGltcGxlbWVudGF0aW9uIG9mIFRlbnNvci5mcm9tUGlubmVkQnVmZmVyKCkuXG4gKi9cbmV4cG9ydCBjb25zdCB0ZW5zb3JGcm9tUGlubmVkQnVmZmVyID0gKHR5cGUsIGJ1ZmZlciwgZGltcykgPT4gbmV3IFRlbnNvcih7IGxvY2F0aW9uOiAnY3B1LXBpbm5lZCcsIHR5cGUsIGRhdGE6IGJ1ZmZlciwgZGltczogZGltcyA/PyBbYnVmZmVyLmxlbmd0aF0gfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10ZW5zb3ItZmFjdG9yeS1pbXBsLmpzLm1hcCJdLCJuYW1lcyI6WyJUZW5zb3IiLCJidWZmZXJUb1RlbnNvciIsImJ1ZmZlciIsIm9wdGlvbnMiLCJ1bmRlZmluZWQiLCJFcnJvciIsImhlaWdodCIsIndpZHRoIiwidGVuc29yTGF5b3V0Iiwibm9ybSIsIm1lYW4iLCJiaWFzIiwibm9ybU1lYW4iLCJub3JtQmlhcyIsImlucHV0Zm9ybWF0IiwiZm9ybWF0Iiwib3V0cHV0Zm9ybWF0IiwidGVuc29yRm9ybWF0Iiwic3RyaWRlIiwiZmxvYXQzMkRhdGEiLCJGbG9hdDMyQXJyYXkiLCJzdGVwIiwickltYWdlUG9pbnRlciIsImdJbWFnZVBvaW50ZXIiLCJiSW1hZ2VQb2ludGVyIiwiYUltYWdlUG9pbnRlciIsInJUZW5zb3JQb2ludGVyIiwiZ1RlbnNvclBvaW50ZXIiLCJiVGVuc29yUG9pbnRlciIsImFUZW5zb3JQb2ludGVyIiwiaSIsIm91dHB1dFRlbnNvciIsInRlbnNvckZyb21JbWFnZSIsImltYWdlIiwiaXNIVE1MSW1hZ2VFbGUiLCJIVE1MSW1hZ2VFbGVtZW50IiwiaXNJbWFnZURhdGFFbGUiLCJJbWFnZURhdGEiLCJpc0ltYWdlQml0bWFwIiwiSW1hZ2VCaXRtYXAiLCJpc1N0cmluZyIsImRhdGEiLCJidWZmZXJUb1RlbnNvck9wdGlvbnMiLCJjcmVhdGVDYW52YXMiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJPZmZzY3JlZW5DYW52YXMiLCJjcmVhdGVDYW52YXNDb250ZXh0IiwiY2FudmFzIiwiSFRNTENhbnZhc0VsZW1lbnQiLCJnZXRDb250ZXh0IiwicGl4ZWxzMkRDb250ZXh0IiwicmVzaXplZEhlaWdodCIsInJlc2l6ZWRXaWR0aCIsImRyYXdJbWFnZSIsImdldEltYWdlRGF0YSIsInRlbXBDYW52YXMiLCJwdXRJbWFnZURhdGEiLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsImNvbnRleHQiLCJuZXdJbWFnZSIsIkltYWdlIiwiY3Jvc3NPcmlnaW4iLCJzcmMiLCJvbmxvYWQiLCJpbWciLCJ0ZW5zb3JGcm9tVGV4dHVyZSIsInRleHR1cmUiLCJkb3dubG9hZCIsImRpc3Bvc2UiLCJkaW1zIiwibG9jYXRpb24iLCJ0eXBlIiwidGVuc29yRnJvbUdwdUJ1ZmZlciIsImdwdUJ1ZmZlciIsImRhdGFUeXBlIiwidGVuc29yRnJvbU1MVGVuc29yIiwibWxUZW5zb3IiLCJ0ZW5zb3JGcm9tUGlubmVkQnVmZmVyIiwibGVuZ3RoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-factory-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-factory.js":
/*!********************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/tensor-factory.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n //# sourceMappingURL=tensor-factory.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL3RlbnNvci1mYWN0b3J5LmpzIiwibWFwcGluZ3MiOiI7QUFBQSw0REFBNEQ7QUFDNUQsa0NBQWtDO0FBQ3hCLENBQ1YsMENBQTBDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1hZ2UtdGV4dC1zdHVkaW8tMDMvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL3RlbnNvci1mYWN0b3J5LmpzP2RmMTYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG5leHBvcnQge307XG4vLyMgc291cmNlTWFwcGluZ1VSTD10ZW5zb3ItZmFjdG9yeS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-factory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-impl-type-mapping.js":
/*!******************************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/tensor-impl-type-mapping.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP: () => (/* binding */ NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP),\n/* harmony export */   NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP: () => (/* binding */ NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP),\n/* harmony export */   checkTypedArray: () => (/* binding */ checkTypedArray)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP = new Map([\n    [\n        \"float32\",\n        Float32Array\n    ],\n    [\n        \"uint8\",\n        Uint8Array\n    ],\n    [\n        \"int8\",\n        Int8Array\n    ],\n    [\n        \"uint16\",\n        Uint16Array\n    ],\n    [\n        \"int16\",\n        Int16Array\n    ],\n    [\n        \"int32\",\n        Int32Array\n    ],\n    [\n        \"bool\",\n        Uint8Array\n    ],\n    [\n        \"float64\",\n        Float64Array\n    ],\n    [\n        \"uint32\",\n        Uint32Array\n    ],\n    [\n        \"int4\",\n        Uint8Array\n    ],\n    [\n        \"uint4\",\n        Uint8Array\n    ]\n]);\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP = new Map([\n    [\n        Float32Array,\n        \"float32\"\n    ],\n    [\n        Uint8Array,\n        \"uint8\"\n    ],\n    [\n        Int8Array,\n        \"int8\"\n    ],\n    [\n        Uint16Array,\n        \"uint16\"\n    ],\n    [\n        Int16Array,\n        \"int16\"\n    ],\n    [\n        Int32Array,\n        \"int32\"\n    ],\n    [\n        Float64Array,\n        \"float64\"\n    ],\n    [\n        Uint32Array,\n        \"uint32\"\n    ]\n]);\n// the following code allows delaying execution of BigInt/Float16Array checking. This allows lazy initialization for\n// NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP and NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP, which allows BigInt/Float16Array\n// polyfill if available.\nlet isTypedArrayChecked = false;\nconst checkTypedArray = ()=>{\n    if (!isTypedArrayChecked) {\n        isTypedArrayChecked = true;\n        const isBigInt64ArrayAvailable = typeof BigInt64Array !== \"undefined\" && BigInt64Array.from;\n        const isBigUint64ArrayAvailable = typeof BigUint64Array !== \"undefined\" && BigUint64Array.from;\n        const isFloat16ArrayAvailable = typeof Float16Array !== \"undefined\" && Float16Array.from;\n        if (isBigInt64ArrayAvailable) {\n            NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set(\"int64\", BigInt64Array);\n            NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigInt64Array, \"int64\");\n        }\n        if (isBigUint64ArrayAvailable) {\n            NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set(\"uint64\", BigUint64Array);\n            NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigUint64Array, \"uint64\");\n        }\n        if (isFloat16ArrayAvailable) {\n            NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set(\"float16\", Float16Array);\n            NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(Float16Array, \"float16\");\n        } else {\n            // if Float16Array is not available, use 'Uint16Array' to store the data.\n            NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set(\"float16\", Uint16Array);\n        }\n    }\n}; //# sourceMappingURL=tensor-impl-type-mapping.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-impl-type-mapping.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-impl.js":
/*!*****************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/tensor-impl.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tensor: () => (/* binding */ Tensor)\n/* harmony export */ });\n/* harmony import */ var _tensor_conversion_impl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tensor-conversion-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-conversion-impl.js\");\n/* harmony import */ var _tensor_factory_impl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tensor-factory-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-factory-impl.js\");\n/* harmony import */ var _tensor_impl_type_mapping_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tensor-impl-type-mapping.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-impl-type-mapping.js\");\n/* harmony import */ var _tensor_utils_impl_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tensor-utils-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-utils-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n\n\n\n/**\n * the implementation of Tensor interface.\n *\n * @ignore\n */ class Tensor {\n    /**\n     * implementation.\n     */ constructor(arg0, arg1, arg2){\n        // perform one-time check for BigInt/Float16Array support\n        (0,_tensor_impl_type_mapping_js__WEBPACK_IMPORTED_MODULE_2__.checkTypedArray)();\n        let type;\n        let dims;\n        if (typeof arg0 === \"object\" && \"location\" in arg0) {\n            //\n            // constructing tensor from specific location\n            //\n            this.dataLocation = arg0.location;\n            type = arg0.type;\n            dims = arg0.dims;\n            switch(arg0.location){\n                case \"cpu-pinned\":\n                    {\n                        const expectedTypedArrayConstructor = _tensor_impl_type_mapping_js__WEBPACK_IMPORTED_MODULE_2__.NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.get(type);\n                        if (!expectedTypedArrayConstructor) {\n                            throw new TypeError(`unsupported type \"${type}\" to create tensor from pinned buffer`);\n                        }\n                        if (!(arg0.data instanceof expectedTypedArrayConstructor)) {\n                            throw new TypeError(`buffer should be of type ${expectedTypedArrayConstructor.name}`);\n                        }\n                        this.cpuData = arg0.data;\n                        break;\n                    }\n                case \"texture\":\n                    {\n                        if (type !== \"float32\") {\n                            throw new TypeError(`unsupported type \"${type}\" to create tensor from texture`);\n                        }\n                        this.gpuTextureData = arg0.texture;\n                        this.downloader = arg0.download;\n                        this.disposer = arg0.dispose;\n                        break;\n                    }\n                case \"gpu-buffer\":\n                    {\n                        if (type !== \"float32\" && type !== \"float16\" && type !== \"int32\" && type !== \"int64\" && type !== \"uint32\" && type !== \"uint8\" && type !== \"bool\" && type !== \"uint4\" && type !== \"int4\") {\n                            throw new TypeError(`unsupported type \"${type}\" to create tensor from gpu buffer`);\n                        }\n                        this.gpuBufferData = arg0.gpuBuffer;\n                        this.downloader = arg0.download;\n                        this.disposer = arg0.dispose;\n                        break;\n                    }\n                case \"ml-tensor\":\n                    {\n                        if (type !== \"float32\" && type !== \"float16\" && type !== \"int32\" && type !== \"int64\" && type !== \"uint32\" && type !== \"uint64\" && type !== \"int8\" && type !== \"uint8\" && type !== \"bool\" && type !== \"uint4\" && type !== \"int4\") {\n                            throw new TypeError(`unsupported type \"${type}\" to create tensor from MLTensor`);\n                        }\n                        this.mlTensorData = arg0.mlTensor;\n                        this.downloader = arg0.download;\n                        this.disposer = arg0.dispose;\n                        break;\n                    }\n                default:\n                    throw new Error(`Tensor constructor: unsupported location '${this.dataLocation}'`);\n            }\n        } else {\n            //\n            // constructing tensor of location 'cpu'\n            //\n            let data;\n            let maybeDims;\n            // check whether arg0 is type or data\n            if (typeof arg0 === \"string\") {\n                //\n                // Override: constructor(type, data, ...)\n                //\n                type = arg0;\n                maybeDims = arg2;\n                if (arg0 === \"string\") {\n                    // string tensor\n                    if (!Array.isArray(arg1)) {\n                        throw new TypeError(\"A string tensor's data must be a string array.\");\n                    }\n                    // we don't check whether every element in the array is string; this is too slow. we assume it's correct and\n                    // error will be populated at inference\n                    data = arg1;\n                } else {\n                    // numeric tensor\n                    const typedArrayConstructor = _tensor_impl_type_mapping_js__WEBPACK_IMPORTED_MODULE_2__.NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.get(arg0);\n                    if (typedArrayConstructor === undefined) {\n                        throw new TypeError(`Unsupported tensor type: ${arg0}.`);\n                    }\n                    if (Array.isArray(arg1)) {\n                        if (arg0 === \"float16\" && typedArrayConstructor === Uint16Array || arg0 === \"uint4\" || arg0 === \"int4\") {\n                            // - 'float16':\n                            //   When no Float16Array polyfill is used, we cannot create 'float16' tensor from number array.\n                            //\n                            //   Throw error here because when user try to use number array as data,\n                            //   e.g. new Tensor('float16', [1, 2, 3, 4], dims)), it will actually call\n                            //   Uint16Array.from(arg1) which generates wrong data.\n                            //\n                            // - 'uint4' and 'int4':\n                            //   Uint8Array.from(arg1) will generate wrong data for 'uint4' and 'int4' tensor.\n                            //\n                            throw new TypeError(`Creating a ${arg0} tensor from number array is not supported. Please use ${typedArrayConstructor.name} as data.`);\n                        } else if (arg0 === \"uint64\" || arg0 === \"int64\") {\n                            // use 'as any' here because:\n                            // 1. TypeScript's check on type of 'Array.isArray()' does not work with readonly arrays.\n                            // see https://github.com/microsoft/TypeScript/issues/17002\n                            // 2. TypeScript's check on union type of '(BigInt64ArrayConstructor|BigUint64ArrayConstructor).from()'\n                            // does not accept parameter mapFn.\n                            // 3. parameters of 'SupportedTypedArrayConstructors.from()' does not match the requirement of the union\n                            // type.\n                            // assume 'arg1' is of type \"readonly number[]|readonly bigint[]\" here.\n                            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                            data = typedArrayConstructor.from(arg1, BigInt);\n                        } else {\n                            // assume 'arg1' is of type \"readonly number[]\" here.\n                            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                            data = typedArrayConstructor.from(arg1);\n                        }\n                    } else if (arg1 instanceof typedArrayConstructor) {\n                        data = arg1;\n                    } else if (arg1 instanceof Uint8ClampedArray) {\n                        if (arg0 === \"uint8\") {\n                            data = Uint8Array.from(arg1);\n                        } else {\n                            throw new TypeError(`A Uint8ClampedArray tensor's data must be type of uint8`);\n                        }\n                    } else {\n                        throw new TypeError(`A ${type} tensor's data must be type of ${typedArrayConstructor}`);\n                    }\n                }\n            } else {\n                //\n                // Override: constructor(data, ...)\n                //\n                maybeDims = arg1;\n                if (Array.isArray(arg0)) {\n                    // only boolean[] and string[] is supported\n                    if (arg0.length === 0) {\n                        throw new TypeError(\"Tensor type cannot be inferred from an empty array.\");\n                    }\n                    const firstElementType = typeof arg0[0];\n                    if (firstElementType === \"string\") {\n                        type = \"string\";\n                        data = arg0;\n                    } else if (firstElementType === \"boolean\") {\n                        type = \"bool\";\n                        // 'arg0' is of type 'boolean[]'. Uint8Array.from(boolean[]) actually works, but typescript thinks this is\n                        // wrong type. We use 'as any' to make it happy.\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                        data = Uint8Array.from(arg0);\n                    } else {\n                        throw new TypeError(`Invalid element type of data array: ${firstElementType}.`);\n                    }\n                } else if (arg0 instanceof Uint8ClampedArray) {\n                    type = \"uint8\";\n                    data = Uint8Array.from(arg0);\n                } else {\n                    // get tensor type from TypedArray\n                    const mappedType = _tensor_impl_type_mapping_js__WEBPACK_IMPORTED_MODULE_2__.NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.get(arg0.constructor);\n                    if (mappedType === undefined) {\n                        throw new TypeError(`Unsupported type for tensor data: ${arg0.constructor}.`);\n                    }\n                    type = mappedType;\n                    data = arg0;\n                }\n            }\n            // type and data is processed, now processing dims\n            if (maybeDims === undefined) {\n                // assume 1-D tensor if dims omitted\n                maybeDims = [\n                    data.length\n                ];\n            } else if (!Array.isArray(maybeDims)) {\n                throw new TypeError(\"A tensor's dims must be a number array\");\n            }\n            dims = maybeDims;\n            this.cpuData = data;\n            this.dataLocation = \"cpu\";\n        }\n        // perform check on dims\n        const size = (0,_tensor_utils_impl_js__WEBPACK_IMPORTED_MODULE_3__.calculateSize)(dims);\n        // if data is on CPU, check whether data length matches tensor size\n        if (this.cpuData && size !== this.cpuData.length) {\n            if ((type === \"uint4\" || type === \"int4\") && Math.ceil(size / 2) === this.cpuData.length) {\n            // for (u)int4, the data length is half of the tensor size. So we check this special case when size is odd.\n            } else {\n                throw new Error(`Tensor's size(${size}) does not match data length(${this.cpuData.length}).`);\n            }\n        }\n        this.type = type;\n        this.dims = dims;\n        this.size = size;\n    }\n    // #endregion\n    // #region factory\n    static async fromImage(image, options) {\n        return (0,_tensor_factory_impl_js__WEBPACK_IMPORTED_MODULE_1__.tensorFromImage)(image, options);\n    }\n    static fromTexture(texture, options) {\n        return (0,_tensor_factory_impl_js__WEBPACK_IMPORTED_MODULE_1__.tensorFromTexture)(texture, options);\n    }\n    static fromGpuBuffer(gpuBuffer, options) {\n        return (0,_tensor_factory_impl_js__WEBPACK_IMPORTED_MODULE_1__.tensorFromGpuBuffer)(gpuBuffer, options);\n    }\n    static fromMLTensor(mlTensor, options) {\n        return (0,_tensor_factory_impl_js__WEBPACK_IMPORTED_MODULE_1__.tensorFromMLTensor)(mlTensor, options);\n    }\n    static fromPinnedBuffer(type, buffer, dims) {\n        return (0,_tensor_factory_impl_js__WEBPACK_IMPORTED_MODULE_1__.tensorFromPinnedBuffer)(type, buffer, dims);\n    }\n    // #endregion\n    // #region conversions\n    toDataURL(options) {\n        return (0,_tensor_conversion_impl_js__WEBPACK_IMPORTED_MODULE_0__.tensorToDataURL)(this, options);\n    }\n    toImageData(options) {\n        return (0,_tensor_conversion_impl_js__WEBPACK_IMPORTED_MODULE_0__.tensorToImageData)(this, options);\n    }\n    // #endregion\n    // #region properties\n    get data() {\n        this.ensureValid();\n        if (!this.cpuData) {\n            throw new Error(\"The data is not on CPU. Use `getData()` to download GPU data to CPU, \" + \"or use `texture` or `gpuBuffer` property to access the GPU data directly.\");\n        }\n        return this.cpuData;\n    }\n    get location() {\n        return this.dataLocation;\n    }\n    get texture() {\n        this.ensureValid();\n        if (!this.gpuTextureData) {\n            throw new Error(\"The data is not stored as a WebGL texture.\");\n        }\n        return this.gpuTextureData;\n    }\n    get gpuBuffer() {\n        this.ensureValid();\n        if (!this.gpuBufferData) {\n            throw new Error(\"The data is not stored as a WebGPU buffer.\");\n        }\n        return this.gpuBufferData;\n    }\n    get mlTensor() {\n        this.ensureValid();\n        if (!this.mlTensorData) {\n            throw new Error(\"The data is not stored as a WebNN MLTensor.\");\n        }\n        return this.mlTensorData;\n    }\n    // #endregion\n    // #region methods\n    async getData(releaseData) {\n        this.ensureValid();\n        switch(this.dataLocation){\n            case \"cpu\":\n            case \"cpu-pinned\":\n                return this.data;\n            case \"texture\":\n            case \"gpu-buffer\":\n            case \"ml-tensor\":\n                {\n                    if (!this.downloader) {\n                        throw new Error(\"The current tensor is not created with a specified data downloader.\");\n                    }\n                    if (this.isDownloading) {\n                        throw new Error(\"The current tensor is being downloaded.\");\n                    }\n                    try {\n                        this.isDownloading = true;\n                        const data = await this.downloader();\n                        this.downloader = undefined;\n                        this.dataLocation = \"cpu\";\n                        this.cpuData = data;\n                        if (releaseData && this.disposer) {\n                            this.disposer();\n                            this.disposer = undefined;\n                        }\n                        return data;\n                    } finally{\n                        this.isDownloading = false;\n                    }\n                }\n            default:\n                throw new Error(`cannot get data from location: ${this.dataLocation}`);\n        }\n    }\n    dispose() {\n        if (this.isDownloading) {\n            throw new Error(\"The current tensor is being downloaded.\");\n        }\n        if (this.disposer) {\n            this.disposer();\n            this.disposer = undefined;\n        }\n        this.cpuData = undefined;\n        this.gpuTextureData = undefined;\n        this.gpuBufferData = undefined;\n        this.mlTensorData = undefined;\n        this.downloader = undefined;\n        this.isDownloading = undefined;\n        this.dataLocation = \"none\";\n    }\n    // #endregion\n    // #region tensor utilities\n    ensureValid() {\n        if (this.dataLocation === \"none\") {\n            throw new Error(\"The tensor is disposed.\");\n        }\n    }\n    reshape(dims) {\n        this.ensureValid();\n        if (this.downloader || this.disposer) {\n            throw new Error(\"Cannot reshape a tensor that owns GPU resource.\");\n        }\n        return (0,_tensor_utils_impl_js__WEBPACK_IMPORTED_MODULE_3__.tensorReshape)(this, dims);\n    }\n} //# sourceMappingURL=tensor-impl.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-utils-impl.js":
/*!***********************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/tensor-utils-impl.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSize: () => (/* binding */ calculateSize),\n/* harmony export */   tensorReshape: () => (/* binding */ tensorReshape)\n/* harmony export */ });\n/* harmony import */ var _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tensor-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n/**\n * calculate size from dims.\n *\n * @param dims the dims array. May be an illegal input.\n */ const calculateSize = (dims)=>{\n    let size = 1;\n    for(let i = 0; i < dims.length; i++){\n        const dim = dims[i];\n        if (typeof dim !== \"number\" || !Number.isSafeInteger(dim)) {\n            throw new TypeError(`dims[${i}] must be an integer, got: ${dim}`);\n        }\n        if (dim < 0) {\n            throw new RangeError(`dims[${i}] must be a non-negative integer, got: ${dim}`);\n        }\n        size *= dim;\n    }\n    return size;\n};\n/**\n * implementation of Tensor.reshape()\n */ const tensorReshape = (tensor, dims)=>{\n    switch(tensor.location){\n        case \"cpu\":\n            return new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor(tensor.type, tensor.data, dims);\n        case \"cpu-pinned\":\n            return new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor({\n                location: \"cpu-pinned\",\n                data: tensor.data,\n                type: tensor.type,\n                dims\n            });\n        case \"texture\":\n            return new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor({\n                location: \"texture\",\n                texture: tensor.texture,\n                type: tensor.type,\n                dims\n            });\n        case \"gpu-buffer\":\n            return new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor({\n                location: \"gpu-buffer\",\n                gpuBuffer: tensor.gpuBuffer,\n                type: tensor.type,\n                dims\n            });\n        case \"ml-tensor\":\n            return new _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor({\n                location: \"ml-tensor\",\n                mlTensor: tensor.mlTensor,\n                type: tensor.type,\n                dims\n            });\n        default:\n            throw new Error(`tensorReshape: tensor location ${tensor.location} is not supported`);\n    }\n}; //# sourceMappingURL=tensor-utils-impl.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-utils-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor.js":
/*!************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/tensor.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tensor: () => (/* binding */ Tensor)\n/* harmony export */ });\n/* harmony import */ var _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tensor-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nconst Tensor = _tensor_impl_js__WEBPACK_IMPORTED_MODULE_0__.Tensor; //# sourceMappingURL=tensor.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL3RlbnNvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLDREQUE0RDtBQUM1RCxrQ0FBa0M7QUFDc0I7QUFDeEQsZ0VBQWdFO0FBQ3pELE1BQU1BLFNBQVNDLG1EQUFVQSxDQUFDLENBQ2pDLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ltYWdlLXRleHQtc3R1ZGlvLTAzLy4vbm9kZV9tb2R1bGVzL29ubnhydW50aW1lLWNvbW1vbi9kaXN0L2VzbS90ZW5zb3IuanM/NjcxNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS5cbmltcG9ydCB7IFRlbnNvciBhcyBUZW5zb3JJbXBsIH0gZnJvbSAnLi90ZW5zb3ItaW1wbC5qcyc7XG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25hbWluZy1jb252ZW50aW9uXG5leHBvcnQgY29uc3QgVGVuc29yID0gVGVuc29ySW1wbDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRlbnNvci5qcy5tYXAiXSwibmFtZXMiOlsiVGVuc29yIiwiVGVuc29ySW1wbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/tensor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/trace.js":
/*!***********************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/trace.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TRACE: () => (/* binding */ TRACE),\n/* harmony export */   TRACE_FUNC_BEGIN: () => (/* binding */ TRACE_FUNC_BEGIN),\n/* harmony export */   TRACE_FUNC_END: () => (/* binding */ TRACE_FUNC_END)\n/* harmony export */ });\n/* harmony import */ var _env_impl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./env-impl.js */ \"(ssr)/./node_modules/onnxruntime-common/dist/esm/env-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n/**\n * @ignore\n */ const TRACE = (deviceType, label)=>{\n    if (typeof _env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env.trace === \"undefined\" ? !_env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env.wasm.trace : !_env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env.trace) {\n        return;\n    }\n    // eslint-disable-next-line no-console\n    console.timeStamp(`${deviceType}::ORT::${label}`);\n};\nconst TRACE_FUNC = (msg, extraMsg)=>{\n    const stack = new Error().stack?.split(/\\r\\n|\\r|\\n/g) || [];\n    let hasTraceFunc = false;\n    for(let i = 0; i < stack.length; i++){\n        if (hasTraceFunc && !stack[i].includes(\"TRACE_FUNC\")) {\n            let label = `FUNC_${msg}::${stack[i].trim().split(\" \")[1]}`;\n            if (extraMsg) {\n                label += `::${extraMsg}`;\n            }\n            TRACE(\"CPU\", label);\n            return;\n        }\n        if (stack[i].includes(\"TRACE_FUNC\")) {\n            hasTraceFunc = true;\n        }\n    }\n};\n/**\n * @ignore\n */ const TRACE_FUNC_BEGIN = (extraMsg)=>{\n    if (typeof _env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env.trace === \"undefined\" ? !_env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env.wasm.trace : !_env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env.trace) {\n        return;\n    }\n    TRACE_FUNC(\"BEGIN\", extraMsg);\n};\n/**\n * @ignore\n */ const TRACE_FUNC_END = (extraMsg)=>{\n    if (typeof _env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env.trace === \"undefined\" ? !_env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env.wasm.trace : !_env_impl_js__WEBPACK_IMPORTED_MODULE_0__.env.trace) {\n        return;\n    }\n    TRACE_FUNC(\"END\", extraMsg);\n}; //# sourceMappingURL=trace.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/trace.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/onnxruntime-common/dist/esm/version.js":
/*!*************************************************************!*\
  !*** ./node_modules/onnxruntime-common/dist/esm/version.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n// This file is generated by /js/scripts/update-version.ts\n// Do not modify file content manually.\nconst version = \"1.21.0-dev.20250206-d981b153d3\"; //# sourceMappingURL=version.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvZXNtL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDREQUE0RDtBQUM1RCxrQ0FBa0M7QUFDbEMsMERBQTBEO0FBQzFELHVDQUF1QztBQUNoQyxNQUFNQSxVQUFVLGlDQUFpQyxDQUN4RCxtQ0FBbUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWFnZS10ZXh0LXN0dWRpby0wMy8uL25vZGVfbW9kdWxlcy9vbm54cnVudGltZS1jb21tb24vZGlzdC9lc20vdmVyc2lvbi5qcz9mMTUzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuLy8gVGhpcyBmaWxlIGlzIGdlbmVyYXRlZCBieSAvanMvc2NyaXB0cy91cGRhdGUtdmVyc2lvbi50c1xuLy8gRG8gbm90IG1vZGlmeSBmaWxlIGNvbnRlbnQgbWFudWFsbHkuXG5leHBvcnQgY29uc3QgdmVyc2lvbiA9ICcxLjIxLjAtZGV2LjIwMjUwMjA2LWQ5ODFiMTUzZDMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOlsidmVyc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/onnxruntime-common/dist/esm/version.js\n");

/***/ })

};
;