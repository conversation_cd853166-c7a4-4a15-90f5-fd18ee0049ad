import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import './fonts.css'
import { FontPreloader } from '@/components/font-preloader'
import Script from 'next/script'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Image Text Studio - Free Image Text Editor',
  description: 'Create beautiful text overlays on images with our completely free editor. No signup required, unlimited use.',
  icons: {
    icon: '/favicon.jpg',
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        {/* High priority preconnect for Google Fonts - must be first */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* System fonts don't need to be loaded */}
        
        {/* Add preload hints for the most commonly used fonts */}
        <link rel="preload" as="font" href="https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2" crossOrigin="anonymous" />
        <link rel="preload" as="font" href="https://fonts.gstatic.com/s/opensans/v35/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVI.woff2" crossOrigin="anonymous" />
        <link rel="preload" as="font" href="https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Hw5aXo.woff2" crossOrigin="anonymous" />
        
        {/* Load common Google Fonts first with higher priority */}
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@400;500;600;700&family=Oswald:wght@400;500;600;700&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789" rel="stylesheet" />
        
        {/* Load the additional Google Fonts */}
        <link href="https://fonts.googleapis.com/css2?family=Big+Shoulders+Stencil:opsz,wght@10..72,100..900&family=Boldonse&family=Bytesized&family=Jaini&family=Noto+Sans+JP:wght@100..900&family=Noto+Sans+KR:wght@100..900&family=Akshar:wght@300..700&family=Allerta+Stencil&family=Hachi+Maru+Pop&family=Tangerine:wght@400;700&family=Birthstone&family=Hurricane&family=Winky+Sans:ital,wght@0,300..900;1,300..900&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789" rel="stylesheet" />
        
        {/* Load the rest of the Google Fonts */}
        <link href="https://fonts.googleapis.com/css2?family=ABeeZee:ital@0;1&family=Abel&family=Abril+Fatface&family=Acme&family=Alata&family=Albert+Sans:wght@100;200;300;400;500;600;700;800;900&family=Alegreya:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Alegreya+Sans:ital,wght@0,100;0,300;0,400;0,500;0,700;0,800;0,900;1,100;1,300;1,400;1,500;1,700;1,800;1,900&family=Alegreya+Sans+SC:ital,wght@0,100;0,300;0,400;0,500;0,700;0,800;0,900;1,100;1,300;1,400;1,500;1,700;1,800;1,900&family=Alfa+Slab+One&family=Alice&family=Amatic+SC:wght@400;700&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789" rel="stylesheet" />
        
        <link href="https://fonts.googleapis.com/css2?family=Anton&family=Archivo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Arimo:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&family=Asap:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Barlow:wght@100;200;300;400;500;600;700;800;900&family=Caveat:wght@400;500;600;700&family=Dancing+Script:wght@400;500;600;700&family=DM+Sans:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789" rel="stylesheet" />
        
        <link href="https://fonts.googleapis.com/css2?family=Fira+Sans:wght@100;200;300;400;500;600;700;800;900&family=Josefin+Sans:wght@100;200;300;400;500;600;700&family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&family=Mulish:wght@200;300;400;500;600;700;800;900&family=Nunito:wght@200;300;400;500;600;700;800;900&family=Oxygen:wght@300;400;700&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap&text=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789" rel="stylesheet" />
      </head>
      <body className={`${inter.className}`}>
        {/* Font loading indicator */}
        <div className="font-loading-spinner"></div>

        {/* Wrap content with loading class */}
        <div className="font-loading-content">
          <main className="min-h-screen">
            {children}
          </main>
        </div>

        {/* Font preloader component - dynamically loads all fonts */}
        <FontPreloader />
        
        {/* Critical font loading script - loads immediately */}
        <Script id="font-loader" strategy="beforeInteractive">
          {`
            // Mark document as loading fonts ASAP
            document.documentElement.classList.add('fonts-loading');
            
            // Initialize document once the initial core fonts are loaded
            if ('fonts' in document) {
              Promise.all([
                document.fonts.load('1em "Roboto"'),
                document.fonts.load('1em "Open Sans"'),
                document.fonts.load('1em "Montserrat"'),
                document.fonts.load('1em "Poppins"')
              ]).then(() => {
                // Show content with core fonts loaded
                document.documentElement.classList.add('fonts-core-loaded');
              }).catch(() => {
                // Fallback if font loading fails
                setTimeout(() => {
                  document.documentElement.classList.add('fonts-core-loaded');
                }, 500);
              });
            } else {
              // Browser doesn't support font loading API
              document.documentElement.classList.add('fonts-core-loaded');
            }
          `}
        </Script>
      </body>
    </html>
  )
}