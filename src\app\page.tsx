'use client';

import { useState } from 'react';
import { Studio } from "@/components/studio/studio";
import Image from 'next/image';
import Link from 'next/link';

export default function Home() {
  const [showEditor, setShowEditor] = useState(false);

  // If user wants to use the editor, show it directly
  if (showEditor) {
    return (
      <main className="fixed inset-0 h-screen w-full overflow-hidden flex flex-col bg-black no-scroll">
        <Studio />
      </main>
    );
  }

  // Landing page for users
  return (
    <div className="min-h-screen bg-black text-white overflow-y-auto">
      {/* Navbar */}
      <nav className="border-b border-gray-800 py-5 px-6 md:px-12 flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-violet-500">
            Image Text Studio
          </span>
        </div>
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowEditor(true)}
            className="bg-gradient-to-r from-blue-500 to-violet-500 px-4 py-2 rounded-lg text-white font-medium hover:opacity-90 transition-all"
          >
            Start Editing
          </button>
        </div>
      </nav>
      
      {/* Hero Section */}
      <section className="relative pt-24 pb-32 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-radial from-violet-900/20 via-transparent to-transparent opacity-60"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-gradient-radial from-blue-900/20 via-transparent to-transparent opacity-60"></div>
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-6 text-center">
          <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight">
            Create <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-violet-500">stunning designs</span> with text
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed">
            Your completely free platform for adding beautiful text overlays to your images with professional tools and effects
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4 mb-20">
            <button
              onClick={() => setShowEditor(true)}
              className="bg-gradient-to-r from-blue-500 to-violet-500 px-8 py-4 rounded-lg text-lg font-semibold hover:opacity-90 transition-all shadow-lg"
            >
              Start Creating - It's Free!
            </button>
            <div className="bg-white/10 backdrop-blur-sm px-8 py-4 rounded-lg text-lg font-semibold border border-white/20">
              ✨ No signup required • Unlimited use
            </div>
          </div>
          
          <div className="relative mx-auto max-w-5xl">
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/30 to-violet-500/30 rounded-xl blur-xl opacity-70"></div>
            <div className="relative bg-gray-900/80 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden shadow-2xl">
              <Image 
                src="/layer-system.png" 
                alt="Image Text Studio in action" 
                width={1200} 
                height={800}
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>
      
      {/* Features Section */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-5xl font-bold mb-6">Powerful Features - All Free</h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Everything you need to create professional text overlays for your images, completely free with no limitations
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-8 hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-violet-500 rounded-lg mb-6 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Text Behind Subject</h3>
              <p className="text-gray-400 mb-4">
                Place text behind your subject with automatic subject detection and isolation.
              </p>
              <ul className="text-gray-400 space-y-2">
                <li className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  Automatic subject detection
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  No manual masking required
                </li>
              </ul>
            </div>
            
            {/* Feature 2 */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-8 hover:shadow-xl hover:shadow-violet-500/10 transition-all duration-300">
              <div className="w-14 h-14 bg-gradient-to-br from-violet-500 to-blue-500 rounded-lg mb-6 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Advanced Layer System</h3>
              <p className="text-gray-400 mb-4">
                Take complete control with our advanced layer management system.
              </p>
              <ul className="text-gray-400 space-y-2">
                <li className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-violet-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  Precise layer control
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-violet-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  Instant preview changes
                </li>
              </ul>
            </div>
            
            {/* Feature 3 */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-8 hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-violet-500 rounded-lg mb-6 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Text & Background Effects</h3>
              <p className="text-gray-400 mb-4">
                Apply beautiful blur effects to both text and background elements.
              </p>
              <ul className="text-gray-400 space-y-2">
                <li className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  Adjustable blur intensity
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  Create depth through blurring
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>
      
      {/* Image Showcase Section */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">Image Showcase</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="group relative rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/20">
              <Image 
                src="/Text-behind-subject.jpg" 
                alt="Text Behind Subject" 
                width={500} 
                height={375}
                className="w-full h-auto transition-transform duration-700 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-70 transition-opacity duration-300"></div>
              <div className="absolute bottom-0 left-0 p-6 translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                <h3 className="text-xl font-bold mb-2">Text Behind Subject</h3>
                <p className="text-sm text-gray-300">Create depth with text behind your main subject</p>
              </div>
            </div>
            
            <div className="group relative rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-violet-500/20">
              <Image 
                src="/ocean.jpg" 
                alt="Ocean Scene" 
                width={700} 
                height={500}
                className="w-full h-auto transition-transform duration-700 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-70 transition-opacity duration-300"></div>
              <div className="absolute bottom-0 left-0 p-6 translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                <h3 className="text-xl font-bold mb-2">Ocean Scenes</h3>
                <p className="text-sm text-gray-300">Perfect for travel and inspirational content</p>
              </div>
            </div>
            
            <div className="group relative rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/20">
              <Image 
                src="/tower.jpg" 
                alt="Urban Photography" 
                width={700} 
                height={500}
                className="w-full h-auto transition-transform duration-700 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-70 transition-opacity duration-300"></div>
              <div className="absolute bottom-0 left-0 p-6 translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                <h3 className="text-xl font-bold mb-2">Urban Photography</h3>
                <p className="text-sm text-gray-300">Create striking urban-themed designs</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Call to Action */}
      <section className="py-24 bg-gray-900 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-violet-900/20"></div>
        <div className="relative z-10 max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">Ready to create amazing designs?</h2>
          <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">
            Start creating professional-quality designs right now with our completely free editor. No signup, no limits, no catch.
          </p>
          <button
            onClick={() => setShowEditor(true)}
            className="bg-gradient-to-r from-blue-500 to-violet-500 px-10 py-4 rounded-lg text-lg font-semibold hover:opacity-90 transition-all shadow-lg"
          >
            Start Creating Now — It's Free
          </button>
        </div>
      </section>
      
      {/* Footer */}
      <footer className="py-12 bg-black text-gray-400 text-sm border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-8 md:mb-0">
              <p className="text-center md:text-left">Image-Text Studio v2.0 - Free Edition<br />© 2025 Image-Text Studio. Crafted with ❤️ by Bhanu</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-6">
              <div className="space-y-3">
                <h3 className="font-semibold text-white mb-2">Legal</h3>
                <div className="flex flex-col gap-2">
                  <Link href="/terms" className="hover:text-blue-400 transition-colors">
                    Terms of Use
                  </Link>
                  <Link href="/privacy-policy" className="hover:text-blue-400 transition-colors">
                    Privacy Policy
                  </Link>
                  <Link href="/disclaimer" className="hover:text-blue-400 transition-colors">
                    Disclaimer
                  </Link>
                </div>
              </div>
              <div className="space-y-3">
                <h3 className="font-semibold text-white mb-2">Company</h3>
                <div className="flex flex-col gap-2">
                  <Link href="/about" className="hover:text-blue-400 transition-colors">
                    About Us
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
} 