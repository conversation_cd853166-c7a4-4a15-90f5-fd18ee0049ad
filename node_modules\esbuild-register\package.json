{"name": "esbuild-register", "description": "Transpile JSX, TypeScript and esnext features on the fly with esbuild", "version": "3.6.0", "main": "register.js", "license": "MIT", "files": ["dist", "/register.js", "/loader.js"], "exports": {".": "./register.js", "./loader": "./loader.js", "./dist/node": "./dist/node.js", "./dist/*": "./dist/*"}, "scripts": {"build": "tsup src/node.ts src/loader.ts --dts", "test": "npm run build && node -r ./register.js tests/test.ts", "prepublishOnly": "npm run build"}, "devDependencies": {"@egoist/prettier-config": "^0.1.0", "@types/debug": "^4.1.7", "@types/node": "^14.0.23", "@types/source-map-support": "^0.5.3", "esbuild": "0.15.13", "execa": "^4.0.3", "joycon": "^2.2.5", "pirates": "^4.0.1", "semantic-release": "^24.0.0", "source-map": "0.7.3", "source-map-support": "^0.5.19", "strip-json-comments": "^4.0.0", "tsconfig-paths": "^4.2.0", "tsup": "^4.7.1", "typescript": "^4.8.4", "uvu": "0.5.2"}, "peerDependencies": {"esbuild": ">=0.12 <1"}, "dependencies": {"debug": "^4.3.4"}, "packageManager": "pnpm@9.6.0+sha512.38dc6fba8dba35b39340b9700112c2fe1e12f10b17134715a4aa98ccf7bb035e76fd981cf0bb384dfa98f8d6af5481c2bef2f4266a24bfa20c34eb7147ce0b5e"}