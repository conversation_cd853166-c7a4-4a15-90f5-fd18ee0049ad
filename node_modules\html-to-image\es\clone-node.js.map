{"version": 3, "file": "clone-node.js", "sourceRoot": "", "sources": ["../src/clone-node.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAA;AACrD,OAAO,EACL,WAAW,EACX,OAAO,EACP,mBAAmB,EACnB,kBAAkB,GACnB,MAAM,QAAQ,CAAA;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,SAAS,CAAA;AACrC,OAAO,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAA;AAE7C,KAAK,UAAU,kBAAkB,CAAC,MAAyB;IACzD,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,CAAA;IAClC,IAAI,OAAO,KAAK,QAAQ,EAAE;QACxB,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAsB,CAAA;KACpD;IACD,OAAO,WAAW,CAAC,OAAO,CAAC,CAAA;AAC7B,CAAC;AAED,KAAK,UAAU,iBAAiB,CAAC,KAAuB,EAAE,OAAgB;IACxE,IAAI,KAAK,CAAC,UAAU,EAAE;QACpB,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAC/C,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACnC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAA;QAChC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAA;QAClC,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;QACxD,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,CAAA;QAClC,OAAO,WAAW,CAAC,OAAO,CAAC,CAAA;KAC5B;IAED,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;IAC3B,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAA;IACvC,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAA;IACrE,OAAO,WAAW,CAAC,OAAO,CAAC,CAAA;AAC7B,CAAC;AAED,KAAK,UAAU,kBAAkB,CAAC,MAAyB,EAAE,OAAgB;;IAC3E,IAAI;QACF,IAAI,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,eAAe,0CAAE,IAAI,EAAE;YACjC,OAAO,CAAC,MAAM,SAAS,CACrB,MAAM,CAAC,eAAe,CAAC,IAAI,EAC3B,OAAO,EACP,IAAI,CACL,CAAoB,CAAA;SACtB;KACF;IAAC,WAAM;QACN,yBAAyB;KAC1B;IAED,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAsB,CAAA;AACrD,CAAC;AAED,KAAK,UAAU,eAAe,CAC5B,IAAO,EACP,OAAgB;IAEhB,IAAI,mBAAmB,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE;QAChD,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAA;KAChC;IAED,IAAI,mBAAmB,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE;QAC/C,OAAO,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;KACxC;IAED,IAAI,mBAAmB,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE;QAChD,OAAO,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;KACzC;IAED,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAM,CAAA;AAChD,CAAC;AAED,MAAM,aAAa,GAAG,CAAC,IAAiB,EAA2B,EAAE,CACnE,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,MAAM,CAAA;AAE/D,MAAM,YAAY,GAAG,CAAC,IAAiB,EAA2B,EAAE,CAClE,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,KAAK,CAAA;AAE9D,KAAK,UAAU,aAAa,CAC1B,UAAa,EACb,UAAa,EACb,OAAgB;;IAEhB,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,UAAU,CAAA;KAClB;IAED,IAAI,QAAQ,GAAQ,EAAE,CAAA;IAEtB,IAAI,aAAa,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,aAAa,EAAE;QACzD,QAAQ,GAAG,OAAO,CAAI,UAAU,CAAC,aAAa,EAAE,CAAC,CAAA;KAClD;SAAM,IACL,mBAAmB,CAAC,UAAU,EAAE,iBAAiB,CAAC;SAClD,MAAA,UAAU,CAAC,eAAe,0CAAE,IAAI,CAAA,EAChC;QACA,QAAQ,GAAG,OAAO,CAAI,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;KAClE;SAAM;QACL,QAAQ,GAAG,OAAO,CAAI,CAAC,MAAA,UAAU,CAAC,UAAU,mCAAI,UAAU,CAAC,CAAC,UAAU,CAAC,CAAA;KACxE;IAED,IACE,QAAQ,CAAC,MAAM,KAAK,CAAC;QACrB,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,EACjD;QACA,OAAO,UAAU,CAAA;KAClB;IAED,MAAM,QAAQ,CAAC,MAAM,CACnB,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAClB,QAAQ;SACL,IAAI,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;SACrC,IAAI,CAAC,CAAC,WAA+B,EAAE,EAAE;QACxC,IAAI,WAAW,EAAE;YACf,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;SACpC;IACH,CAAC,CAAC,EACN,OAAO,CAAC,OAAO,EAAE,CAClB,CAAA;IAED,OAAO,UAAU,CAAA;AACnB,CAAC;AAED,SAAS,aAAa,CACpB,UAAa,EACb,UAAa,EACb,OAAgB;IAEhB,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAA;IACpC,IAAI,CAAC,WAAW,EAAE;QAChB,OAAM;KACP;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IACvD,IAAI,WAAW,CAAC,OAAO,EAAE;QACvB,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAA;QACzC,WAAW,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAA;KAC1D;SAAM;QACL,kBAAkB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC3C,IAAI,KAAK,GAAG,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;YAC9C,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAChD,MAAM,WAAW,GACf,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;gBACpE,KAAK,GAAG,GAAG,WAAW,IAAI,CAAA;aAC3B;YAED,IACE,mBAAmB,CAAC,UAAU,EAAE,iBAAiB,CAAC;gBAClD,IAAI,KAAK,SAAS;gBAClB,KAAK,KAAK,QAAQ,EAClB;gBACA,KAAK,GAAG,OAAO,CAAA;aAChB;YAED,IAAI,IAAI,KAAK,GAAG,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;gBAChD,KAAK,GAAG,QAAQ,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAA;aAChD;YAED,WAAW,CAAC,WAAW,CACrB,IAAI,EACJ,KAAK,EACL,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,CACtC,CAAA;QACH,CAAC,CAAC,CAAA;KACH;AACH,CAAC;AAED,SAAS,eAAe,CAAwB,UAAa,EAAE,UAAa;IAC1E,IAAI,mBAAmB,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE;QACxD,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,KAAK,CAAA;KACxC;IAED,IAAI,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE;QACrD,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,CAAA;KACnD;AACH,CAAC;AAED,SAAS,gBAAgB,CAAwB,UAAa,EAAE,UAAa;IAC3E,IAAI,mBAAmB,CAAC,UAAU,EAAE,iBAAiB,CAAC,EAAE;QACtD,MAAM,YAAY,GAAG,UAAsC,CAAA;QAC3D,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAC3D,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAC5D,CAAA;QAED,IAAI,cAAc,EAAE;YAClB,cAAc,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;SAC5C;KACF;AACH,CAAC;AAED,SAAS,QAAQ,CACf,UAAa,EACb,UAAa,EACb,OAAgB;IAEhB,IAAI,mBAAmB,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE;QAC5C,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;QAC9C,mBAAmB,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;QACpD,eAAe,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QACvC,gBAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;KACzC;IAED,OAAO,UAAU,CAAA;AACnB,CAAC;AAED,KAAK,UAAU,gBAAgB,CAC7B,KAAQ,EACR,OAAgB;IAEhB,MAAM,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;IACxE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO,KAAK,CAAA;KACb;IAED,MAAM,aAAa,GAAmC,EAAE,CAAA;IACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,MAAM,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAA;QACzC,IAAI,EAAE,EAAE;YACN,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;YACrC,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAgB,CAAA;YAC5D,IAAI,CAAC,KAAK,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,EAAE;gBAC9C,4CAA4C;gBAC5C,aAAa,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAE,CAAA;aAClE;SACF;KACF;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;IAC1C,IAAI,KAAK,CAAC,MAAM,EAAE;QAChB,MAAM,EAAE,GAAG,8BAA8B,CAAA;QACzC,MAAM,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;QAC/C,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QAC7B,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAA;QAC/B,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAA;QACrB,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAA;QACtB,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAC7B,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAA;QAE1B,MAAM,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;QACjD,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;SAC3B;QAED,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;KACvB;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,IAAO,EACP,OAAgB,EAChB,MAAgB;IAEhB,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QACtD,OAAO,IAAI,CAAA;KACZ;IAED,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;SACzB,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAe,CAAC;SACxE,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;SAC9D,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;SACzD,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;AAChE,CAAC"}