{"version": 3, "file": "embed-webfonts.js", "sourceRoot": "", "sources": ["../src/embed-webfonts.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,+BAAgC;AAChC,qCAA0C;AAC1C,qDAA+D;AAO/D,IAAM,aAAa,GAAiC,EAAE,CAAA;AAEtD,SAAe,QAAQ,CAAC,GAAW;;;;;;oBAC7B,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,CAAA;oBAC9B,IAAI,KAAK,IAAI,IAAI,EAAE;wBACjB,sBAAO,KAAK,EAAA;qBACb;oBAEW,qBAAM,KAAK,CAAC,GAAG,CAAC,EAAA;;oBAAtB,GAAG,GAAG,SAAgB;oBACZ,qBAAM,GAAG,CAAC,IAAI,EAAE,EAAA;;oBAA1B,OAAO,GAAG,SAAgB;oBAChC,KAAK,GAAG,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,CAAA;oBAExB,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;oBAE1B,sBAAO,KAAK,EAAA;;;;CACb;AAED,SAAe,UAAU,CAAC,IAAc,EAAE,OAAgB;;;;;YACpD,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;YACpB,QAAQ,GAAG,6BAA6B,CAAA;YACxC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,CAAA;YAC/C,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAO,GAAW;;;oBAC3C,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;oBACrC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;wBAC/B,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAA;qBAClC;oBAED,sBAAO,IAAA,wBAAc,EACnB,GAAG,EACH,OAAO,CAAC,gBAAgB,EACxB,UAAC,EAAU;gCAAR,MAAM,YAAA;4BACP,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,cAAO,MAAM,MAAG,CAAC,CAAA;4BAChD,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;wBACtB,CAAC,CACF,EAAA;;iBACF,CAAC,CAAA;YAEF,sBAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,OAAO,EAAP,CAAO,CAAC,EAAA;;;CAClD;AAED,SAAS,QAAQ,CAAC,MAAc;IAC9B,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,OAAO,EAAE,CAAA;KACV;IAED,IAAM,MAAM,GAAa,EAAE,CAAA;IAC3B,IAAM,aAAa,GAAG,sBAAsB,CAAA;IAC5C,qBAAqB;IACrB,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;IAE/C,iDAAiD;IACjD,IAAM,cAAc,GAAG,IAAI,MAAM,CAC/B,kDAAkD,EAClD,IAAI,CACL,CAAA;IAED,iDAAiD;IACjD,OAAO,IAAI,EAAE;QACX,IAAM,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC5C,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,MAAK;SACN;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;KACxB;IACD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAA;IAE7C,IAAM,WAAW,GAAG,wCAAwC,CAAA;IAC5D,wCAAwC;IACxC,IAAM,gBAAgB,GACpB,uDAAuD;QACvD,uDAAuD,CAAA;IACzD,gBAAgB;IAChB,IAAM,YAAY,GAAG,IAAI,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;IAEvD,iDAAiD;IACjD,OAAO,IAAI,EAAE;QACX,IAAI,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACvC,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACpC,IAAI,OAAO,KAAK,IAAI,EAAE;gBACpB,MAAK;aACN;iBAAM;gBACL,WAAW,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAA;aAC/C;SACF;aAAM;YACL,YAAY,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAA;SAC/C;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;KACxB;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAe,WAAW,CACxB,WAA4B,EAC5B,OAAgB;;;;YAEV,GAAG,GAAmB,EAAE,CAAA;YACxB,SAAS,GAA6B,EAAE,CAAA;YAE9C,6BAA6B;YAC7B,WAAW,CAAC,OAAO,CAAC,UAAC,KAAK;gBACxB,IAAI,UAAU,IAAI,KAAK,EAAE;oBACvB,IAAI;wBACF,IAAA,cAAO,EAAU,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,KAAK;4BACzD,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,WAAW,EAAE;gCACrC,IAAI,aAAW,GAAG,KAAK,GAAG,CAAC,CAAA;gCAC3B,IAAM,GAAG,GAAI,IAAsB,CAAC,IAAI,CAAA;gCACxC,IAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC;qCAC3B,IAAI,CAAC,UAAC,QAAQ,IAAK,OAAA,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,EAA7B,CAA6B,CAAC;qCACjD,IAAI,CAAC,UAAC,OAAO;oCACZ,OAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;wCAC7B,IAAI;4CACF,KAAK,CAAC,UAAU,CACd,IAAI,EACJ,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;gDACxB,CAAC,CAAC,CAAC,aAAW,IAAI,CAAC,CAAC;gDACpB,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAC1B,CAAA;yCACF;wCAAC,OAAO,KAAK,EAAE;4CACd,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE;gDACpD,IAAI,MAAA;gDACJ,KAAK,OAAA;6CACN,CAAC,CAAA;yCACH;oCACH,CAAC,CAAC;gCAdF,CAcE,CACH;qCACA,KAAK,CAAC,UAAC,CAAC;oCACP,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;gCACzD,CAAC,CAAC,CAAA;gCAEJ,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;6BACzB;wBACH,CAAC,CAAC,CAAA;qBACH;oBAAC,OAAO,CAAC,EAAE;wBACV,IAAM,QAAM,GACV,WAAW,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,IAAI,IAAI,EAAd,CAAc,CAAC,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;wBACpE,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;4BACtB,SAAS,CAAC,IAAI,CACZ,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;iCACjB,IAAI,CAAC,UAAC,QAAQ,IAAK,OAAA,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,EAA7B,CAA6B,CAAC;iCACjD,IAAI,CAAC,UAAC,OAAO;gCACZ,OAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;oCAC7B,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,QAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;gCACjD,CAAC,CAAC;4BAFF,CAEE,CACH;iCACA,KAAK,CAAC,UAAC,GAAY;gCAClB,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAA;4BACvD,CAAC,CAAC,CACL,CAAA;yBACF;wBACD,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,CAAC,CAAC,CAAA;qBACnD;iBACF;YACH,CAAC,CAAC,CAAA;YAEF,sBAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;oBACjC,2BAA2B;oBAC3B,WAAW,CAAC,OAAO,CAAC,UAAC,KAAK;wBACxB,IAAI,UAAU,IAAI,KAAK,EAAE;4BACvB,IAAI;gCACF,IAAA,cAAO,EAAe,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;oCACvD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gCAChB,CAAC,CAAC,CAAA;6BACH;4BAAC,OAAO,CAAC,EAAE;gCACV,OAAO,CAAC,KAAK,CAAC,6CAAsC,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC,CAAC,CAAA;6BACrE;yBACF;oBACH,CAAC,CAAC,CAAA;oBAEF,OAAO,GAAG,CAAA;gBACZ,CAAC,CAAC,EAAA;;;CACH;AAED,SAAS,eAAe,CAAC,QAAwB;IAC/C,OAAO,QAAQ;SACZ,MAAM,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,cAAc,EAApC,CAAoC,CAAC;SACtD,MAAM,CAAC,UAAC,IAAI,IAAK,OAAA,IAAA,6BAAW,EAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAA/C,CAA+C,CAAC,CAAA;AACtE,CAAC;AAED,SAAe,iBAAiB,CAC9B,IAAO,EACP,OAAgB;;;;;;oBAEhB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;wBAC9B,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;qBAC7D;oBAEK,WAAW,GAAG,IAAA,cAAO,EAAgB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAA;oBACzD,qBAAM,WAAW,CAAC,WAAW,EAAE,OAAO,CAAC,EAAA;;oBAAlD,QAAQ,GAAG,SAAuC;oBAExD,sBAAO,eAAe,CAAC,QAAQ,CAAC,EAAA;;;;CACjC;AAED,SAAS,mBAAmB,CAAC,IAAY;IACvC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;AACzC,CAAC;AAED,SAAS,YAAY,CAAC,IAAiB;IACrC,IAAM,KAAK,GAAG,IAAI,GAAG,EAAU,CAAA;IAC/B,SAAS,QAAQ,CAAC,IAAiB;QACjC,IAAM,UAAU,GACd,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,UAAU,CAAA;QAC5D,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;YACjC,KAAK,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QAEF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAC,KAAK;YACtC,IAAI,KAAK,YAAY,WAAW,EAAE;gBAChC,QAAQ,CAAC,KAAK,CAAC,CAAA;aAChB;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IACD,QAAQ,CAAC,IAAI,CAAC,CAAA;IACd,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAsB,aAAa,CACjC,IAAO,EACP,OAAgB;;;;;wBAEF,qBAAM,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAA;;oBAA9C,KAAK,GAAG,SAAsC;oBAC9C,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;oBACnB,qBAAM,OAAO,CAAC,GAAG,CAChC,KAAK;6BACF,MAAM,CAAC,UAAC,IAAI;4BACX,OAAA,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;wBAAzD,CAAyD,CAC1D;6BACA,GAAG,CAAC,UAAC,IAAI;4BACR,IAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB;gCACnC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI;gCAC5B,CAAC,CAAC,IAAI,CAAA;4BACR,OAAO,IAAA,gCAAc,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;wBACvD,CAAC,CAAC,CACL,EAAA;;oBAXK,QAAQ,GAAG,SAWhB;oBAED,sBAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA;;;;CAC3B;AApBD,sCAoBC;AAED,SAAsB,aAAa,CACjC,UAAa,EACb,OAAgB;;;;;;yBAGd,CAAA,OAAO,CAAC,YAAY,IAAI,IAAI,CAAA,EAA5B,wBAA4B;oBACxB,KAAA,OAAO,CAAC,YAAY,CAAA;;;yBACpB,OAAO,CAAC,SAAS,EAAjB,wBAAiB;oBACjB,KAAA,IAAI,CAAA;;wBACJ,qBAAM,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,EAAA;;oBAAxC,KAAA,SAAwC,CAAA;;;oBAFxC,QAEwC;;;oBALxC,OAAO,KAKiC;oBAE9C,IAAI,OAAO,EAAE;wBACL,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;wBAC3C,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;wBAErD,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;wBAEnC,IAAI,UAAU,CAAC,UAAU,EAAE;4BACzB,UAAU,CAAC,YAAY,CAAC,SAAS,EAAE,UAAU,CAAC,UAAU,CAAC,CAAA;yBAC1D;6BAAM;4BACL,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;yBAClC;qBACF;;;;;CACF;AAvBD,sCAuBC"}