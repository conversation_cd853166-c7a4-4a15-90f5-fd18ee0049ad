{"onnxruntime-web": {"source": "https://www.npmjs.com/package/onnxruntime-web", "type": "code", "license": "MIT"}, "ISNET": {"source": "https://github.com/xuebinqin/DIS", "type": "model", "license": "MIT"}, "lodash-es": {"source": "https://lodash.com/", "type": "code", "license": "MIT"}, "ndarray": {"source": "https://www.npmjs.com/package/ndarray", "type": "code", "license": "MIT"}, "zod": {"source": "https://www.npmjs.com/package/zod", "type": "code", "license": "MIT"}}