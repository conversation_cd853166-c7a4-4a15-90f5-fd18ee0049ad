"use client"

import { useStore } from "@/store/useStore"
import { But<PERSON> } from "@/components/ui/button"
import { Upload } from "lucide-react"
import { useCallback, useState } from "react"

export function ImageUpload() {
  const { setImage } = useStore()
  const [isUploading, setIsUploading] = useState(false)

  const handleImageUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Process the image directly without any authentication or subscription checks
    processImage(file)
  }, [setImage])

  const processImage = (file: File) => {
    setIsUploading(true)
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      if (result) {
        setImage(result)
      }
      setIsUploading(false)
    }
    reader.readAsDataURL(file)
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center overflow-hidden bg-black text-white">
      <div className="text-center">
        <h1 className="text-3xl md:text-4xl font-bold mb-6">Welcome to Image-Text Studio</h1>
        <p className="text-gray-400 mx-auto max-w-md mb-8">
          Upload an image to get started with our completely free advanced text overlay tools.
        </p>

        <div className="relative mx-auto flex justify-center">
          <input
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="absolute inset-0 cursor-pointer opacity-0 w-full h-full z-10"
            title="Upload an image"
            disabled={isUploading}
          />
          <Button
            variant="outline"
            size="lg"
            className="border-white/40 text-white hover:bg-white/20 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
            type="button"
            disabled={isUploading}
          >
            {isUploading ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : (
              <>
                <Upload className="mr-2 h-5 w-5" />
                Upload an image
              </>
            )}
          </Button>
        </div>

        <p className="mt-4 text-sm text-white/70">
          ✨ Completely free • No signup required • Unlimited use
        </p>

        <div className="mt-6 text-xs text-gray-500 max-w-lg mx-auto">
          <p>All features are available for free including text behind subject, advanced layer system, and professional effects.</p>
        </div>
      </div>
    </div>
  )
}