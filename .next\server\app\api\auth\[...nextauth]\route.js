"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_coding_HTML_Image_text_studio_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_coding_HTML_Image_text_studio_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase-admin */ \"(rsc)/./src/lib/supabase-admin.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async signIn ({ user, account, req }) {\n            if (account?.provider === \"google\" && user.email) {\n                try {\n                    // Get referral code from cookie if available\n                    let referredBy = null;\n                    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n                    const referralCode = cookieStore.get(\"referral_code\")?.value;\n                    if (referralCode) {\n                        // Find the referral by code\n                        try {\n                            const { data: referral, error: referralError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"referrals\").select(\"id, influencer_id\").eq(\"referral_code\", referralCode).single();\n                            if (!referralError && referral) {\n                                referredBy = referral.id;\n                            }\n                        } catch (error) {\n                            console.log(\"Error looking up referral, table might not exist yet:\", error);\n                        }\n                    }\n                    // Check if user exists in Supabase\n                    const { data: existingUser, error: queryError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"profiles\").select(\"id\").eq(\"email\", user.email).single();\n                    if (queryError && queryError.code !== \"PGRST116\") {\n                        console.error(\"Error checking for existing user:\", queryError);\n                        return false;\n                    }\n                    if (!existingUser) {\n                        // Create new user profile using admin client to bypass RLS\n                        const uuid = (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(); // Generate a valid UUID\n                        const { data: newProfile, error: profileError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"profiles\").insert([\n                            {\n                                id: uuid,\n                                email: user.email,\n                                full_name: user.name || \"\",\n                                avatar_url: user.image || \"\",\n                                is_admin: false,\n                                is_influencer: false,\n                                provider_id: user.id // Store the original provider ID\n                            }\n                        ]).select(\"id\").single();\n                        if (profileError) {\n                            console.error(\"Error creating profile:\", profileError);\n                            return false;\n                        }\n                        if (newProfile) {\n                            // Create initial subscription using admin client\n                            const { error: subscriptionError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"subscriptions\").insert([\n                                {\n                                    user_id: newProfile.id,\n                                    status: \"active\",\n                                    plan_type: \"free\",\n                                    free_edits_remaining: 5,\n                                    lite_edits_remaining: 0,\n                                    lite_edits_monthly_limit: 100,\n                                    subscription_start_date: new Date().toISOString(),\n                                    subscription_end_date: null,\n                                    last_reset_date: null,\n                                    payment_id: null,\n                                    referred_by: referredBy\n                                }\n                            ]);\n                            if (subscriptionError) {\n                                console.error(\"Error creating subscription:\", subscriptionError);\n                                return false;\n                            }\n                            // If this is a referral conversion, update the referral_visits record\n                            if (referredBy) {\n                                try {\n                                    // Get the most recent visit for this referral code\n                                    const { data: recentVisit, error: visitError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"referral_visits\").select(\"id\").eq(\"referral_id\", referredBy).order(\"visited_at\", {\n                                        ascending: false\n                                    }).limit(1).single();\n                                    if (!visitError && recentVisit) {\n                                        // Update the visit with the converted user ID\n                                        await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"referral_visits\").update({\n                                            converted_user_id: newProfile.id\n                                        }).eq(\"id\", recentVisit.id);\n                                    }\n                                } catch (error) {\n                                    console.log(\"Error updating referral visit, table might not exist yet:\", error);\n                                }\n                            }\n                        }\n                    }\n                    return true;\n                } catch (error) {\n                    console.error(\"Error during sign in:\", error);\n                    return false;\n                }\n            }\n            return true;\n        },\n        async session ({ session, token }) {\n            if (token?.sub) {\n                // Set the user ID from token\n                session.user.id = token.sub;\n            }\n            if (session.user?.email) {\n                try {\n                    console.log(\"Session callback for\", session.user.email);\n                    // Get user profile\n                    const { data: profile, error: profileError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"profiles\").select(\"id, email, full_name, avatar_url, is_admin, is_influencer, influencer_expiry_date\").eq(\"email\", session.user.email).single();\n                    if (profileError) {\n                        console.error(\"Error fetching profile:\", profileError);\n                        return session;\n                    }\n                    if (profile) {\n                        console.log(\"Found profile:\", profile);\n                        // Get subscription info\n                        const { data: subscription, error: subscriptionError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"subscriptions\").select(\"*\").eq(\"user_id\", profile.id).single();\n                        if (subscriptionError && subscriptionError.code !== \"PGRST116\") {\n                            console.error(\"Error fetching subscription:\", subscriptionError);\n                        }\n                        session.user.subscription = subscription || null;\n                        session.user.is_admin = profile.is_admin || false;\n                        session.user.is_influencer = profile.is_influencer || false;\n                        session.user.influencer_expiry_date = profile.influencer_expiry_date;\n                        // Check if influencer status has expired\n                        if (profile.is_influencer && profile.influencer_expiry_date) {\n                            const expiryDate = new Date(profile.influencer_expiry_date);\n                            if (expiryDate < new Date()) {\n                                // Influencer status has expired\n                                await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"profiles\").update({\n                                    is_influencer: false,\n                                    influencer_expiry_date: null\n                                }).eq(\"id\", profile.id);\n                                session.user.is_influencer = false;\n                                session.user.influencer_expiry_date = null;\n                            }\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error in session callback:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user, account, profile, trigger, session }) {\n            // When user signs in via provider\n            if (account && user) {\n                // First get the Supabase user ID from email\n                try {\n                    const { data: supabaseUser, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(\"profiles\").select(\"id\").eq(\"email\", user.email).single();\n                    if (!error && supabaseUser) {\n                        // Use Supabase ID, not provider ID\n                        token.sub = supabaseUser.id;\n                        token.is_admin = supabaseUser.is_admin || false;\n                        token.is_influencer = supabaseUser.is_influencer || false;\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching user for JWT:\", error);\n                }\n            }\n            // Handle session update\n            if (trigger === \"update\" && session?.user) {\n                token.subscription = session.user.subscription;\n                token.is_admin = session.user.is_admin;\n                token.is_influencer = session.user.is_influencer;\n                token.influencer_expiry_date = session.user.influencer_expiry_date;\n            }\n            return token;\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    }\n};\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase-admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase-admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Create a Supabase client with the service role key for admin operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://dpfkvqyrzizkuzlmhpfb.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlLWFkbWluLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEO0FBRXJELDBFQUEwRTtBQUNuRSxNQUFNQyxnQkFBZ0JELG1FQUFZQSxDQUN2Q0UsMENBQW9DLEVBQ3BDQSxRQUFRQyxHQUFHLENBQUNFLHlCQUF5QixFQUNyQztJQUNFQyxNQUFNO1FBQ0pDLGtCQUFrQjtRQUNsQkMsZ0JBQWdCO0lBQ2xCO0FBQ0YsR0FDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ltYWdlLXRleHQtc3R1ZGlvLTAzLy4vc3JjL2xpYi9zdXBhYmFzZS1hZG1pbi50cz8xYTNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcyc7XG5cbi8vIENyZWF0ZSBhIFN1cGFiYXNlIGNsaWVudCB3aXRoIHRoZSBzZXJ2aWNlIHJvbGUga2V5IGZvciBhZG1pbiBvcGVyYXRpb25zXG5leHBvcnQgY29uc3Qgc3VwYWJhc2VBZG1pbiA9IGNyZWF0ZUNsaWVudChcbiAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSEsXG4gIHtcbiAgICBhdXRoOiB7XG4gICAgICBhdXRvUmVmcmVzaFRva2VuOiBmYWxzZSxcbiAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxuICAgIH1cbiAgfVxuKTsgIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlQWRtaW4iLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsImF1dGgiLCJhdXRvUmVmcmVzaFRva2VuIiwicGVyc2lzdFNlc3Npb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase-admin.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/uuid","vendor-chunks/jose","vendor-chunks/@supabase","vendor-chunks/openid-client","vendor-chunks/whatwg-url","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/tr46","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/webidl-conversions","vendor-chunks/preact","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ccoding%5CHTML%5CImage-text-studio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();