"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: function() { return /* binding */ AppSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=File,Home,Info,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=File,Home,Info,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=File,Home,Info,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=File,Home,Info,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=File,Home,Info,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AppSidebar(param) {\n    let { className } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { setActiveLayerId } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    // Close sidebar when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (e)=>{\n            const target = e.target;\n            // Check if the click is outside the sidebar and not on the menu button\n            if (open && !target.closest(\"[data-sidebar]\") && !target.closest(\"[data-sidebar-trigger]\")) {\n                setOpen(false);\n            }\n        };\n        // When canvas is clicked, deselect active text layer and close sidebar\n        const handleCanvasClick = ()=>{\n            setOpen(false);\n        };\n        document.addEventListener(\"click\", handleClickOutside);\n        // Find and add event listener to the canvas element\n        const canvasElement = document.querySelector(\"[data-canvas]\");\n        if (canvasElement) {\n            canvasElement.addEventListener(\"click\", handleCanvasClick);\n        }\n        return ()=>{\n            document.removeEventListener(\"click\", handleClickOutside);\n            if (canvasElement) {\n                canvasElement.removeEventListener(\"click\", handleCanvasClick);\n            }\n        };\n    }, [\n        open,\n        setActiveLayerId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"icon\",\n                onClick: ()=>setOpen(!open),\n                className: \"absolute top-1 left-2 z-50 h-8 w-8 text-white hover:bg-white/10\",\n                \"data-sidebar-trigger\": \"true\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: \"Toggle Menu\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black/50\",\n                onClick: ()=>setOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-sidebar\": \"true\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed top-0 left-0 z-50 h-full w-64 bg-black border-r border-white/10 p-4 shadow-xl transition-transform duration-200 ease-in-out\", open ? \"translate-x-0\" : \"-translate-x-full\", className),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: \"Image-Text Studio\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setOpen(false),\n                                className: \"h-8 w-8 text-white hover:bg-white/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 py-4 border-b border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-white font-medium mb-2\",\n                                    children: \"✨ Completely Free Tool\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-white/60\",\n                                    children: \"No signup required • Unlimited use • All features available\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/\",\n                                className: \"block w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-start text-white hover:bg-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                href: \"/about\",\n                                className: \"block w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-start text-white hover:bg-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"About Us\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 mt-4 border-t border-white/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-white/60 mb-2 px-2\",\n                                        children: \"Legal & Policies\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/terms\",\n                                        className: \"block w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-white hover:bg-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Terms of Use\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/privacy-policy\",\n                                        className: \"block w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-white hover:bg-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Privacy Policy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/disclaimer\",\n                                        className: \"block w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"w-full justify-start text-white hover:bg-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_File_Home_Info_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Disclaimer\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-4 left-0 right-0 px-4 text-white/60 text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Image-Text Studio v1.0\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2025 Image-Text Studio. Crafted with ❤️ by Bhanu\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AppSidebar, \"xWmlrQ0gIcmEhZuHBVpQnD/HRBc=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});