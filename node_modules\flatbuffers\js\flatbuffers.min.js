"use strict";var flatbuffers=(()=>{var b=(l,t)=>()=>(t||l((t={exports:{}}).exports,t),t.exports);var _=b(c=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0});c.SIZE_PREFIX_LENGTH=c.FILE_IDENTIFIER_LENGTH=c.SIZEOF_INT=c.SIZEOF_SHORT=void 0;c.SIZEOF_SHORT=2;c.SIZEOF_INT=4;c.FILE_IDENTIFIER_LENGTH=4;c.SIZE_PREFIX_LENGTH=4});var y=b(o=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0});o.isLittleEndian=o.float64=o.float32=o.int32=void 0;o.int32=new Int32Array(2);o.float32=new Float32Array(o.int32.buffer);o.float64=new Float64Array(o.int32.buffer);o.isLittleEndian=new Uint16Array(new Uint8Array([1,0]).buffer)[0]===1});var g=b(f=>{"use strict";Object.defineProperty(f,"__esModule",{value:!0});f.Encoding=void 0;var S;(function(l){l[l.UTF8_BYTES=1]="UTF8_BYTES",l[l.UTF16_STRING=2]="UTF16_STRING"})(S||(f.Encoding=S={}))});var T=b(E=>{"use strict";Object.defineProperty(E,"__esModule",{value:!0});E.ByteBuffer=void 0;var d=_(),n=y(),m=g(),w=class l{constructor(t){this.bytes_=t,this.position_=0,this.text_decoder_=new TextDecoder}static allocate(t){return new l(new Uint8Array(t))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return BigInt.asIntN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readUint64(t){return BigInt.asUintN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readFloat32(t){return n.int32[0]=this.readInt32(t),n.float32[0]}readFloat64(t){return n.int32[n.isLittleEndian?0:1]=this.readInt32(t),n.int32[n.isLittleEndian?1:0]=this.readInt32(t+4),n.float64[0]}writeInt8(t,e){this.bytes_[t]=e}writeUint8(t,e){this.bytes_[t]=e}writeInt16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeUint16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeInt32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeUint32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeInt64(t,e){this.writeInt32(t,Number(BigInt.asIntN(32,e))),this.writeInt32(t+4,Number(BigInt.asIntN(32,e>>BigInt(32))))}writeUint64(t,e){this.writeUint32(t,Number(BigInt.asUintN(32,e))),this.writeUint32(t+4,Number(BigInt.asUintN(32,e>>BigInt(32))))}writeFloat32(t,e){n.float32[0]=e,this.writeInt32(t,n.int32[0])}writeFloat64(t,e){n.float64[0]=e,this.writeInt32(t,n.int32[n.isLittleEndian?0:1]),this.writeInt32(t+4,n.int32[n.isLittleEndian?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+d.SIZEOF_INT+d.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let e=0;e<d.FILE_IDENTIFIER_LENGTH;e++)t+=String.fromCharCode(this.readInt8(this.position_+d.SIZEOF_INT+e));return t}__offset(t,e){let i=t-this.readInt32(t);return e<this.readInt16(i)?this.readInt16(i+e):0}__union(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t}__string(t,e){t+=this.readInt32(t);let i=this.readInt32(t);t+=d.SIZEOF_INT;let s=this.bytes_.subarray(t,t+i);return e===m.Encoding.UTF8_BYTES?s:this.text_decoder_.decode(s)}__union_with_string(t,e){return typeof t=="string"?this.__string(e):this.__union(t,e)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+d.SIZEOF_INT}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(t.length!=d.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+d.FILE_IDENTIFIER_LENGTH);for(let e=0;e<d.FILE_IDENTIFIER_LENGTH;e++)if(t.charCodeAt(e)!=this.readInt8(this.position()+d.SIZEOF_INT+e))return!1;return!0}createScalarList(t,e){let i=[];for(let s=0;s<e;++s){let a=t(s);a!==null&&i.push(a)}return i}createObjList(t,e){let i=[];for(let s=0;s<e;++s){let a=t(s);a!==null&&i.push(a.unpack())}return i}};E.ByteBuffer=w});var L=b(p=>{"use strict";Object.defineProperty(p,"__esModule",{value:!0});p.Builder=void 0;var B=T(),h=_(),N=class l{constructor(t){this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,this.text_encoder=new TextEncoder;let e;t?e=t:e=1024,this.bb=B.ByteBuffer.allocate(e),this.space=e}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(t,e){t>this.minalign&&(this.minalign=t);let i=~(this.bb.capacity()-this.space+e)+1&t-1;for(;this.space<i+t+e;){let s=this.bb.capacity();this.bb=l.growByteBuffer(this.bb),this.space+=this.bb.capacity()-s}this.pad(i)}pad(t){for(let e=0;e<t;e++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,e,i){(this.force_defaults||e!=i)&&(this.addInt8(e),this.slot(t))}addFieldInt16(t,e,i){(this.force_defaults||e!=i)&&(this.addInt16(e),this.slot(t))}addFieldInt32(t,e,i){(this.force_defaults||e!=i)&&(this.addInt32(e),this.slot(t))}addFieldInt64(t,e,i){(this.force_defaults||e!==i)&&(this.addInt64(e),this.slot(t))}addFieldFloat32(t,e,i){(this.force_defaults||e!=i)&&(this.addFloat32(e),this.slot(t))}addFieldFloat64(t,e,i){(this.force_defaults||e!=i)&&(this.addFloat64(e),this.slot(t))}addFieldOffset(t,e,i){(this.force_defaults||e!=i)&&(this.addOffset(e),this.slot(t))}addFieldStruct(t,e,i){e!=i&&(this.nested(e),this.slot(t))}nested(t){if(t!=this.offset())throw new TypeError("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new TypeError("FlatBuffers: object serialization must not be nested.")}slot(t){this.vtable!==null&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){let e=t.capacity();if(e&3221225472)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");let i=e<<1,s=B.ByteBuffer.allocate(i);return s.setPosition(i-e),s.bytes().set(t.bytes(),i-e),s}addOffset(t){this.prep(h.SIZEOF_INT,0),this.writeInt32(this.offset()-t+h.SIZEOF_INT)}startObject(t){this.notNested(),this.vtable==null&&(this.vtable=[]),this.vtable_in_use=t;for(let e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(this.vtable==null||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);let t=this.offset(),e=this.vtable_in_use-1;for(;e>=0&&this.vtable[e]==0;e--);let i=e+1;for(;e>=0;e--)this.addInt16(this.vtable[e]!=0?t-this.vtable[e]:0);let s=2;this.addInt16(t-this.object_start);let a=(i+s)*h.SIZEOF_SHORT;this.addInt16(a);let u=0,j=this.space;t:for(e=0;e<this.vtables.length;e++){let O=this.bb.capacity()-this.vtables[e];if(a==this.bb.readInt16(O)){for(let I=h.SIZEOF_SHORT;I<a;I+=h.SIZEOF_SHORT)if(this.bb.readInt16(j+I)!=this.bb.readInt16(O+I))continue t;u=this.vtables[e];break}}return u?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,u-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,e,i){let s=i?h.SIZE_PREFIX_LENGTH:0;if(e){let a=e;if(this.prep(this.minalign,h.SIZEOF_INT+h.FILE_IDENTIFIER_LENGTH+s),a.length!=h.FILE_IDENTIFIER_LENGTH)throw new TypeError("FlatBuffers: file identifier must be length "+h.FILE_IDENTIFIER_LENGTH);for(let u=h.FILE_IDENTIFIER_LENGTH-1;u>=0;u--)this.writeInt8(a.charCodeAt(u))}this.prep(this.minalign,h.SIZEOF_INT+s),this.addOffset(t),s&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,e){this.finish(t,e,!0)}requiredField(t,e){let i=this.bb.capacity()-t,s=i-this.bb.readInt32(i);if(!(e<this.bb.readInt16(s)&&this.bb.readInt16(s+e)!=0))throw new TypeError("FlatBuffers: field "+e+" must be set")}startVector(t,e,i){this.notNested(),this.vector_num_elems=e,this.prep(h.SIZEOF_INT,t*e),this.prep(i,t*e)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);let e=this.createString(t);return this.string_maps.set(t,e),e}createString(t){if(t==null)return 0;let e;return t instanceof Uint8Array?e=t:e=this.text_encoder.encode(t),this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length),this.bb.bytes().set(e,this.space),this.endVector()}createByteVector(t){return t==null?0:(this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length),this.bb.bytes().set(t,this.space),this.endVector())}createObjectOffset(t){return t===null?0:typeof t=="string"?this.createString(t):t.pack(this)}createObjectOffsetList(t){let e=[];for(let i=0;i<t.length;++i){let s=t[i];if(s!==null)e.push(this.createObjectOffset(s));else throw new TypeError("FlatBuffers: Argument for createObjectOffsetList cannot contain null.")}return e}createStructOffsetList(t,e){return e(this,t.length),this.createObjectOffsetList(t.slice().reverse()),this.endVector()}};p.Builder=N});var D=b(r=>{Object.defineProperty(r,"__esModule",{value:!0});r.ByteBuffer=r.Builder=r.Encoding=r.isLittleEndian=r.float64=r.float32=r.int32=r.SIZE_PREFIX_LENGTH=r.FILE_IDENTIFIER_LENGTH=r.SIZEOF_INT=r.SIZEOF_SHORT=void 0;var v=_();Object.defineProperty(r,"SIZEOF_SHORT",{enumerable:!0,get:function(){return v.SIZEOF_SHORT}});var R=_();Object.defineProperty(r,"SIZEOF_INT",{enumerable:!0,get:function(){return R.SIZEOF_INT}});var P=_();Object.defineProperty(r,"FILE_IDENTIFIER_LENGTH",{enumerable:!0,get:function(){return P.FILE_IDENTIFIER_LENGTH}});var U=_();Object.defineProperty(r,"SIZE_PREFIX_LENGTH",{enumerable:!0,get:function(){return U.SIZE_PREFIX_LENGTH}});var F=y();Object.defineProperty(r,"int32",{enumerable:!0,get:function(){return F.int32}});Object.defineProperty(r,"float32",{enumerable:!0,get:function(){return F.float32}});Object.defineProperty(r,"float64",{enumerable:!0,get:function(){return F.float64}});Object.defineProperty(r,"isLittleEndian",{enumerable:!0,get:function(){return F.isLittleEndian}});var Z=g();Object.defineProperty(r,"Encoding",{enumerable:!0,get:function(){return Z.Encoding}});var H=L();Object.defineProperty(r,"Builder",{enumerable:!0,get:function(){return H.Builder}});var G=T();Object.defineProperty(r,"ByteBuffer",{enumerable:!0,get:function(){return G.ByteBuffer}})});return D();})();
