{"name": "is-bun-module", "author": "SunsetTechuila <<EMAIL>>", "description": "Is this specifier a Bun core module or supported Node one?", "version": "1.3.0", "license": "MIT", "files": ["dist/**/*"], "main": "./dist/cjs/index.js", "module": "./dist/esm/index.mjs", "exports": {".": {"import": {"types": "./dist/esm/index.d.mts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}, "homepage": "https://github.com/SunsetTechuila/is-bun-module", "repository": "github:SunsetTech<PERSON>a/is-bun-module", "bugs": {"url": "https://github.com/SunsetTechuila/is-bun-module/issues"}, "keywords": ["core", "modules", "module", "node", "dependencies", "bun"], "scripts": {"build": "bun --bun tsup", "check-all": "bun --bun concurrently --kill-others=failure 'bun run test' 'bun lint' 'bun type-check' 'bun format:check'", "test": "bun test", "format": "bun format:base --write", "format:check": "bun format:base --check", "format:base": "bun --bun prettier . --cache", "lint": "bun --bun eslint . --cache", "type-check": "bun --bun tsc", "publish": "bun --bun semantic-release"}, "dependencies": {"semver": "^7.6.3"}, "devDependencies": {"@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@eslint/js": "^9.15.0", "@semantic-release/exec": "^6.0.3", "@types/bun": "^1.1.14", "@types/eslint__js": "^8.42.3", "@types/semver": "^7.5.8", "concurrently": "^9.1.0", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "husky": "^9.1.7", "prettier": "^3.4.0", "semantic-release": "^24.2.0", "tsup": "^8.3.5", "typescript": "^5.7.2", "typescript-eslint": "^8.16.0"}}