"use strict";var tl=Object.create;var Et=Object.defineProperty;var rl=Object.getOwnPropertyDescriptor;var nl=Object.getOwnPropertyNames;var il=Object.getPrototypeOf,ol=Object.prototype.hasOwnProperty;var Oi=e=>{throw TypeError(e)};var sl=(e,t,r)=>t in e?Et(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var se=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),bt=(e,t)=>{for(var r in t)Et(e,r,{get:t[r],enumerable:!0})},Ii=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of nl(t))!ol.call(e,i)&&i!==r&&Et(e,i,{get:()=>t[i],enumerable:!(n=rl(t,i))||n.enumerable});return e};var H=(e,t,r)=>(r=e!=null?tl(il(e)):{},Ii(t||!e||!e.__esModule?Et(r,"default",{value:e,enumerable:!0}):r,e)),al=e=>Ii(Et({},"__esModule",{value:!0}),e);var d=(e,t,r)=>sl(e,typeof t!="symbol"?t+"":t,r),Di=(e,t,r)=>t.has(e)||Oi("Cannot "+r);var wn=(e,t,r)=>(Di(e,t,"read from private field"),r?r.call(e):t.get(e)),_i=(e,t,r)=>t.has(e)?Oi("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Ni=(e,t,r,n)=>(Di(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);var Wi=se((em,Dl)=>{Dl.exports={name:"@prisma/internals",version:"6.5.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@antfu/ni":"0.21.12","@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.4.7",esbuild:"0.24.2","escape-string-regexp":"4.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"4.2.3","strip-ansi":"6.0.1","strip-indent":"3.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"2.1.1",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.5.0-73.173f8d54f8d52e692c7e27e72a88314ec7aeff60","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var Hi=se((rm,Nl)=>{Nl.exports={name:"dotenv",version:"16.4.7",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}});var Zi=se((nm,ve)=>{"use strict";var Sn=require("fs"),kn=require("path"),Ml=require("os"),Fl=require("crypto"),Ll=Hi(),On=Ll.version,$l=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function ql(e){let t={},r=e.toString();r=r.replace(/\r\n?/mg,`
`);let n;for(;(n=$l.exec(r))!=null;){let i=n[1],o=n[2]||"";o=o.trim();let s=o[0];o=o.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),s==='"'&&(o=o.replace(/\\n/g,`
`),o=o.replace(/\\r/g,"\r")),t[i]=o}return t}function Vl(e){let t=Yi(e),r=$.configDotenv({path:t});if(!r.parsed){let s=new Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw s.code="MISSING_DATA",s}let n=zi(e).split(","),i=n.length,o;for(let s=0;s<i;s++)try{let a=n[s].trim(),l=Bl(r,a);o=$.decrypt(l.ciphertext,l.key);break}catch(a){if(s+1>=i)throw a}return $.parse(o)}function jl(e){console.log(`[dotenv@${On}][INFO] ${e}`)}function Ul(e){console.log(`[dotenv@${On}][WARN] ${e}`)}function mr(e){console.log(`[dotenv@${On}][DEBUG] ${e}`)}function zi(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function Bl(e,t){let r;try{r=new URL(t)}catch(a){if(a.code==="ERR_INVALID_URL"){let l=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw l.code="INVALID_DOTENV_KEY",l}throw a}let n=r.password;if(!n){let a=new Error("INVALID_DOTENV_KEY: Missing key part");throw a.code="INVALID_DOTENV_KEY",a}let i=r.searchParams.get("environment");if(!i){let a=new Error("INVALID_DOTENV_KEY: Missing environment part");throw a.code="INVALID_DOTENV_KEY",a}let o=`DOTENV_VAULT_${i.toUpperCase()}`,s=e.parsed[o];if(!s){let a=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${o} in your .env.vault file.`);throw a.code="NOT_FOUND_DOTENV_ENVIRONMENT",a}return{ciphertext:s,key:n}}function Yi(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)Sn.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=kn.resolve(process.cwd(),".env.vault");return Sn.existsSync(t)?t:null}function Ki(e){return e[0]==="~"?kn.join(Ml.homedir(),e.slice(1)):e}function Ql(e){jl("Loading env from encrypted .env.vault");let t=$._parseVault(e),r=process.env;return e&&e.processEnv!=null&&(r=e.processEnv),$.populate(r,t,e),{parsed:t}}function Gl(e){let t=kn.resolve(process.cwd(),".env"),r="utf8",n=!!(e&&e.debug);e&&e.encoding?r=e.encoding:n&&mr("No encoding is specified. UTF-8 is used by default");let i=[t];if(e&&e.path)if(!Array.isArray(e.path))i=[Ki(e.path)];else{i=[];for(let l of e.path)i.push(Ki(l))}let o,s={};for(let l of i)try{let u=$.parse(Sn.readFileSync(l,{encoding:r}));$.populate(s,u,e)}catch(u){n&&mr(`Failed to load ${l} ${u.message}`),o=u}let a=process.env;return e&&e.processEnv!=null&&(a=e.processEnv),$.populate(a,s,e),o?{parsed:s,error:o}:{parsed:s}}function Jl(e){if(zi(e).length===0)return $.configDotenv(e);let t=Yi(e);return t?$._configVault(e):(Ul(`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`),$.configDotenv(e))}function Wl(e,t){let r=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),i=n.subarray(0,12),o=n.subarray(-16);n=n.subarray(12,-16);try{let s=Fl.createDecipheriv("aes-256-gcm",r,i);return s.setAuthTag(o),`${s.update(n)}${s.final()}`}catch(s){let a=s instanceof RangeError,l=s.message==="Invalid key length",u=s.message==="Unsupported state or unable to authenticate data";if(a||l){let c=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw c.code="INVALID_DOTENV_KEY",c}else if(u){let c=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw c.code="DECRYPTION_FAILED",c}else throw s}}function Hl(e,t,r={}){let n=!!(r&&r.debug),i=!!(r&&r.override);if(typeof t!="object"){let o=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw o.code="OBJECT_REQUIRED",o}for(let o of Object.keys(t))Object.prototype.hasOwnProperty.call(e,o)?(i===!0&&(e[o]=t[o]),n&&mr(i===!0?`"${o}" is already defined and WAS overwritten`:`"${o}" is already defined and was NOT overwritten`)):e[o]=t[o]}var $={configDotenv:Gl,_configVault:Ql,_parseVault:Vl,config:Jl,decrypt:Wl,parse:ql,populate:Hl};ve.exports.configDotenv=$.configDotenv;ve.exports._configVault=$._configVault;ve.exports._parseVault=$._parseVault;ve.exports.config=$.config;ve.exports.decrypt=$.decrypt;ve.exports.parse=$.parse;ve.exports.populate=$.populate;ve.exports=$});var io=se((cm,no)=>{"use strict";no.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((r,n)=>Math.min(r,n.length),1/0):0}});var so=se((pm,oo)=>{"use strict";var Zl=io();oo.exports=e=>{let t=Zl(e);if(t===0)return e;let r=new RegExp(`^[ \\t]{${t}}`,"gm");return e.replace(r,"")}});var uo=se((wm,lo)=>{"use strict";lo.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var mo=se((bm,po)=>{"use strict";po.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var Fn=se((xm,fo)=>{"use strict";var nu=mo();fo.exports=e=>typeof e=="string"?e.replace(nu(),""):e});var go=se((Pm,hr)=>{"use strict";hr.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};hr.exports.default=hr.exports});var Gn=se((qf,$o)=>{"use strict";$o.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,l,u,c,p,m,g,h,y,S,P,C,T,O=[];for(l=0;l<i;l++)O.push(l+1),O.push(t.charCodeAt(s+l));for(var R=O.length-1;a<o-3;)for(S=r.charCodeAt(s+(u=a)),P=r.charCodeAt(s+(c=a+1)),C=r.charCodeAt(s+(p=a+2)),T=r.charCodeAt(s+(m=a+3)),g=a+=4,l=0;l<R;l+=2)h=O[l],y=O[l+1],u=e(h,u,c,S,y),c=e(u,c,p,P,y),p=e(c,p,m,C,y),g=e(p,m,g,T,y),O[l]=g,m=p,p=c,c=u,u=h;for(;a<o;)for(S=r.charCodeAt(s+(u=a)),g=++a,l=0;l<R;l+=2)h=O[l],O[l]=g=e(h,u,g,S,O[l+1]),u=h;return g}}()});var fs=se((Uh,Kc)=>{Kc.exports={name:"@prisma/engines-version",version:"6.5.0-73.173f8d54f8d52e692c7e27e72a88314ec7aeff60",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"173f8d54f8d52e692c7e27e72a88314ec7aeff60"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var Dd={};bt(Dd,{Debug:()=>An,Decimal:()=>fe,Extensions:()=>En,MetricsClient:()=>at,PrismaClientInitializationError:()=>k,PrismaClientKnownRequestError:()=>Q,PrismaClientRustPanicError:()=>le,PrismaClientUnknownRequestError:()=>G,PrismaClientValidationError:()=>J,Public:()=>bn,Sql:()=>ee,createParam:()=>os,defineDmmfProperty:()=>ds,deserializeJsonResponse:()=>ze,deserializeRawResult:()=>gn,dmmfToRuntimeDataModel:()=>ps,empty:()=>hs,getPrismaClient:()=>Za,getRuntime:()=>Yr,join:()=>gs,makeStrictEnum:()=>Xa,makeTypedQueryFactory:()=>ms,objectEnumValues:()=>Mr,raw:()=>ei,serializeJsonQuery:()=>Ur,skip:()=>jr,sqltag:()=>ti,warnEnvConflicts:()=>el,warnOnce:()=>Ot});module.exports=al(Dd);var En={};bt(En,{defineExtension:()=>Mi,getExtensionContext:()=>Fi});function Mi(e){return typeof e=="function"?e:t=>t.$extends(e)}function Fi(e){return e}var bn={};bt(bn,{validator:()=>Li});function Li(...e){return t=>t}function xn(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}function pr(e){return{ok:!0,value:e,map(t){return pr(t(e))},flatMap(t){return t(e)}}}function _e(e){return{ok:!1,error:e,map(){return _e(e)},flatMap(){return _e(e)}}}var vn=class{constructor(){d(this,"registeredErrors",[])}consumeError(t){return this.registeredErrors[t]}registerNewError(t){let r=0;for(;this.registeredErrors[r]!==void 0;)r++;return this.registeredErrors[r]={error:t},r}},Pn=e=>{let t=new vn,r=ie(t,e.transactionContext.bind(e)),n={adapterName:e.adapterName,errorRegistry:t,queryRaw:ie(t,e.queryRaw.bind(e)),executeRaw:ie(t,e.executeRaw.bind(e)),executeScript:ie(t,e.executeScript.bind(e)),dispose:ie(t,e.dispose.bind(e)),provider:e.provider,transactionContext:async(...i)=>(await r(...i)).map(s=>ll(t,s))};return e.getConnectionInfo&&(n.getConnectionInfo=cl(t,e.getConnectionInfo.bind(e))),n},ll=(e,t)=>{let r=ie(e,t.startTransaction.bind(t));return{adapterName:t.adapterName,provider:t.provider,queryRaw:ie(e,t.queryRaw.bind(t)),executeRaw:ie(e,t.executeRaw.bind(t)),startTransaction:async(...n)=>(await r(...n)).map(o=>ul(e,o))}},ul=(e,t)=>({adapterName:t.adapterName,provider:t.provider,options:t.options,queryRaw:ie(e,t.queryRaw.bind(t)),executeRaw:ie(e,t.executeRaw.bind(t)),commit:ie(e,t.commit.bind(t)),rollback:ie(e,t.rollback.bind(t))});function ie(e,t){return async(...r)=>{try{return pr(await t(...r))}catch(n){if(xn(n))return _e(n.cause);let i=e.registerNewError(n);return _e({kind:"GenericJs",id:i})}}}function cl(e,t){return(...r)=>{try{return pr(t(...r))}catch(n){if(xn(n))return _e(n.cause);let i=e.registerNewError(n);return _e({kind:"GenericJs",id:i})}}}var dr={};bt(dr,{$:()=>Ui,bgBlack:()=>bl,bgBlue:()=>Tl,bgCyan:()=>Al,bgGreen:()=>vl,bgMagenta:()=>Cl,bgRed:()=>xl,bgWhite:()=>Rl,bgYellow:()=>Pl,black:()=>hl,blue:()=>Fe,bold:()=>K,cyan:()=>xe,dim:()=>Ne,gray:()=>vt,green:()=>xt,grey:()=>El,hidden:()=>fl,inverse:()=>ml,italic:()=>dl,magenta:()=>yl,red:()=>be,reset:()=>pl,strikethrough:()=>gl,underline:()=>Z,white:()=>wl,yellow:()=>Me});var Tn,$i,qi,Vi,ji=!0;typeof process<"u"&&({FORCE_COLOR:Tn,NODE_DISABLE_COLORS:$i,NO_COLOR:qi,TERM:Vi}=process.env||{},ji=process.stdout&&process.stdout.isTTY);var Ui={enabled:!$i&&qi==null&&Vi!=="dumb"&&(Tn!=null&&Tn!=="0"||ji)};function N(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!Ui.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var pl=N(0,0),K=N(1,22),Ne=N(2,22),dl=N(3,23),Z=N(4,24),ml=N(7,27),fl=N(8,28),gl=N(9,29),hl=N(30,39),be=N(31,39),xt=N(32,39),Me=N(33,39),Fe=N(34,39),yl=N(35,39),xe=N(36,39),wl=N(37,39),vt=N(90,39),El=N(90,39),bl=N(40,49),xl=N(41,49),vl=N(42,49),Pl=N(43,49),Tl=N(44,49),Cl=N(45,49),Al=N(46,49),Rl=N(47,49);var Sl=100,Bi=["green","yellow","blue","magenta","cyan","red"],Pt=[],Qi=Date.now(),kl=0,Cn=typeof process<"u"?process.env:{};globalThis.DEBUG??=Cn.DEBUG??"";globalThis.DEBUG_COLORS??=Cn.DEBUG_COLORS?Cn.DEBUG_COLORS==="true":!0;var Tt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function Ol(e){let t={color:Bi[kl++%Bi.length],enabled:Tt.enabled(e),namespace:e,log:Tt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&Pt.push([o,...n]),Pt.length>Sl&&Pt.shift(),Tt.enabled(o)||i){let l=n.map(c=>typeof c=="string"?c:Il(c)),u=`+${Date.now()-Qi}ms`;Qi=Date.now(),globalThis.DEBUG_COLORS?a(dr[s](K(o)),...l,dr[s](u)):a(o,...l,u)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var An=new Proxy(Ol,{get:(e,t)=>Tt[t],set:(e,t,r)=>Tt[t]=r});function Il(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function Gi(e=7500){let t=Pt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function Ji(){Pt.length=0}var F=An;var _l=Wi(),Rn=_l.version;var Dn=H(Zi()),fr=H(require("fs"));var Ge=H(require("path"));function Xi(e){let t=e.ignoreProcessEnv?{}:process.env,r=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(o,s){let a=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(s);if(!a)return o;let l=a[1],u,c;if(l==="\\")c=a[0],u=c.replace("\\$","$");else{let p=a[2];c=a[0].substring(l.length),u=Object.hasOwnProperty.call(t,p)?t[p]:e.parsed[p]||"",u=r(u)}return o.replace(c,u)},n)??n;for(let n in e.parsed){let i=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=r(i)}for(let n in e.parsed)t[n]=e.parsed[n];return e}var In=F("prisma:tryLoadEnv");function Ct({rootEnvPath:e,schemaEnvPath:t},r={conflictCheck:"none"}){let n=eo(e);r.conflictCheck!=="none"&&Kl(n,t,r.conflictCheck);let i=null;return to(n?.path,t)||(i=eo(t)),!n&&!i&&In("No Environment variables loaded"),i?.dotenvResult.error?console.error(be(K("Schema Env Error: "))+i.dotenvResult.error):{message:[n?.message,i?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...i?.dotenvResult?.parsed}}}function Kl(e,t,r){let n=e?.dotenvResult.parsed,i=!to(e?.path,t);if(n&&t&&i&&fr.default.existsSync(t)){let o=Dn.default.parse(fr.default.readFileSync(t)),s=[];for(let a in o)n[a]===o[a]&&s.push(a);if(s.length>0){let a=Ge.default.relative(process.cwd(),e.path),l=Ge.default.relative(process.cwd(),t);if(r==="error"){let u=`There is a conflict between env var${s.length>1?"s":""} in ${Z(a)} and ${Z(l)}
Conflicting env vars:
${s.map(c=>`  ${K(c)}`).join(`
`)}

We suggest to move the contents of ${Z(l)} to ${Z(a)} to consolidate your env vars.
`;throw new Error(u)}else if(r==="warn"){let u=`Conflict for env var${s.length>1?"s":""} ${s.map(c=>K(c)).join(", ")} in ${Z(a)} and ${Z(l)}
Env vars from ${Z(l)} overwrite the ones from ${Z(a)}
      `;console.warn(`${Me("warn(prisma)")} ${u}`)}}}}function eo(e){if(zl(e)){In(`Environment variables loaded from ${e}`);let t=Dn.default.config({path:e,debug:process.env.DOTENV_CONFIG_DEBUG?!0:void 0});return{dotenvResult:Xi(t),message:Ne(`Environment variables loaded from ${Ge.default.relative(process.cwd(),e)}`),path:e}}else In(`Environment variables not found at ${e}`);return null}function to(e,t){return e&&t&&Ge.default.resolve(e)===Ge.default.resolve(t)}function zl(e){return!!(e&&fr.default.existsSync(e))}var ro="library";function Je(e){let t=Yl();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":ro)}function Yl(){let e=process.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}var ao="prisma+postgres",gr=`${ao}:`;function _n(e){return e?.startsWith(`${gr}//`)??!1}var At;(t=>{let e;(R=>(R.findUnique="findUnique",R.findUniqueOrThrow="findUniqueOrThrow",R.findFirst="findFirst",R.findFirstOrThrow="findFirstOrThrow",R.findMany="findMany",R.create="create",R.createMany="createMany",R.createManyAndReturn="createManyAndReturn",R.update="update",R.updateMany="updateMany",R.updateManyAndReturn="updateManyAndReturn",R.upsert="upsert",R.delete="delete",R.deleteMany="deleteMany",R.groupBy="groupBy",R.count="count",R.aggregate="aggregate",R.findRaw="findRaw",R.aggregateRaw="aggregateRaw"))(e=t.ModelAction||={})})(At||={});var Rt=H(require("path"));function Nn(e){return Rt.default.sep===Rt.default.posix.sep?e:e.split(Rt.default.sep).join(Rt.default.posix.sep)}var kt={};bt(kt,{error:()=>tu,info:()=>eu,log:()=>Xl,query:()=>ru,should:()=>co,tags:()=>St,warn:()=>Mn});var St={error:be("prisma:error"),warn:Me("prisma:warn"),info:xe("prisma:info"),query:Fe("prisma:query")},co={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function Xl(...e){console.log(...e)}function Mn(e,...t){co.warn()&&console.warn(`${St.warn} ${e}`,...t)}function eu(e,...t){console.info(`${St.info} ${e}`,...t)}function tu(e,...t){console.error(`${St.error} ${e}`,...t)}function ru(e,...t){console.log(`${St.query} ${e}`,...t)}function ae(e,t){throw new Error(t)}function Ln(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var $n=(e,t)=>e.reduce((r,n)=>(r[t(n)]=n,r),{});function We(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function qn(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}function x(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var ho=new Set,Ot=(e,t,...r)=>{ho.has(e)||(ho.add(e),Mn(t,...r))};var k=class e extends Error{constructor(r,n,i){super(r);d(this,"clientVersion");d(this,"errorCode");d(this,"retryable");this.name="PrismaClientInitializationError",this.clientVersion=n,this.errorCode=i,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};x(k,"PrismaClientInitializationError");var Q=class extends Error{constructor(r,{code:n,clientVersion:i,meta:o,batchRequestIdx:s}){super(r);d(this,"code");d(this,"meta");d(this,"clientVersion");d(this,"batchRequestIdx");this.name="PrismaClientKnownRequestError",this.code=n,this.clientVersion=i,this.meta=o,Object.defineProperty(this,"batchRequestIdx",{value:s,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};x(Q,"PrismaClientKnownRequestError");var le=class extends Error{constructor(r,n){super(r);d(this,"clientVersion");this.name="PrismaClientRustPanicError",this.clientVersion=n}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};x(le,"PrismaClientRustPanicError");var G=class extends Error{constructor(r,{clientVersion:n,batchRequestIdx:i}){super(r);d(this,"clientVersion");d(this,"batchRequestIdx");this.name="PrismaClientUnknownRequestError",this.clientVersion=n,Object.defineProperty(this,"batchRequestIdx",{value:i,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};x(G,"PrismaClientUnknownRequestError");var J=class extends Error{constructor(r,{clientVersion:n}){super(r);d(this,"name","PrismaClientValidationError");d(this,"clientVersion");this.clientVersion=n}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};x(J,"PrismaClientValidationError");var He=9e15,ke=1e9,Vn="0123456789abcdef",Er="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",br="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",jn={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-He,maxE:He,crypto:!1},xo,Pe,b=!0,vr="[DecimalError] ",Se=vr+"Invalid argument: ",vo=vr+"Precision limit exceeded",Po=vr+"crypto unavailable",To="[object Decimal]",W=Math.floor,q=Math.pow,iu=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,ou=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,su=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,Co=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ue=1e7,E=7,au=9007199254740991,lu=Er.length-1,Un=br.length-1,f={toStringTag:To};f.absoluteValue=f.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),w(e)};f.ceil=function(){return w(new this.constructor(this),this.e+1,2)};f.clampedTo=f.clamp=function(e,t){var r,n=this,i=n.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(Se+t);return r=n.cmp(e),r<0?e:n.cmp(t)>0?t:new i(n)};f.comparedTo=f.cmp=function(e){var t,r,n,i,o=this,s=o.d,a=(e=new o.constructor(e)).d,l=o.s,u=e.s;if(!s||!a)return!l||!u?NaN:l!==u?l:s===a?0:!s^l<0?1:-1;if(!s[0]||!a[0])return s[0]?l:a[0]?-u:0;if(l!==u)return l;if(o.e!==e.e)return o.e>e.e^l<0?1:-1;for(n=s.length,i=a.length,t=0,r=n<i?n:i;t<r;++t)if(s[t]!==a[t])return s[t]>a[t]^l<0?1:-1;return n===i?0:n>i^l<0?1:-1};f.cosine=f.cos=function(){var e,t,r=this,n=r.constructor;return r.d?r.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=uu(n,Oo(n,r)),n.precision=e,n.rounding=t,w(Pe==2||Pe==3?r.neg():r,e,t,!0)):new n(1):new n(NaN)};f.cubeRoot=f.cbrt=function(){var e,t,r,n,i,o,s,a,l,u,c=this,p=c.constructor;if(!c.isFinite()||c.isZero())return new p(c);for(b=!1,o=c.s*q(c.s*c,1/3),!o||Math.abs(o)==1/0?(r=j(c.d),e=c.e,(o=(e-r.length+1)%3)&&(r+=o==1||o==-2?"0":"00"),o=q(r,1/3),e=W((e+1)/3)-(e%3==(e<0?-1:2)),o==1/0?r="5e"+e:(r=o.toExponential(),r=r.slice(0,r.indexOf("e")+1)+e),n=new p(r),n.s=c.s):n=new p(o.toString()),s=(e=p.precision)+3;;)if(a=n,l=a.times(a).times(a),u=l.plus(c),n=_(u.plus(c).times(a),u.plus(l),s+2,1),j(a.d).slice(0,s)===(r=j(n.d)).slice(0,s))if(r=r.slice(s-3,s+1),r=="9999"||!i&&r=="4999"){if(!i&&(w(a,e+1,0),a.times(a).times(a).eq(c))){n=a;break}s+=4,i=1}else{(!+r||!+r.slice(1)&&r.charAt(0)=="5")&&(w(n,e+1,1),t=!n.times(n).times(n).eq(c));break}return b=!0,w(n,e,p.rounding,t)};f.decimalPlaces=f.dp=function(){var e,t=this.d,r=NaN;if(t){if(e=t.length-1,r=(e-W(this.e/E))*E,e=t[e],e)for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r};f.dividedBy=f.div=function(e){return _(this,new this.constructor(e))};f.dividedToIntegerBy=f.divToInt=function(e){var t=this,r=t.constructor;return w(_(t,new r(e),0,1,1),r.precision,r.rounding)};f.equals=f.eq=function(e){return this.cmp(e)===0};f.floor=function(){return w(new this.constructor(this),this.e+1,3)};f.greaterThan=f.gt=function(e){return this.cmp(e)>0};f.greaterThanOrEqualTo=f.gte=function(e){var t=this.cmp(e);return t==1||t===0};f.hyperbolicCosine=f.cosh=function(){var e,t,r,n,i,o=this,s=o.constructor,a=new s(1);if(!o.isFinite())return new s(o.s?1/0:NaN);if(o.isZero())return a;r=s.precision,n=s.rounding,s.precision=r+Math.max(o.e,o.sd())+4,s.rounding=1,i=o.d.length,i<32?(e=Math.ceil(i/3),t=(1/Tr(4,e)).toString()):(e=16,t="2.3283064365386962890625e-10"),o=Ke(s,1,o.times(t),new s(1),!0);for(var l,u=e,c=new s(8);u--;)l=o.times(o),o=a.minus(l.times(c.minus(l.times(c))));return w(o,s.precision=r,s.rounding=n,!0)};f.hyperbolicSine=f.sinh=function(){var e,t,r,n,i=this,o=i.constructor;if(!i.isFinite()||i.isZero())return new o(i);if(t=o.precision,r=o.rounding,o.precision=t+Math.max(i.e,i.sd())+4,o.rounding=1,n=i.d.length,n<3)i=Ke(o,2,i,i,!0);else{e=1.4*Math.sqrt(n),e=e>16?16:e|0,i=i.times(1/Tr(5,e)),i=Ke(o,2,i,i,!0);for(var s,a=new o(5),l=new o(16),u=new o(20);e--;)s=i.times(i),i=i.times(a.plus(s.times(l.times(s).plus(u))))}return o.precision=t,o.rounding=r,w(i,t,r,!0)};f.hyperbolicTangent=f.tanh=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+7,n.rounding=1,_(r.sinh(),r.cosh(),n.precision=e,n.rounding=t)):new n(r.s)};f.inverseCosine=f.acos=function(){var e=this,t=e.constructor,r=e.abs().cmp(1),n=t.precision,i=t.rounding;return r!==-1?r===0?e.isNeg()?de(t,n,i):new t(0):new t(NaN):e.isZero()?de(t,n+4,i).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=i,e.times(2))};f.inverseHyperbolicCosine=f.acosh=function(){var e,t,r=this,n=r.constructor;return r.lte(1)?new n(r.eq(1)?0:NaN):r.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(r.e),r.sd())+4,n.rounding=1,b=!1,r=r.times(r).minus(1).sqrt().plus(r),b=!0,n.precision=e,n.rounding=t,r.ln()):new n(r)};f.inverseHyperbolicSine=f.asinh=function(){var e,t,r=this,n=r.constructor;return!r.isFinite()||r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,n.rounding=1,b=!1,r=r.times(r).plus(1).sqrt().plus(r),b=!0,n.precision=e,n.rounding=t,r.ln())};f.inverseHyperbolicTangent=f.atanh=function(){var e,t,r,n,i=this,o=i.constructor;return i.isFinite()?i.e>=0?new o(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=o.precision,t=o.rounding,n=i.sd(),Math.max(n,e)<2*-i.e-1?w(new o(i),e,t,!0):(o.precision=r=n-i.e,i=_(i.plus(1),new o(1).minus(i),r+e,1),o.precision=e+4,o.rounding=1,i=i.ln(),o.precision=e,o.rounding=t,i.times(.5))):new o(NaN)};f.inverseSine=f.asin=function(){var e,t,r,n,i=this,o=i.constructor;return i.isZero()?new o(i):(t=i.abs().cmp(1),r=o.precision,n=o.rounding,t!==-1?t===0?(e=de(o,r+4,n).times(.5),e.s=i.s,e):new o(NaN):(o.precision=r+6,o.rounding=1,i=i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(),o.precision=r,o.rounding=n,i.times(2)))};f.inverseTangent=f.atan=function(){var e,t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,m=c.rounding;if(u.isFinite()){if(u.isZero())return new c(u);if(u.abs().eq(1)&&p+4<=Un)return s=de(c,p+4,m).times(.25),s.s=u.s,s}else{if(!u.s)return new c(NaN);if(p+4<=Un)return s=de(c,p+4,m).times(.5),s.s=u.s,s}for(c.precision=a=p+10,c.rounding=1,r=Math.min(28,a/E+2|0),e=r;e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(b=!1,t=Math.ceil(a/E),n=1,l=u.times(u),s=new c(u),i=u;e!==-1;)if(i=i.times(l),o=s.minus(i.div(n+=2)),i=i.times(l),s=o.plus(i.div(n+=2)),s.d[t]!==void 0)for(e=t;s.d[e]===o.d[e]&&e--;);return r&&(s=s.times(2<<r-1)),b=!0,w(s,c.precision=p,c.rounding=m,!0)};f.isFinite=function(){return!!this.d};f.isInteger=f.isInt=function(){return!!this.d&&W(this.e/E)>this.d.length-2};f.isNaN=function(){return!this.s};f.isNegative=f.isNeg=function(){return this.s<0};f.isPositive=f.isPos=function(){return this.s>0};f.isZero=function(){return!!this.d&&this.d[0]===0};f.lessThan=f.lt=function(e){return this.cmp(e)<0};f.lessThanOrEqualTo=f.lte=function(e){return this.cmp(e)<1};f.logarithm=f.log=function(e){var t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,m=c.rounding,g=5;if(e==null)e=new c(10),t=!0;else{if(e=new c(e),r=e.d,e.s<0||!r||!r[0]||e.eq(1))return new c(NaN);t=e.eq(10)}if(r=u.d,u.s<0||!r||!r[0]||u.eq(1))return new c(r&&!r[0]?-1/0:u.s!=1?NaN:r?0:1/0);if(t)if(r.length>1)o=!0;else{for(i=r[0];i%10===0;)i/=10;o=i!==1}if(b=!1,a=p+g,s=Re(u,a),n=t?xr(c,a+10):Re(e,a),l=_(s,n,a,1),It(l.d,i=p,m))do if(a+=10,s=Re(u,a),n=t?xr(c,a+10):Re(e,a),l=_(s,n,a,1),!o){+j(l.d).slice(i+1,i+15)+1==1e14&&(l=w(l,p+1,0));break}while(It(l.d,i+=10,m));return b=!0,w(l,p,m)};f.minus=f.sub=function(e){var t,r,n,i,o,s,a,l,u,c,p,m,g=this,h=g.constructor;if(e=new h(e),!g.d||!e.d)return!g.s||!e.s?e=new h(NaN):g.d?e.s=-e.s:e=new h(e.d||g.s!==e.s?g:NaN),e;if(g.s!=e.s)return e.s=-e.s,g.plus(e);if(u=g.d,m=e.d,a=h.precision,l=h.rounding,!u[0]||!m[0]){if(m[0])e.s=-e.s;else if(u[0])e=new h(g);else return new h(l===3?-0:0);return b?w(e,a,l):e}if(r=W(e.e/E),c=W(g.e/E),u=u.slice(),o=c-r,o){for(p=o<0,p?(t=u,o=-o,s=m.length):(t=m,r=c,s=u.length),n=Math.max(Math.ceil(a/E),s)+2,o>n&&(o=n,t.length=1),t.reverse(),n=o;n--;)t.push(0);t.reverse()}else{for(n=u.length,s=m.length,p=n<s,p&&(s=n),n=0;n<s;n++)if(u[n]!=m[n]){p=u[n]<m[n];break}o=0}for(p&&(t=u,u=m,m=t,e.s=-e.s),s=u.length,n=m.length-s;n>0;--n)u[s++]=0;for(n=m.length;n>o;){if(u[--n]<m[n]){for(i=n;i&&u[--i]===0;)u[i]=ue-1;--u[i],u[n]+=ue}u[n]-=m[n]}for(;u[--s]===0;)u.pop();for(;u[0]===0;u.shift())--r;return u[0]?(e.d=u,e.e=Pr(u,r),b?w(e,a,l):e):new h(l===3?-0:0)};f.modulo=f.mod=function(e){var t,r=this,n=r.constructor;return e=new n(e),!r.d||!e.s||e.d&&!e.d[0]?new n(NaN):!e.d||r.d&&!r.d[0]?w(new n(r),n.precision,n.rounding):(b=!1,n.modulo==9?(t=_(r,e.abs(),0,3,1),t.s*=e.s):t=_(r,e,0,n.modulo,1),t=t.times(e),b=!0,r.minus(t))};f.naturalExponential=f.exp=function(){return Bn(this)};f.naturalLogarithm=f.ln=function(){return Re(this)};f.negated=f.neg=function(){var e=new this.constructor(this);return e.s=-e.s,w(e)};f.plus=f.add=function(e){var t,r,n,i,o,s,a,l,u,c,p=this,m=p.constructor;if(e=new m(e),!p.d||!e.d)return!p.s||!e.s?e=new m(NaN):p.d||(e=new m(e.d||p.s===e.s?p:NaN)),e;if(p.s!=e.s)return e.s=-e.s,p.minus(e);if(u=p.d,c=e.d,a=m.precision,l=m.rounding,!u[0]||!c[0])return c[0]||(e=new m(p)),b?w(e,a,l):e;if(o=W(p.e/E),n=W(e.e/E),u=u.slice(),i=o-n,i){for(i<0?(r=u,i=-i,s=c.length):(r=c,n=o,s=u.length),o=Math.ceil(a/E),s=o>s?o+1:s+1,i>s&&(i=s,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for(s=u.length,i=c.length,s-i<0&&(i=s,r=c,c=u,u=r),t=0;i;)t=(u[--i]=u[i]+c[i]+t)/ue|0,u[i]%=ue;for(t&&(u.unshift(t),++n),s=u.length;u[--s]==0;)u.pop();return e.d=u,e.e=Pr(u,n),b?w(e,a,l):e};f.precision=f.sd=function(e){var t,r=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Se+e);return r.d?(t=Ao(r.d),e&&r.e+1>t&&(t=r.e+1)):t=NaN,t};f.round=function(){var e=this,t=e.constructor;return w(new t(e),e.e+1,t.rounding)};f.sine=f.sin=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=pu(n,Oo(n,r)),n.precision=e,n.rounding=t,w(Pe>2?r.neg():r,e,t,!0)):new n(NaN)};f.squareRoot=f.sqrt=function(){var e,t,r,n,i,o,s=this,a=s.d,l=s.e,u=s.s,c=s.constructor;if(u!==1||!a||!a[0])return new c(!u||u<0&&(!a||a[0])?NaN:a?s:1/0);for(b=!1,u=Math.sqrt(+s),u==0||u==1/0?(t=j(a),(t.length+l)%2==0&&(t+="0"),u=Math.sqrt(t),l=W((l+1)/2)-(l<0||l%2),u==1/0?t="5e"+l:(t=u.toExponential(),t=t.slice(0,t.indexOf("e")+1)+l),n=new c(t)):n=new c(u.toString()),r=(l=c.precision)+3;;)if(o=n,n=o.plus(_(s,o,r+2,1)).times(.5),j(o.d).slice(0,r)===(t=j(n.d)).slice(0,r))if(t=t.slice(r-3,r+1),t=="9999"||!i&&t=="4999"){if(!i&&(w(o,l+1,0),o.times(o).eq(s))){n=o;break}r+=4,i=1}else{(!+t||!+t.slice(1)&&t.charAt(0)=="5")&&(w(n,l+1,1),e=!n.times(n).eq(s));break}return b=!0,w(n,l,c.rounding,e)};f.tangent=f.tan=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,r=r.sin(),r.s=1,r=_(r,new n(1).minus(r.times(r)).sqrt(),e+10,0),n.precision=e,n.rounding=t,w(Pe==2||Pe==4?r.neg():r,e,t,!0)):new n(NaN)};f.times=f.mul=function(e){var t,r,n,i,o,s,a,l,u,c=this,p=c.constructor,m=c.d,g=(e=new p(e)).d;if(e.s*=c.s,!m||!m[0]||!g||!g[0])return new p(!e.s||m&&!m[0]&&!g||g&&!g[0]&&!m?NaN:!m||!g?e.s/0:e.s*0);for(r=W(c.e/E)+W(e.e/E),l=m.length,u=g.length,l<u&&(o=m,m=g,g=o,s=l,l=u,u=s),o=[],s=l+u,n=s;n--;)o.push(0);for(n=u;--n>=0;){for(t=0,i=l+n;i>n;)a=o[i]+g[n]*m[i-n-1]+t,o[i--]=a%ue|0,t=a/ue|0;o[i]=(o[i]+t)%ue|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=Pr(o,r),b?w(e,p.precision,p.rounding):e};f.toBinary=function(e,t){return Qn(this,2,e,t)};f.toDecimalPlaces=f.toDP=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(X(e,0,ke),t===void 0?t=n.rounding:X(t,0,8),w(r,e+r.e+1,t))};f.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=me(n,!0):(X(e,0,ke),t===void 0?t=i.rounding:X(t,0,8),n=w(new i(n),e+1,t),r=me(n,!0,e+1)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?r=me(i):(X(e,0,ke),t===void 0?t=o.rounding:X(t,0,8),n=w(new o(i),e+i.e+1,t),r=me(n,!1,e+n.e+1)),i.isNeg()&&!i.isZero()?"-"+r:r};f.toFraction=function(e){var t,r,n,i,o,s,a,l,u,c,p,m,g=this,h=g.d,y=g.constructor;if(!h)return new y(g);if(u=r=new y(1),n=l=new y(0),t=new y(n),o=t.e=Ao(h)-g.e-1,s=o%E,t.d[0]=q(10,s<0?E+s:s),e==null)e=o>0?t:u;else{if(a=new y(e),!a.isInt()||a.lt(u))throw Error(Se+a);e=a.gt(t)?o>0?t:u:a}for(b=!1,a=new y(j(h)),c=y.precision,y.precision=o=h.length*E*2;p=_(a,t,0,1,1),i=r.plus(p.times(n)),i.cmp(e)!=1;)r=n,n=i,i=u,u=l.plus(p.times(i)),l=i,i=t,t=a.minus(p.times(i)),a=i;return i=_(e.minus(r),n,0,1,1),l=l.plus(i.times(u)),r=r.plus(i.times(n)),l.s=u.s=g.s,m=_(u,n,o,1).minus(g).abs().cmp(_(l,r,o,1).minus(g).abs())<1?[u,n]:[l,r],y.precision=c,b=!0,m};f.toHexadecimal=f.toHex=function(e,t){return Qn(this,16,e,t)};f.toNearest=function(e,t){var r=this,n=r.constructor;if(r=new n(r),e==null){if(!r.d)return r;e=new n(1),t=n.rounding}else{if(e=new n(e),t===void 0?t=n.rounding:X(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(b=!1,r=_(r,e,0,t,1).times(e),b=!0,w(r)):(e.s=r.s,r=e),r};f.toNumber=function(){return+this};f.toOctal=function(e,t){return Qn(this,8,e,t)};f.toPower=f.pow=function(e){var t,r,n,i,o,s,a=this,l=a.constructor,u=+(e=new l(e));if(!a.d||!e.d||!a.d[0]||!e.d[0])return new l(q(+a,u));if(a=new l(a),a.eq(1))return a;if(n=l.precision,o=l.rounding,e.eq(1))return w(a,n,o);if(t=W(e.e/E),t>=e.d.length-1&&(r=u<0?-u:u)<=au)return i=Ro(l,a,r,n),e.s<0?new l(1).div(i):w(i,n,o);if(s=a.s,s<0){if(t<e.d.length-1)return new l(NaN);if(e.d[t]&1||(s=1),a.e==0&&a.d[0]==1&&a.d.length==1)return a.s=s,a}return r=q(+a,u),t=r==0||!isFinite(r)?W(u*(Math.log("0."+j(a.d))/Math.LN10+a.e+1)):new l(r+"").e,t>l.maxE+1||t<l.minE-1?new l(t>0?s/0:0):(b=!1,l.rounding=a.s=1,r=Math.min(12,(t+"").length),i=Bn(e.times(Re(a,n+r)),n),i.d&&(i=w(i,n+5,1),It(i.d,n,o)&&(t=n+10,i=w(Bn(e.times(Re(a,t+r)),t),t+5,1),+j(i.d).slice(n+1,n+15)+1==1e14&&(i=w(i,n+1,0)))),i.s=s,b=!0,l.rounding=o,w(i,n,o))};f.toPrecision=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=me(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(X(e,1,ke),t===void 0?t=i.rounding:X(t,0,8),n=w(new i(n),e,t),r=me(n,e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toSignificantDigits=f.toSD=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(X(e,1,ke),t===void 0?t=n.rounding:X(t,0,8)),w(new n(r),e,t)};f.toString=function(){var e=this,t=e.constructor,r=me(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+r:r};f.truncated=f.trunc=function(){return w(new this.constructor(this),this.e+1,1)};f.valueOf=f.toJSON=function(){var e=this,t=e.constructor,r=me(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+r:r};function j(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=E-n.length,r&&(o+=Ae(r)),o+=n;s=e[t],n=s+"",r=E-n.length,r&&(o+=Ae(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function X(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Se+e)}function It(e,t,r,n){var i,o,s,a;for(o=e[0];o>=10;o/=10)--t;return--t<0?(t+=E,i=0):(i=Math.ceil((t+1)/E),t%=E),o=q(10,E-t),a=e[i]%o|0,n==null?t<3?(t==0?a=a/100|0:t==1&&(a=a/10|0),s=r<4&&a==99999||r>3&&a==49999||a==5e4||a==0):s=(r<4&&a+1==o||r>3&&a+1==o/2)&&(e[i+1]/o/100|0)==q(10,t-2)-1||(a==o/2||a==0)&&(e[i+1]/o/100|0)==0:t<4?(t==0?a=a/1e3|0:t==1?a=a/100|0:t==2&&(a=a/10|0),s=(n||r<4)&&a==9999||!n&&r>3&&a==4999):s=((n||r<4)&&a+1==o||!n&&r>3&&a+1==o/2)&&(e[i+1]/o/1e3|0)==q(10,t-3)-1,s}function yr(e,t,r){for(var n,i=[0],o,s=0,a=e.length;s<a;){for(o=i.length;o--;)i[o]*=t;for(i[0]+=Vn.indexOf(e.charAt(s++)),n=0;n<i.length;n++)i[n]>r-1&&(i[n+1]===void 0&&(i[n+1]=0),i[n+1]+=i[n]/r|0,i[n]%=r)}return i.reverse()}function uu(e,t){var r,n,i;if(t.isZero())return t;n=t.d.length,n<32?(r=Math.ceil(n/3),i=(1/Tr(4,r)).toString()):(r=16,i="2.3283064365386962890625e-10"),e.precision+=r,t=Ke(e,1,t.times(i),new e(1));for(var o=r;o--;){var s=t.times(t);t=s.times(s).minus(s).times(8).plus(1)}return e.precision-=r,t}var _=function(){function e(n,i,o){var s,a=0,l=n.length;for(n=n.slice();l--;)s=n[l]*i+a,n[l]=s%o|0,a=s/o|0;return a&&n.unshift(a),n}function t(n,i,o,s){var a,l;if(o!=s)l=o>s?1:-1;else for(a=l=0;a<o;a++)if(n[a]!=i[a]){l=n[a]>i[a]?1:-1;break}return l}function r(n,i,o,s){for(var a=0;o--;)n[o]-=a,a=n[o]<i[o]?1:0,n[o]=a*s+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s,a,l){var u,c,p,m,g,h,y,S,P,C,T,O,R,re,wt,L,Y,Ee,U,Qe,cr=n.constructor,yn=n.s==i.s?1:-1,B=n.d,D=i.d;if(!B||!B[0]||!D||!D[0])return new cr(!n.s||!i.s||(B?D&&B[0]==D[0]:!D)?NaN:B&&B[0]==0||!D?yn*0:yn/0);for(l?(g=1,c=n.e-i.e):(l=ue,g=E,c=W(n.e/g)-W(i.e/g)),U=D.length,Y=B.length,P=new cr(yn),C=P.d=[],p=0;D[p]==(B[p]||0);p++);if(D[p]>(B[p]||0)&&c--,o==null?(re=o=cr.precision,s=cr.rounding):a?re=o+(n.e-i.e)+1:re=o,re<0)C.push(1),h=!0;else{if(re=re/g+2|0,p=0,U==1){for(m=0,D=D[0],re++;(p<Y||m)&&re--;p++)wt=m*l+(B[p]||0),C[p]=wt/D|0,m=wt%D|0;h=m||p<Y}else{for(m=l/(D[0]+1)|0,m>1&&(D=e(D,m,l),B=e(B,m,l),U=D.length,Y=B.length),L=U,T=B.slice(0,U),O=T.length;O<U;)T[O++]=0;Qe=D.slice(),Qe.unshift(0),Ee=D[0],D[1]>=l/2&&++Ee;do m=0,u=t(D,T,U,O),u<0?(R=T[0],U!=O&&(R=R*l+(T[1]||0)),m=R/Ee|0,m>1?(m>=l&&(m=l-1),y=e(D,m,l),S=y.length,O=T.length,u=t(y,T,S,O),u==1&&(m--,r(y,U<S?Qe:D,S,l))):(m==0&&(u=m=1),y=D.slice()),S=y.length,S<O&&y.unshift(0),r(T,y,O,l),u==-1&&(O=T.length,u=t(D,T,U,O),u<1&&(m++,r(T,U<O?Qe:D,O,l))),O=T.length):u===0&&(m++,T=[0]),C[p++]=m,u&&T[0]?T[O++]=B[L]||0:(T=[B[L]],O=1);while((L++<Y||T[0]!==void 0)&&re--);h=T[0]!==void 0}C[0]||C.shift()}if(g==1)P.e=c,xo=h;else{for(p=1,m=C[0];m>=10;m/=10)p++;P.e=p+c*g-1,w(P,a?o+P.e+1:o,s,h)}return P}}();function w(e,t,r,n){var i,o,s,a,l,u,c,p,m,g=e.constructor;e:if(t!=null){if(p=e.d,!p)return e;for(i=1,a=p[0];a>=10;a/=10)i++;if(o=t-i,o<0)o+=E,s=t,c=p[m=0],l=c/q(10,i-s-1)%10|0;else if(m=Math.ceil((o+1)/E),a=p.length,m>=a)if(n){for(;a++<=m;)p.push(0);c=l=0,i=1,o%=E,s=o-E+1}else break e;else{for(c=a=p[m],i=1;a>=10;a/=10)i++;o%=E,s=o-E+i,l=s<0?0:c/q(10,i-s-1)%10|0}if(n=n||t<0||p[m+1]!==void 0||(s<0?c:c%q(10,i-s-1)),u=r<4?(l||n)&&(r==0||r==(e.s<0?3:2)):l>5||l==5&&(r==4||n||r==6&&(o>0?s>0?c/q(10,i-s):0:p[m-1])%10&1||r==(e.s<0?8:7)),t<1||!p[0])return p.length=0,u?(t-=e.e+1,p[0]=q(10,(E-t%E)%E),e.e=-t||0):p[0]=e.e=0,e;if(o==0?(p.length=m,a=1,m--):(p.length=m+1,a=q(10,E-o),p[m]=s>0?(c/q(10,i-s)%q(10,s)|0)*a:0),u)for(;;)if(m==0){for(o=1,s=p[0];s>=10;s/=10)o++;for(s=p[0]+=a,a=1;s>=10;s/=10)a++;o!=a&&(e.e++,p[0]==ue&&(p[0]=1));break}else{if(p[m]+=a,p[m]!=ue)break;p[m--]=0,a=1}for(o=p.length;p[--o]===0;)p.pop()}return b&&(e.e>g.maxE?(e.d=null,e.e=NaN):e.e<g.minE&&(e.e=0,e.d=[0])),e}function me(e,t,r){if(!e.isFinite())return ko(e);var n,i=e.e,o=j(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ae(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):i<0?(o="0."+Ae(-i-1)+o,r&&(n=r-s)>0&&(o+=Ae(n))):i>=s?(o+=Ae(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Ae(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Ae(n))),o}function Pr(e,t){var r=e[0];for(t*=E;r>=10;r/=10)t++;return t}function xr(e,t,r){if(t>lu)throw b=!0,r&&(e.precision=r),Error(vo);return w(new e(Er),t,1,!0)}function de(e,t,r){if(t>Un)throw Error(vo);return w(new e(br),t,r,!0)}function Ao(e){var t=e.length-1,r=t*E+1;if(t=e[t],t){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function Ae(e){for(var t="";e--;)t+="0";return t}function Ro(e,t,r,n){var i,o=new e(1),s=Math.ceil(n/E+4);for(b=!1;;){if(r%2&&(o=o.times(t),Eo(o.d,s)&&(i=!0)),r=W(r/2),r===0){r=o.d.length-1,i&&o.d[r]===0&&++o.d[r];break}t=t.times(t),Eo(t.d,s)}return b=!0,o}function wo(e){return e.d[e.d.length-1]&1}function So(e,t,r){for(var n,i,o=new e(t[0]),s=0;++s<t.length;){if(i=new e(t[s]),!i.s){o=i;break}n=o.cmp(i),(n===r||n===0&&o.s===r)&&(o=i)}return o}function Bn(e,t){var r,n,i,o,s,a,l,u=0,c=0,p=0,m=e.constructor,g=m.rounding,h=m.precision;if(!e.d||!e.d[0]||e.e>17)return new m(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(t==null?(b=!1,l=h):l=t,a=new m(.03125);e.e>-2;)e=e.times(a),p+=5;for(n=Math.log(q(2,p))/Math.LN10*2+5|0,l+=n,r=o=s=new m(1),m.precision=l;;){if(o=w(o.times(e),l,1),r=r.times(++c),a=s.plus(_(o,r,l,1)),j(a.d).slice(0,l)===j(s.d).slice(0,l)){for(i=p;i--;)s=w(s.times(s),l,1);if(t==null)if(u<3&&It(s.d,l-n,g,u))m.precision=l+=10,r=o=a=new m(1),c=0,u++;else return w(s,m.precision=h,g,b=!0);else return m.precision=h,s}s=a}}function Re(e,t){var r,n,i,o,s,a,l,u,c,p,m,g=1,h=10,y=e,S=y.d,P=y.constructor,C=P.rounding,T=P.precision;if(y.s<0||!S||!S[0]||!y.e&&S[0]==1&&S.length==1)return new P(S&&!S[0]?-1/0:y.s!=1?NaN:S?0:y);if(t==null?(b=!1,c=T):c=t,P.precision=c+=h,r=j(S),n=r.charAt(0),Math.abs(o=y.e)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)y=y.times(e),r=j(y.d),n=r.charAt(0),g++;o=y.e,n>1?(y=new P("0."+r),o++):y=new P(n+"."+r.slice(1))}else return u=xr(P,c+2,T).times(o+""),y=Re(new P(n+"."+r.slice(1)),c-h).plus(u),P.precision=T,t==null?w(y,T,C,b=!0):y;for(p=y,l=s=y=_(y.minus(1),y.plus(1),c,1),m=w(y.times(y),c,1),i=3;;){if(s=w(s.times(m),c,1),u=l.plus(_(s,new P(i),c,1)),j(u.d).slice(0,c)===j(l.d).slice(0,c))if(l=l.times(2),o!==0&&(l=l.plus(xr(P,c+2,T).times(o+""))),l=_(l,new P(g),c,1),t==null)if(It(l.d,c-h,C,a))P.precision=c+=h,u=s=y=_(p.minus(1),p.plus(1),c,1),m=w(y.times(y),c,1),i=a=1;else return w(l,P.precision=T,C,b=!0);else return P.precision=T,l;l=u,i+=2}}function ko(e){return String(e.s*e.s/0)}function wr(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;n++);for(i=t.length;t.charCodeAt(i-1)===48;--i);if(t=t.slice(n,i),t){if(i-=n,e.e=r=r-n-1,e.d=[],n=(r+1)%E,r<0&&(n+=E),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=E;n<i;)e.d.push(+t.slice(n,n+=E));t=t.slice(n),n=E-t.length}else n-=i;for(;n--;)t+="0";e.d.push(+t),b&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function cu(e,t){var r,n,i,o,s,a,l,u,c;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),Co.test(t))return wr(e,t)}else if(t==="Infinity"||t==="NaN")return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(ou.test(t))r=16,t=t.toLowerCase();else if(iu.test(t))r=2;else if(su.test(t))r=8;else throw Error(Se+t);for(o=t.search(/p/i),o>0?(l=+t.slice(o+1),t=t.substring(2,o)):t=t.slice(2),o=t.indexOf("."),s=o>=0,n=e.constructor,s&&(t=t.replace(".",""),a=t.length,o=a-o,i=Ro(n,new n(r),o,o*2)),u=yr(t,r,ue),c=u.length-1,o=c;u[o]===0;--o)u.pop();return o<0?new n(e.s*0):(e.e=Pr(u,c),e.d=u,b=!1,s&&(e=_(e,i,a*4)),l&&(e=e.times(Math.abs(l)<54?q(2,l):Le.pow(2,l))),b=!0,e)}function pu(e,t){var r,n=t.d.length;if(n<3)return t.isZero()?t:Ke(e,2,t,t);r=1.4*Math.sqrt(n),r=r>16?16:r|0,t=t.times(1/Tr(5,r)),t=Ke(e,2,t,t);for(var i,o=new e(5),s=new e(16),a=new e(20);r--;)i=t.times(t),t=t.times(o.plus(i.times(s.times(i).minus(a))));return t}function Ke(e,t,r,n,i){var o,s,a,l,u=1,c=e.precision,p=Math.ceil(c/E);for(b=!1,l=r.times(r),a=new e(n);;){if(s=_(a.times(l),new e(t++*t++),c,1),a=i?n.plus(s):n.minus(s),n=_(s.times(l),new e(t++*t++),c,1),s=a.plus(n),s.d[p]!==void 0){for(o=p;s.d[o]===a.d[o]&&o--;);if(o==-1)break}o=a,a=n,n=s,s=o,u++}return b=!0,s.d.length=p+1,s}function Tr(e,t){for(var r=e;--t;)r*=e;return r}function Oo(e,t){var r,n=t.s<0,i=de(e,e.precision,1),o=i.times(.5);if(t=t.abs(),t.lte(o))return Pe=n?4:1,t;if(r=t.divToInt(i),r.isZero())Pe=n?3:2;else{if(t=t.minus(r.times(i)),t.lte(o))return Pe=wo(r)?n?2:3:n?4:1,t;Pe=wo(r)?n?1:4:n?3:2}return t.minus(i).abs()}function Qn(e,t,r,n){var i,o,s,a,l,u,c,p,m,g=e.constructor,h=r!==void 0;if(h?(X(r,1,ke),n===void 0?n=g.rounding:X(n,0,8)):(r=g.precision,n=g.rounding),!e.isFinite())c=ko(e);else{for(c=me(e),s=c.indexOf("."),h?(i=2,t==16?r=r*4-3:t==8&&(r=r*3-2)):i=t,s>=0&&(c=c.replace(".",""),m=new g(1),m.e=c.length-s,m.d=yr(me(m),10,i),m.e=m.d.length),p=yr(c,10,i),o=l=p.length;p[--l]==0;)p.pop();if(!p[0])c=h?"0p+0":"0";else{if(s<0?o--:(e=new g(e),e.d=p,e.e=o,e=_(e,m,r,n,0,i),p=e.d,o=e.e,u=xo),s=p[r],a=i/2,u=u||p[r+1]!==void 0,u=n<4?(s!==void 0||u)&&(n===0||n===(e.s<0?3:2)):s>a||s===a&&(n===4||u||n===6&&p[r-1]&1||n===(e.s<0?8:7)),p.length=r,u)for(;++p[--r]>i-1;)p[r]=0,r||(++o,p.unshift(1));for(l=p.length;!p[l-1];--l);for(s=0,c="";s<l;s++)c+=Vn.charAt(p[s]);if(h){if(l>1)if(t==16||t==8){for(s=t==16?4:3,--l;l%s;l++)c+="0";for(p=yr(c,i,t),l=p.length;!p[l-1];--l);for(s=1,c="1.";s<l;s++)c+=Vn.charAt(p[s])}else c=c.charAt(0)+"."+c.slice(1);c=c+(o<0?"p":"p+")+o}else if(o<0){for(;++o;)c="0"+c;c="0."+c}else if(++o>l)for(o-=l;o--;)c+="0";else o<l&&(c=c.slice(0,o)+"."+c.slice(o))}c=(t==16?"0x":t==2?"0b":t==8?"0o":"")+c}return e.s<0?"-"+c:c}function Eo(e,t){if(e.length>t)return e.length=t,!0}function du(e){return new this(e).abs()}function mu(e){return new this(e).acos()}function fu(e){return new this(e).acosh()}function gu(e,t){return new this(e).plus(t)}function hu(e){return new this(e).asin()}function yu(e){return new this(e).asinh()}function wu(e){return new this(e).atan()}function Eu(e){return new this(e).atanh()}function bu(e,t){e=new this(e),t=new this(t);var r,n=this.precision,i=this.rounding,o=n+4;return!e.s||!t.s?r=new this(NaN):!e.d&&!t.d?(r=de(this,o,1).times(t.s>0?.25:.75),r.s=e.s):!t.d||e.isZero()?(r=t.s<0?de(this,n,i):new this(0),r.s=e.s):!e.d||t.isZero()?(r=de(this,o,1).times(.5),r.s=e.s):t.s<0?(this.precision=o,this.rounding=1,r=this.atan(_(e,t,o,1)),t=de(this,o,1),this.precision=n,this.rounding=i,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(_(e,t,o,1)),r}function xu(e){return new this(e).cbrt()}function vu(e){return w(e=new this(e),e.e+1,2)}function Pu(e,t,r){return new this(e).clamp(t,r)}function Tu(e){if(!e||typeof e!="object")throw Error(vr+"Object expected");var t,r,n,i=e.defaults===!0,o=["precision",1,ke,"rounding",0,8,"toExpNeg",-He,0,"toExpPos",0,He,"maxE",0,He,"minE",-He,0,"modulo",0,9];for(t=0;t<o.length;t+=3)if(r=o[t],i&&(this[r]=jn[r]),(n=e[r])!==void 0)if(W(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(Se+r+": "+n);if(r="crypto",i&&(this[r]=jn[r]),(n=e[r])!==void 0)if(n===!0||n===!1||n===0||n===1)if(n)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error(Po);else this[r]=!1;else throw Error(Se+r+": "+n);return this}function Cu(e){return new this(e).cos()}function Au(e){return new this(e).cosh()}function Io(e){var t,r,n;function i(o){var s,a,l,u=this;if(!(u instanceof i))return new i(o);if(u.constructor=i,bo(o)){u.s=o.s,b?!o.d||o.e>i.maxE?(u.e=NaN,u.d=null):o.e<i.minE?(u.e=0,u.d=[0]):(u.e=o.e,u.d=o.d.slice()):(u.e=o.e,u.d=o.d?o.d.slice():o.d);return}if(l=typeof o,l==="number"){if(o===0){u.s=1/o<0?-1:1,u.e=0,u.d=[0];return}if(o<0?(o=-o,u.s=-1):u.s=1,o===~~o&&o<1e7){for(s=0,a=o;a>=10;a/=10)s++;b?s>i.maxE?(u.e=NaN,u.d=null):s<i.minE?(u.e=0,u.d=[0]):(u.e=s,u.d=[o]):(u.e=s,u.d=[o]);return}if(o*0!==0){o||(u.s=NaN),u.e=NaN,u.d=null;return}return wr(u,o.toString())}if(l==="string")return(a=o.charCodeAt(0))===45?(o=o.slice(1),u.s=-1):(a===43&&(o=o.slice(1)),u.s=1),Co.test(o)?wr(u,o):cu(u,o);if(l==="bigint")return o<0?(o=-o,u.s=-1):u.s=1,wr(u,o.toString());throw Error(Se+o)}if(i.prototype=f,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.EUCLID=9,i.config=i.set=Tu,i.clone=Io,i.isDecimal=bo,i.abs=du,i.acos=mu,i.acosh=fu,i.add=gu,i.asin=hu,i.asinh=yu,i.atan=wu,i.atanh=Eu,i.atan2=bu,i.cbrt=xu,i.ceil=vu,i.clamp=Pu,i.cos=Cu,i.cosh=Au,i.div=Ru,i.exp=Su,i.floor=ku,i.hypot=Ou,i.ln=Iu,i.log=Du,i.log10=Nu,i.log2=_u,i.max=Mu,i.min=Fu,i.mod=Lu,i.mul=$u,i.pow=qu,i.random=Vu,i.round=ju,i.sign=Uu,i.sin=Bu,i.sinh=Qu,i.sqrt=Gu,i.sub=Ju,i.sum=Wu,i.tan=Hu,i.tanh=Ku,i.trunc=zu,e===void 0&&(e={}),e&&e.defaults!==!0)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function Ru(e,t){return new this(e).div(t)}function Su(e){return new this(e).exp()}function ku(e){return w(e=new this(e),e.e+1,3)}function Ou(){var e,t,r=new this(0);for(b=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return b=!0,new this(1/0);r=t}return b=!0,r.sqrt()}function bo(e){return e instanceof Le||e&&e.toStringTag===To||!1}function Iu(e){return new this(e).ln()}function Du(e,t){return new this(e).log(t)}function _u(e){return new this(e).log(2)}function Nu(e){return new this(e).log(10)}function Mu(){return So(this,arguments,-1)}function Fu(){return So(this,arguments,1)}function Lu(e,t){return new this(e).mod(t)}function $u(e,t){return new this(e).mul(t)}function qu(e,t){return new this(e).pow(t)}function Vu(e){var t,r,n,i,o=0,s=new this(1),a=[];if(e===void 0?e=this.precision:X(e,1,ke),n=Math.ceil(e/E),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));o<n;)i=t[o],i>=429e7?t[o]=crypto.getRandomValues(new Uint32Array(1))[0]:a[o++]=i%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);o<n;)i=t[o]+(t[o+1]<<8)+(t[o+2]<<16)+((t[o+3]&127)<<24),i>=214e7?crypto.randomBytes(4).copy(t,o):(a.push(i%1e7),o+=4);o=n/4}else throw Error(Po);else for(;o<n;)a[o++]=Math.random()*1e7|0;for(n=a[--o],e%=E,n&&e&&(i=q(10,E-e),a[o]=(n/i|0)*i);a[o]===0;o--)a.pop();if(o<0)r=0,a=[0];else{for(r=-1;a[0]===0;r-=E)a.shift();for(n=1,i=a[0];i>=10;i/=10)n++;n<E&&(r-=E-n)}return s.e=r,s.d=a,s}function ju(e){return w(e=new this(e),e.e+1,this.rounding)}function Uu(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function Bu(e){return new this(e).sin()}function Qu(e){return new this(e).sinh()}function Gu(e){return new this(e).sqrt()}function Ju(e,t){return new this(e).sub(t)}function Wu(){var e=0,t=arguments,r=new this(t[e]);for(b=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return b=!0,w(r,this.precision,this.rounding)}function Hu(e){return new this(e).tan()}function Ku(e){return new this(e).tanh()}function zu(e){return w(e=new this(e),e.e+1,1)}f[Symbol.for("nodejs.util.inspect.custom")]=f.toString;f[Symbol.toStringTag]="Decimal";var Le=f.constructor=Io(jn);Er=new Le(Er);br=new Le(br);var fe=Le;function ze(e){return e===null?e:Array.isArray(e)?e.map(ze):typeof e=="object"?Yu(e)?Zu(e):We(e,ze):e}function Yu(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function Zu({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new fe(t);case"Json":return JSON.parse(t);default:ae(t,"Unknown tagged value")}}function Ye(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function Ze(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function Cr(e){return e.toString()!=="Invalid Date"}function Xe(e){return Le.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}var Lo=H(uo());var Fo=H(require("fs"));var Do={keyword:xe,entity:xe,value:e=>K(Fe(e)),punctuation:Fe,directive:xe,function:xe,variable:e=>K(Fe(e)),string:e=>K(xt(e)),boolean:Me,number:xe,comment:vt};var Xu=e=>e,Ar={},ec=0,v={manual:Ar.Prism&&Ar.Prism.manual,disableWorkerMessageHandler:Ar.Prism&&Ar.Prism.disableWorkerMessageHandler,util:{encode:function(e){if(e instanceof ce){let t=e;return new ce(t.type,v.util.encode(t.content),t.alias)}else return Array.isArray(e)?e.map(v.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++ec}),e.__id},clone:function e(t,r){let n,i,o=v.util.type(t);switch(r=r||{},o){case"Object":if(i=v.util.objId(t),r[i])return r[i];n={},r[i]=n;for(let s in t)t.hasOwnProperty(s)&&(n[s]=e(t[s],r));return n;case"Array":return i=v.util.objId(t),r[i]?r[i]:(n=[],r[i]=n,t.forEach(function(s,a){n[a]=e(s,r)}),n);default:return t}}},languages:{extend:function(e,t){let r=v.util.clone(v.languages[e]);for(let n in t)r[n]=t[n];return r},insertBefore:function(e,t,r,n){n=n||v.languages;let i=n[e],o={};for(let a in i)if(i.hasOwnProperty(a)){if(a==t)for(let l in r)r.hasOwnProperty(l)&&(o[l]=r[l]);r.hasOwnProperty(a)||(o[a]=i[a])}let s=n[e];return n[e]=o,v.languages.DFS(v.languages,function(a,l){l===s&&a!=e&&(this[a]=o)}),o},DFS:function e(t,r,n,i){i=i||{};let o=v.util.objId;for(let s in t)if(t.hasOwnProperty(s)){r.call(t,s,t[s],n||s);let a=t[s],l=v.util.type(a);l==="Object"&&!i[o(a)]?(i[o(a)]=!0,e(a,r,null,i)):l==="Array"&&!i[o(a)]&&(i[o(a)]=!0,e(a,r,s,i))}}},plugins:{},highlight:function(e,t,r){let n={code:e,grammar:t,language:r};return v.hooks.run("before-tokenize",n),n.tokens=v.tokenize(n.code,n.grammar),v.hooks.run("after-tokenize",n),ce.stringify(v.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,r,n,i,o,s){for(let y in r){if(!r.hasOwnProperty(y)||!r[y])continue;if(y==s)return;let S=r[y];S=v.util.type(S)==="Array"?S:[S];for(let P=0;P<S.length;++P){let C=S[P],T=C.inside,O=!!C.lookbehind,R=!!C.greedy,re=0,wt=C.alias;if(R&&!C.pattern.global){let L=C.pattern.toString().match(/[imuy]*$/)[0];C.pattern=RegExp(C.pattern.source,L+"g")}C=C.pattern||C;for(let L=n,Y=i;L<t.length;Y+=t[L].length,++L){let Ee=t[L];if(t.length>e.length)return;if(Ee instanceof ce)continue;if(R&&L!=t.length-1){C.lastIndex=Y;var p=C.exec(e);if(!p)break;var c=p.index+(O?p[1].length:0),m=p.index+p[0].length,a=L,l=Y;for(let D=t.length;a<D&&(l<m||!t[a].type&&!t[a-1].greedy);++a)l+=t[a].length,c>=l&&(++L,Y=l);if(t[L]instanceof ce)continue;u=a-L,Ee=e.slice(Y,l),p.index-=Y}else{C.lastIndex=0;var p=C.exec(Ee),u=1}if(!p){if(o)break;continue}O&&(re=p[1]?p[1].length:0);var c=p.index+re,p=p[0].slice(re),m=c+p.length,g=Ee.slice(0,c),h=Ee.slice(m);let U=[L,u];g&&(++L,Y+=g.length,U.push(g));let Qe=new ce(y,T?v.tokenize(p,T):p,wt,p,R);if(U.push(Qe),h&&U.push(h),Array.prototype.splice.apply(t,U),u!=1&&v.matchGrammar(e,t,r,L,Y,!0,y),o)break}}}},tokenize:function(e,t){let r=[e],n=t.rest;if(n){for(let i in n)t[i]=n[i];delete t.rest}return v.matchGrammar(e,r,t,0,0,!1),r},hooks:{all:{},add:function(e,t){let r=v.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){let r=v.hooks.all[e];if(!(!r||!r.length))for(var n=0,i;i=r[n++];)i(t)}},Token:ce};v.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/};v.languages.javascript=v.languages.extend("clike",{"class-name":[v.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/});v.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;v.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:v.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:v.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:v.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:v.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/});v.languages.markup&&v.languages.markup.tag.addInlined("script","javascript");v.languages.js=v.languages.javascript;v.languages.typescript=v.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/});v.languages.ts=v.languages.typescript;function ce(e,t,r,n,i){this.type=e,this.content=t,this.alias=r,this.length=(n||"").length|0,this.greedy=!!i}ce.stringify=function(e,t){return typeof e=="string"?e:Array.isArray(e)?e.map(function(r){return ce.stringify(r,t)}).join(""):tc(e.type)(e.content)};function tc(e){return Do[e]||Xu}function _o(e){return rc(e,v.languages.javascript)}function rc(e,t){return v.tokenize(e,t).map(n=>ce.stringify(n)).join("")}var No=H(so());function Mo(e){return(0,No.default)(e)}var Rr=class e{constructor(t,r){d(this,"firstLineNumber");d(this,"lines");this.firstLineNumber=t,this.lines=r}static read(t){let r;try{r=Fo.default.readFileSync(t,"utf-8")}catch{return null}return e.fromContent(r)}static fromContent(t){let r=t.split(/\r?\n/);return new e(1,r)}get lastLineNumber(){return this.firstLineNumber+this.lines.length-1}mapLineAt(t,r){if(t<this.firstLineNumber||t>this.lines.length+this.firstLineNumber)return this;let n=t-this.firstLineNumber,i=[...this.lines];return i[n]=r(i[n]),new e(this.firstLineNumber,i)}mapLines(t){return new e(this.firstLineNumber,this.lines.map((r,n)=>t(r,this.firstLineNumber+n)))}lineAt(t){return this.lines[t-this.firstLineNumber]}prependSymbolAt(t,r){return this.mapLines((n,i)=>i===t?`${r} ${n}`:`  ${n}`)}slice(t,r){let n=this.lines.slice(t-1,r).join(`
`);return new e(t,Mo(n).split(`
`))}highlight(){let t=_o(this.toString());return new e(this.firstLineNumber,t.split(`
`))}toString(){return this.lines.join(`
`)}};var nc={red:be,gray:vt,dim:Ne,bold:K,underline:Z,highlightSource:e=>e.highlight()},ic={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function oc({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function sc({callsite:e,message:t,originalMethod:r,isPanic:n,callArguments:i},o){let s=oc({message:t,originalMethod:r,isPanic:n,callArguments:i});if(!e||typeof window<"u"||process.env.NODE_ENV==="production")return s;let a=e.getLocation();if(!a||!a.lineNumber||!a.columnNumber)return s;let l=Math.max(1,a.lineNumber-3),u=Rr.read(a.fileName)?.slice(l,a.lineNumber),c=u?.lineAt(a.lineNumber);if(u&&c){let p=lc(c),m=ac(c);if(!m)return s;s.functionName=`${m.code})`,s.location=a,n||(u=u.mapLineAt(a.lineNumber,h=>h.slice(0,m.openingBraceIndex))),u=o.highlightSource(u);let g=String(u.lastLineNumber).length;if(s.contextLines=u.mapLines((h,y)=>o.gray(String(y).padStart(g))+" "+h).mapLines(h=>o.dim(h)).prependSymbolAt(a.lineNumber,o.bold(o.red("\u2192"))),i){let h=p+g+1;h+=2,s.callArguments=(0,Lo.default)(i,h).slice(h)}}return s}function ac(e){let t=Object.keys(At.ModelAction).join("|"),n=new RegExp(String.raw`\.(${t})\(`).exec(e);if(n){let i=n.index+n[0].length,o=e.lastIndexOf(" ",n.index)+1;return{code:e.slice(o,i),openingBraceIndex:i}}return null}function lc(e){let t=0;for(let r=0;r<e.length;r++){if(e.charAt(r)!==" ")return t;t++}return t}function uc({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],l=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${l}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${l}`)),t&&a.push(s.underline(cc(t))),i){a.push("");let u=[i.toString()];o&&(u.push(o),u.push(s.dim(")"))),a.push(u.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function cc(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function Sr(e){let t=e.showColors?nc:ic,r;return r=sc(e,t),uc(r,t)}var Qo=H(Gn());function jo(e,t,r){let n=Uo(e),i=pc(n),o=mc(i);o?kr(o,t,r):t.addErrorMessage(()=>"Unknown error")}function Uo(e){return e.errors.flatMap(t=>t.kind==="Union"?Uo(t):[t])}function pc(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:dc(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function dc(e,t){return[...new Set(e.concat(t))]}function mc(e){return qn(e,(t,r)=>{let n=qo(t),i=qo(r);return n!==i?n-i:Vo(t)-Vo(r)})}function qo(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function Vo(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}var ne=class{constructor(t,r){this.name=t;this.value=r;d(this,"isRequired",!1)}makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};var et=class{constructor(t=0,r){this.context=r;d(this,"lines",[]);d(this,"currentLine","");d(this,"currentIndent",0);d(this,"marginSymbol");d(this,"afterNextNewLineCallback");this.currentIndent=t}write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};var Or=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};var Ir=e=>e,Dr={bold:Ir,red:Ir,green:Ir,dim:Ir,enabled:!1},Bo={bold:K,red:be,green:xt,dim:Ne,enabled:!0},tt={write(e){e.writeLine(",")}};var ge=class{constructor(t){this.contents=t;d(this,"isUnderlined",!1);d(this,"color",t=>t)}underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};var Oe=class{constructor(){d(this,"hasError",!1)}markAsError(){return this.hasError=!0,this}};var rt=class extends Oe{constructor(){super(...arguments);d(this,"items",[])}addItem(r){return this.items.push(new Or(r)),this}getField(r){return this.items[r]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(n=>n.value.getPrintWidth()))+2}write(r){if(this.items.length===0){this.writeEmpty(r);return}this.writeWithItems(r)}writeEmpty(r){let n=new ge("[]");this.hasError&&n.setColor(r.context.colors.red).underline(),r.write(n)}writeWithItems(r){let{colors:n}=r.context;r.writeLine("[").withIndent(()=>r.writeJoined(tt,this.items).newLine()).write("]"),this.hasError&&r.afterNextNewline(()=>{r.writeLine(n.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var nt=class e extends Oe{constructor(){super(...arguments);d(this,"fields",{});d(this,"suggestions",[])}addField(r){this.fields[r.name]=r}addSuggestion(r){this.suggestions.push(r)}getField(r){return this.fields[r]}getDeepField(r){let[n,...i]=r,o=this.getField(n);if(!o)return;let s=o;for(let a of i){let l;if(s.value instanceof e?l=s.value.getField(a):s.value instanceof rt&&(l=s.value.getField(Number(a))),!l)return;s=l}return s}getDeepFieldValue(r){return r.length===0?this:this.getDeepField(r)?.value}hasField(r){return!!this.getField(r)}removeAllFields(){this.fields={}}removeField(r){delete this.fields[r]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(r){return this.getField(r)?.value}getDeepSubSelectionValue(r){let n=this;for(let i of r){if(!(n instanceof e))return;let o=n.getSubSelectionValue(i);if(!o)return;n=o}return n}getDeepSelectionParent(r){let n=this.getSelectionParent();if(!n)return;let i=n;for(let o of r){let s=i.value.getFieldValue(o);if(!s||!(s instanceof e))return;let a=s.getSelectionParent();if(!a)return;i=a}return i}getSelectionParent(){let r=this.getField("select")?.value.asObject();if(r)return{kind:"select",value:r};let n=this.getField("include")?.value.asObject();if(n)return{kind:"include",value:n}}getSubSelectionValue(r){return this.getSelectionParent()?.value.fields[r].value}getPrintWidth(){let r=Object.values(this.fields);return r.length==0?2:Math.max(...r.map(i=>i.getPrintWidth()))+2}write(r){let n=Object.values(this.fields);if(n.length===0&&this.suggestions.length===0){this.writeEmpty(r);return}this.writeWithContents(r,n)}asObject(){return this}writeEmpty(r){let n=new ge("{}");this.hasError&&n.setColor(r.context.colors.red).underline(),r.write(n)}writeWithContents(r,n){r.writeLine("{").withIndent(()=>{r.writeJoined(tt,[...n,...this.suggestions]).newLine()}),r.write("}"),this.hasError&&r.afterNextNewline(()=>{r.writeLine(r.context.colors.red("~".repeat(this.getPrintWidth())))})}};var V=class extends Oe{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new ge(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};var Dt=class{constructor(){d(this,"fields",[])}addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(tt,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function kr(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":gc(e,t);break;case"IncludeOnScalar":hc(e,t);break;case"EmptySelection":yc(e,t,r);break;case"UnknownSelectionField":xc(e,t);break;case"InvalidSelectionValue":vc(e,t);break;case"UnknownArgument":Pc(e,t);break;case"UnknownInputField":Tc(e,t);break;case"RequiredArgumentMissing":Cc(e,t);break;case"InvalidArgumentType":Ac(e,t);break;case"InvalidArgumentValue":Rc(e,t);break;case"ValueTooLarge":Sc(e,t);break;case"SomeFieldsMissing":kc(e,t);break;case"TooManyFieldsGiven":Oc(e,t);break;case"Union":jo(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function gc(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function hc(e,t){let[r,n]=_t(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new ne(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${Nt(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function yc(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){wc(e,t,i);return}if(n.hasField("select")){Ec(e,t);return}}if(r?.[Ye(e.outputType.name)]){bc(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function wc(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new ne(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function Ec(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Wo(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${Nt(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function bc(e,t){let r=new Dt;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new ne("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=_t(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let l=a?.value.asObject()??new nt;l.addSuggestion(n),a.value=l}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function xc(e,t){let r=Ho(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":Wo(n,e.outputType);break;case"include":Ic(n,e.outputType);break;case"omit":Dc(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(Nt(n)),i.join(" ")})}function vc(e,t){let r=Ho(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function Pc(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),_c(n,e.arguments)),t.addErrorMessage(i=>Go(i,r,e.arguments.map(o=>o.name)))}function Tc(e,t){let[r,n]=_t(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&Ko(o,e.inputType)}t.addErrorMessage(o=>Go(o,n,e.inputType.fields.map(s=>s.name)))}function Go(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=Mc(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(Nt(e)),n.join(" ")}function Cc(e,t){let r;t.addErrorMessage(l=>r?.value instanceof V&&r.value.text==="null"?`Argument \`${l.green(o)}\` must not be ${l.red("null")}.`:`Argument \`${l.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=_t(e.argumentPath),s=new Dt,a=n.getDeepFieldValue(i)?.asObject();if(a)if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let l of e.inputTypes[0].fields)s.addField(l.name,l.typeNames.join(" | "));a.addSuggestion(new ne(o,s).makeRequired())}else{let l=e.inputTypes.map(Jo).join(" | ");a.addSuggestion(new ne(o,l).makeRequired())}}function Jo(e){return e.kind==="list"?`${Jo(e.elementType)}[]`:e.name}function Ac(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=_r("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function Rc(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=_r("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function Sc(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof V&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function kc(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&Ko(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${_r("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(Nt(i)),o.join(" ")})}function Oc(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${_r("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Wo(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ne(r.name,"true"))}function Ic(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new ne(r.name,"true"))}function Dc(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new ne(r.name,"true"))}function _c(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new ne(r.name,r.typeNames.join(" | ")))}function Ho(e,t){let[r,n]=_t(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),l=o?.getField(n);return o&&l?{parentKind:"select",parent:o,field:l,fieldName:n}:(l=s?.getField(n),s&&l?{parentKind:"include",field:l,parent:s,fieldName:n}:(l=a?.getField(n),a&&l?{parentKind:"omit",field:l,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function Ko(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ne(r.name,r.typeNames.join(" | ")))}function _t(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function Nt({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function _r(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var Nc=3;function Mc(e,t){let r=1/0,n;for(let i of t){let o=(0,Qo.default)(e,i);o>Nc||o<r&&(r=o,n=i)}return n}function zo(e){return e.substring(0,1).toLowerCase()+e.substring(1)}var Mt=class{constructor(t,r,n,i,o){d(this,"modelName");d(this,"name");d(this,"typeName");d(this,"isList");d(this,"isEnum");this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function it(e){return e instanceof Mt}var Nr=Symbol(),Jn=new WeakMap,Te=class{constructor(t){t===Nr?Jn.set(this,`Prisma.${this._getName()}`):Jn.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return Jn.get(this)}},Ft=class extends Te{_getNamespace(){return"NullTypes"}},Lt=class extends Ft{};Wn(Lt,"DbNull");var $t=class extends Ft{};Wn($t,"JsonNull");var qt=class extends Ft{};Wn(qt,"AnyNull");var Mr={classes:{DbNull:Lt,JsonNull:$t,AnyNull:qt},instances:{DbNull:new Lt(Nr),JsonNull:new $t(Nr),AnyNull:new qt(Nr)}};function Wn(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var Yo=": ",Fr=class{constructor(t,r){this.name=t;this.value=r;d(this,"hasError",!1)}markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+Yo.length}write(t){let r=new ge(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(Yo).write(this.value)}};var Hn=class{constructor(t){d(this,"arguments");d(this,"errorMessages",[]);this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function ot(e){return new Hn(Zo(e))}function Zo(e){let t=new nt;for(let[r,n]of Object.entries(e)){let i=new Fr(r,Xo(n));t.addField(i)}return t}function Xo(e){if(typeof e=="string")return new V(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new V(String(e));if(typeof e=="bigint")return new V(`${e}n`);if(e===null)return new V("null");if(e===void 0)return new V("undefined");if(Xe(e))return new V(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return Buffer.isBuffer(e)?new V(`Buffer.alloc(${e.byteLength})`):new V(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=Cr(e)?e.toISOString():"Invalid Date";return new V(`new Date("${t}")`)}return e instanceof Te?new V(`Prisma.${e._getName()}`):it(e)?new V(`prisma.${zo(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?Fc(e):typeof e=="object"?Zo(e):new V(Object.prototype.toString.call(e))}function Fc(e){let t=new rt;for(let r of e)t.addItem(Xo(r));return t}function Lr(e,t){let r=t==="pretty"?Bo:Dr,n=e.renderAllMessages(r),i=new et(0,{colors:r}).write(e).toString();return{message:n,args:i}}function $r({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=ot(e);for(let p of t)kr(p,a,s);let{message:l,args:u}=Lr(a,r),c=Sr({message:l,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:u});throw new J(c,{clientVersion:o})}var he=class{constructor(){d(this,"_map",new Map)}get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};function Vt(e){let t;return{get(){return t||(t={value:e()}),t.value}}}function ye(e){return e.replace(/^./,t=>t.toLowerCase())}function ts(e,t,r){let n=ye(r);return!t.result||!(t.result.$allModels||t.result[n])?e:Lc({...e,...es(t.name,e,t.result.$allModels),...es(t.name,e,t.result[n])})}function Lc(e){let t=new he,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return We(e,n=>({...n,needs:r(n.name,new Set)}))}function es(e,t,r){return r?We(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:$c(t,o,i)})):{}}function $c(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function rs(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function ns(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var qr=class{constructor(t,r){this.extension=t;this.previous=r;d(this,"computedFieldsCache",new he);d(this,"modelExtensionsCache",new he);d(this,"queryCallbacksCache",new he);d(this,"clientExtensions",Vt(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions()));d(this,"batchCallbacks",Vt(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t}))}getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>ts(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=ye(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},st=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new qr(t))}isEmpty(){return this.head===void 0}append(t){return new e(new qr(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};var Vr=class{constructor(t){this.name=t}};function is(e){return e instanceof Vr}function os(e){return new Vr(e)}var ss=Symbol(),jt=class{constructor(t){if(t!==ss)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?jr:t}},jr=new jt(ss);function we(e){return e instanceof jt}var qc={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},as="explicitly `undefined` values are not allowed";function Ur({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=st.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:l,previewFeatures:u,globalOmit:c}){let p=new Kn({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:l,previewFeatures:u,globalOmit:c});return{modelName:e,action:qc[t],query:Ut(r,p)}}function Ut({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:us(r,n),selection:Vc(e,t,i,n)}}function Vc(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),Qc(e,n)):jc(n,t,r)}function jc(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&Uc(n,t,e),Bc(n,r,e),n}function Uc(e,t,r){for(let[n,i]of Object.entries(t)){if(we(i))continue;let o=r.nestSelection(n);if(zn(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=Ut(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=Ut(i,o)}}function Bc(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=ns(i,n);for(let[s,a]of Object.entries(o)){if(we(a))continue;zn(a,r.nestSelection(s));let l=r.findField(s);n?.[s]&&!l||(e[s]=!a)}}function Qc(e,t){let r={},n=t.getComputedFields(),i=rs(e,n);for(let[o,s]of Object.entries(i)){if(we(s))continue;let a=t.nestSelection(o);zn(s,a);let l=t.findField(o);if(!(n?.[o]&&!l)){if(s===!1||s===void 0||we(s)){r[o]=!1;continue}if(s===!0){l?.kind==="object"?r[o]=Ut({},a):r[o]=!0;continue}r[o]=Ut(s,a)}}return r}function ls(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(Ze(e)){if(Cr(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(is(e))return{$type:"Param",value:e.name};if(it(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return Gc(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:Buffer.from(r,n,i).toString("base64")}}if(Jc(e))return e.values;if(Xe(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Te){if(e!==Mr.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(Wc(e))return e.toJSON();if(typeof e=="object")return us(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function us(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);we(i)||(i!==void 0?r[n]=ls(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:as}))}return r}function Gc(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||we(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(ls(o,i))}return r}function Jc(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function Wc(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function zn(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:as})}var Kn=class e{constructor(t){this.params=t;d(this,"modelOrType");this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}throwValidationError(t){$r({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[Ye(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:ae(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function cs(e){if(!e._hasPreviewFlag("metrics"))throw new J("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var at=class{constructor(t){d(this,"_client");this._client=t}prometheus(t){return cs(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return cs(this._client),this._client._engine.metrics({format:"json",...t})}};function ps(e){return{models:Yn(e.models),enums:Yn(e.enums),types:Yn(e.types)}}function Yn(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}function ds(e,t){let r=Vt(()=>Hc(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function Hc(e){return{datamodel:{models:Zn(e.models),enums:Zn(e.enums),types:Zn(e.types)}}}function Zn(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}var Xn=new WeakMap,Br="$$PrismaTypedSql",Bt=class{constructor(t,r){Xn.set(this,{sql:t,values:r}),Object.defineProperty(this,Br,{value:Br})}get sql(){return Xn.get(this).sql}get values(){return Xn.get(this).values}};function ms(e){return(...t)=>new Bt(e,t)}function Qr(e){return e!=null&&e[Br]===Br}var Ha=H(fs());var Ka=require("async_hooks"),za=require("events"),Ya=H(require("fs")),ur=H(require("path"));var ee=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let l=0;for(;l<s.values.length;)this.values[o++]=s.values[l++],this.strings[o]=s.strings[l];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function gs(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new ee([r,...Array(e.length-1).fill(t),n],e)}function ei(e){return new ee([e],[])}var hs=ei("");function ti(e,...t){return new ee(e,t)}function Qt(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}function z(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}function $e(e){let t=new he;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}var Gr={enumerable:!0,configurable:!0,writable:!0};function Jr(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>Gr,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var ys=Symbol.for("nodejs.util.inspect.custom");function pe(e,t){let r=zc(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=ws(Reflect.ownKeys(o),r),a=ws(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let l=r.get(s);return l?l.getPropertyDescriptor?{...Gr,...l?.getPropertyDescriptor(s)}:Gr:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[ys]=function(){let o={...this};return delete o[ys],o},i}function zc(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function ws(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}function lt(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}function Wr(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function Es(e){if(e===void 0)return"";let t=ot(e);return new et(0,{colors:Dr}).write(t).toString()}var Yc="P2037";function Hr({error:e,user_facing_error:t},r,n){return t.error_code?new Q(Zc(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new G(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function Zc(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===Yc&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}var Gt="<unknown>";function bs(e){var t=e.split(`
`);return t.reduce(function(r,n){var i=tp(n)||np(n)||sp(n)||cp(n)||lp(n);return i&&r.push(i),r},[])}var Xc=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,ep=/\((\S*)(?::(\d+))(?::(\d+))\)/;function tp(e){var t=Xc.exec(e);if(!t)return null;var r=t[2]&&t[2].indexOf("native")===0,n=t[2]&&t[2].indexOf("eval")===0,i=ep.exec(t[2]);return n&&i!=null&&(t[2]=i[1],t[3]=i[2],t[4]=i[3]),{file:r?null:t[2],methodName:t[1]||Gt,arguments:r?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}var rp=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;function np(e){var t=rp.exec(e);return t?{file:t[2],methodName:t[1]||Gt,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var ip=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,op=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function sp(e){var t=ip.exec(e);if(!t)return null;var r=t[3]&&t[3].indexOf(" > eval")>-1,n=op.exec(t[3]);return r&&n!=null&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||Gt,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}var ap=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i;function lp(e){var t=ap.exec(e);return t?{file:t[3],methodName:t[1]||Gt,arguments:[],lineNumber:+t[4],column:t[5]?+t[5]:null}:null}var up=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i;function cp(e){var t=up.exec(e);return t?{file:t[2],methodName:t[1]||Gt,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var ri=class{getLocation(){return null}},ni=class{constructor(){d(this,"_error");this._error=new Error}getLocation(){let t=this._error.stack;if(!t)return null;let n=bs(t).find(i=>{if(!i.file)return!1;let o=Nn(i.file);return o!=="<anonymous>"&&!o.includes("@prisma")&&!o.includes("/packages/client/src/runtime/")&&!o.endsWith("/runtime/binary.js")&&!o.endsWith("/runtime/library.js")&&!o.endsWith("/runtime/edge.js")&&!o.endsWith("/runtime/edge-esm.js")&&!o.startsWith("internal/")&&!i.methodName.includes("new ")&&!i.methodName.includes("getCallSite")&&!i.methodName.includes("Proxy.")&&i.methodName.split(".").length<4});return!n||!n.file?null:{fileName:n.file,lineNumber:n.lineNumber,columnNumber:n.column}}};function Ie(e){return e==="minimal"?typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new ri:new ni}var xs={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function ut(e={}){let t=dp(e);return Object.entries(t).reduce((n,[i,o])=>(xs[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function dp(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function Kr(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function vs(e,t){let r=Kr(e);return t({action:"aggregate",unpacker:r,argsMapper:ut})(e)}function mp(e={}){let{select:t,...r}=e;return typeof t=="object"?ut({...r,_count:t}):ut({...r,_count:{_all:!0}})}function fp(e={}){return typeof e.select=="object"?t=>Kr(e)(t)._count:t=>Kr(e)(t)._count._all}function Ps(e,t){return t({action:"count",unpacker:fp(e),argsMapper:mp})(e)}function gp(e={}){let t=ut(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function hp(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function Ts(e,t){return t({action:"groupBy",unpacker:hp(e),argsMapper:gp})(e)}function Cs(e,t,r){if(t==="aggregate")return n=>vs(n,r);if(t==="count")return n=>Ps(n,r);if(t==="groupBy")return n=>Ts(n,r)}function As(e,t){let r=t.fields.filter(i=>!i.relationName),n=$n(r,i=>i.name);return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new Mt(e,o,s.type,s.isList,s.kind==="enum")},...Jr(Object.keys(n))})}var Rs=e=>Array.isArray(e)?e:e.split("."),ii=(e,t)=>Rs(t).reduce((r,n)=>r&&r[n],e),Ss=(e,t,r)=>Rs(t).reduceRight((n,i,o,s)=>Object.assign({},ii(e,s.slice(0,o)),{[i]:n}),r);function yp(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function wp(e,t,r){return t===void 0?e??{}:Ss(t,r,e||!0)}function oi(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((l,u)=>({...l,[u.name]:u}),{});return l=>{let u=Ie(e._errorFormat),c=yp(n,i),p=wp(l,o,c),m=r({dataPath:c,callsite:u})(p),g=Ep(e,t);return new Proxy(m,{get(h,y){if(!g.includes(y))return h[y];let P=[a[y].type,r,y],C=[c,p];return oi(e,...P,...C)},...Jr([...g,...Object.getOwnPropertyNames(m)])})}}function Ep(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var bp=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],xp=["aggregate","count","groupBy"];function si(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[vp(e,t),Tp(e,t),Qt(r),z("name",()=>t),z("$name",()=>t),z("$parent",()=>e._appliedParent)];return pe({},n)}function vp(e,t){let r=ye(t),n=Object.keys(At.ModelAction).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>l=>{let u=Ie(e._errorFormat);return e._createPrismaPromise(c=>{let p={args:l,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:c,callsite:u};return e._request({...p,...a})},{action:o,args:l,model:t})};return bp.includes(o)?oi(e,t,s):Pp(i)?Cs(e,i,s):s({})}}}function Pp(e){return xp.includes(e)}function Tp(e,t){return $e(z("fields",()=>{let r=e._runtimeDataModel.models[t];return As(t,r)}))}function ks(e){return e.replace(/^./,t=>t.toUpperCase())}var ai=Symbol();function Jt(e){let t=[Cp(e),Ap(e),z(ai,()=>e),z("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(Qt(r)),pe(e,t)}function Cp(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function Ap(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(ye),n=[...new Set(t.concat(r))];return $e({getKeys(){return n},getPropertyValue(i){let o=ks(i);if(e._runtimeDataModel.models[o]!==void 0)return si(e,o);if(e._runtimeDataModel.models[i]!==void 0)return si(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function Os(e){return e[ai]?e[ai]:e}function Is(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return Jt(t)}function Ds({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let l of Object.values(o)){if(n){if(n[l.name])continue;let u=l.needs.filter(c=>n[c]);u.length>0&&a.push(lt(u))}else if(r){if(!r[l.name])continue;let u=l.needs.filter(c=>!r[c]);u.length>0&&a.push(lt(u))}Rp(e,l.needs)&&s.push(Sp(l,pe(e,s)))}return s.length>0||a.length>0?pe(e,[...s,...a]):e}function Rp(e,t){return t.every(r=>Ln(e,r))}function Sp(e,t){return $e(z(e.name,()=>e.compute(t)))}function zr({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=zr({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&_s({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&_s({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function _s({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||we(s))continue;let l=n.models[r].fields.find(c=>c.name===o);if(!l||l.kind!=="object"||!l.relationName)continue;let u=typeof s=="object"?s:{};t[o]=zr({visitor:i,result:t[o],args:u,modelName:l.type,runtimeDataModel:n})}}function Ns({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:zr({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,l,u)=>{let c=ye(l);return Ds({result:a,modelName:c,select:u.select,omit:u.select?void 0:{...o?.[c],...u.omit},extensions:n})}})}var kp=["$connect","$disconnect","$on","$transaction","$use","$extends"],Ms=kp;function Fs(e){if(e instanceof ee)return Op(e);if(Qr(e))return Ip(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=Wt(e[n]);return r}let t={};for(let r in e)t[r]=Wt(e[r]);return t}function Op(e){return new ee(e.strings,e.values)}function Ip(e){return new Bt(e.sql,e.values)}function Wt(e){if(typeof e!="object"||e==null||e instanceof Te||it(e))return e;if(Xe(e))return new fe(e.toFixed());if(Ze(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=Wt(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:Wt(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=Wt(e[r]);return t}ae(e,"Unknown value")}function $s(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Fs(t.args??{}),__internalParams:t,query:(s,a=t)=>{let l=a.customDataProxyFetch;return a.customDataProxyFetch=Us(o,l),a.args=s,$s(e,a,r,n+1)}})})}function qs(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return $s(e,t,s)}function Vs(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?js(r,n,0,e):e(r)}}function js(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let l=a.customDataProxyFetch;return a.customDataProxyFetch=Us(i,l),js(a,t,r+1,n)}})}var Ls=e=>e;function Us(e=Ls,t=Ls){return r=>e(t(r))}var Bs=F("prisma:client"),Qs={Vercel:"vercel","Netlify CI":"netlify"};function Gs({postinstall:e,ciName:t,clientVersion:r}){if(Bs("checkPlatformCaching:postinstall",e),Bs("checkPlatformCaching:ciName",t),e===!0&&t&&t in Qs){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${Qs[t]}-build`;throw console.error(n),new k(n,r)}}function Js(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}var Dp=()=>globalThis.process?.release?.name==="node",_p=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,Np=()=>!!globalThis.Deno,Mp=()=>typeof globalThis.Netlify=="object",Fp=()=>typeof globalThis.EdgeRuntime=="object",Lp=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function $p(){return[[Mp,"netlify"],[Fp,"edge-light"],[Lp,"workerd"],[Np,"deno"],[_p,"bun"],[Dp,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var qp={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function Yr(){let e=$p();return{id:e,prettyName:qp[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var li=H(Fn());function Ws(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}function Hs(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}var Ks=H(go());function zs({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,Ks.default)({user:t,repo:r,template:n,title:e,body:i})}function Ys({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=Gi(6e3-(s?.length??0)),l=Hs((0,li.default)(a)),u=n?`# Description
\`\`\`
${n}
\`\`\``:"",c=(0,li.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${u}

## Logs
\`\`\`
${l}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?Ws(s):""}
\`\`\`
`),p=zs({title:r,body:c});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${Z(p)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}var Vp=Object.defineProperty,aa=e=>{throw TypeError(e)},jp=(e,t,r)=>t in e?Vp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,tn=(e,t,r)=>jp(e,typeof t!="symbol"?t+"":t,r),mi=(e,t,r)=>t.has(e)||aa("Cannot "+r),rn=(e,t,r)=>(mi(e,t,"read from private field"),r?r.call(e):t.get(e)),Zr=(e,t,r)=>t.has(e)?aa("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),ui=(e,t,r,n)=>(mi(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),Zs=(e,t,r)=>(mi(e,t,"access private method"),r);function la(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}var Xr="/* prisma-comma-repeatable-start */",ci="/* prisma-comma-repeatable-end */";function Up({query:e,params:t}){if(!e.includes(Xr))return{query:e,params:t};let r=[],n=1,i="",o=0,s=0,a=0;for(;o<e.length;){let l=e[o];if(s===1&&l!=='"'){i+=l,o++;continue}if(l==='"'){s===1?s=a:(a=s,s=1),i+=l,o++;continue}if(e.slice(o,o+Xr.length)===Xr){if(s===2)throw new Error("Nested repetition is not allowed");s=2,o+=Xr.length,i+="(";continue}if(e.slice(o,o+ci.length)===ci){if(s===0)throw new Error("Unmatched repetition end");s=0,o+=ci.length,i+=")";continue}if(l==="$"){let u=e.slice(o+1).match(/^\d+/);if(!u){i+="$",o++;continue}o+=u[0].length+1;let c=parseInt(u[0]),p=t[c-1];switch(s){case 0:{r.push(p),i+=`$${n++}`;break}case 2:{let m=Array.isArray(p)?p:[p];if(m.length===0){i+="NULL";break}m.forEach((g,h)=>{r.push(g),i+=`$${n++}`,h!==m.length-1&&(i+=", ")});break}default:throw new Error(`Unexpected state: ${s}`)}continue}i+=l,o++}return{query:i,params:r}}function Xs({query:e,params:t},r){let n=t.map(a=>{if(!la(a))return a;let l=r[a.prisma__value.name];if(l===void 0)throw new Error(`Missing value for query variable ${a.prisma__value.name}`);return l}),{query:i,params:o}=Up({query:e,params:n}),s=o.map(a=>Bp(a));return{sql:i,args:o,argTypes:s}}function Bp(e){return e===null?"Int32":typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":la(e)?Qp(e.prisma__value.type):"Json"}function Qp(e){let r={Any:"Json",String:"Text",Int:"Int32",BigInt:"Int64",Float:"Double",Boolean:"Boolean",Decimal:"Numeric",Date:"DateTime",Object:"Json",Bytes:"Bytes",Array:"Array"}[e];if(!r)throw new Error(`Unknown placeholder type: ${e}`);return r}function Gp(e){return e.rows.map(t=>t.reduce((r,n,i)=>{let o=e.columnNames[i].split("."),s=r;for(let a=0;a<o.length;a++){let l=o[a];a===o.length-1?s[l]=n:(s[l]===void 0&&(s[l]={}),s=s[l])}return r},{}))}var Kt,nn,sn,on,di,fi=class{constructor({queryable:e,placeholderValues:t,onQuery:r}){Zr(this,on),Zr(this,Kt),Zr(this,nn),Zr(this,sn),ui(this,Kt,e),ui(this,nn,t),ui(this,sn,r)}async run(e){return this.interpretNode(e,rn(this,nn))}async interpretNode(e,t){switch(e.type){case"seq":{let r=await Promise.all(e.args.map(n=>this.interpretNode(n,t)));return r[r.length-1]}case"get":return t[e.args.name];case"let":{let r=Object.create(t);return await Promise.all(e.args.bindings.map(async n=>{r[n.name]=await this.interpretNode(n.expr,t)})),this.interpretNode(e.args.expr,r)}case"getFirstNonEmpty":{for(let r of e.args.names){let n=t[r];if(!ea(n))return n}return[]}case"concat":return(await Promise.all(e.args.map(n=>this.interpretNode(n,t)))).reduce((n,i)=>n.concat(Jp(i)),[]);case"sum":return(await Promise.all(e.args.map(n=>this.interpretNode(n,t)))).reduce((n,i)=>ta(n)+ta(i));case"execute":{let r=Xs(e.args,t);return Zs(this,on,di).call(this,r,async()=>{let n=await rn(this,Kt).executeRaw(r);if(n.ok)return n.value;throw n.error})}case"query":{let r=Xs(e.args,t);return Zs(this,on,di).call(this,r,async()=>{let n=await rn(this,Kt).queryRaw(r);if(n.ok)return Gp(n.value);throw n.error})}case"reverse":{let r=await this.interpretNode(e.args,t);return Array.isArray(r)?r.reverse():r}case"unique":{let r=await this.interpretNode(e.args,t);if(!Array.isArray(r))return r;if(r.length>1)throw new Error(`Expected zero or one element, got ${r.length}`);return r[0]??null}case"required":{let r=await this.interpretNode(e.args,t);if(ea(r))throw new Error("Required value is empty");return r}case"mapField":{let r=await this.interpretNode(e.args.records,t);return ua(r,e.args.field)}case"join":{let r=await this.interpretNode(e.args.parent,t),n=await Promise.all(e.args.children.map(async i=>({joinExpr:i,childRecords:await this.interpretNode(i.child,t)})));if(Array.isArray(r)){for(let i of r)ra(an(i),n);return r}return ra(an(r),n)}default:throw new Error(`Unexpected node type: ${e.type}`)}}};Kt=new WeakMap;nn=new WeakMap;sn=new WeakMap;on=new WeakSet;di=async function(e,t){var r;let n=new Date,i=performance.now(),o=await t(),s=performance.now();return(r=rn(this,sn))==null||r.call(this,{timestamp:n,duration:s-i,query:e.sql,params:e.args}),o};function ea(e){return Array.isArray(e)?e.length===0:e==null}function Jp(e){return Array.isArray(e)?e:[e]}function ta(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function an(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function ua(e,t){return Array.isArray(e)?e.map(r=>ua(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function ra(e,t){for(let{joinExpr:r,childRecords:n}of t)e[r.parentField]=Wp(n,e,r);return e}function Wp(e,t,r){if(Array.isArray(e))return e.filter(n=>na(an(n),t,r));{let n=an(e);return na(n,t,r)?n:null}}function na(e,t,r){for(let[n,i]of r.on)if(t[n]!==e[i])return!1;return!0}function Hp(e,t){throw new Error(t)}var oe=class extends Error{constructor(e,t){super("Transaction API error: "+e),this.meta=t,tn(this,"code","P2028")}},en=class extends oe{constructor(e,t){super(`Error from Driver Adapter: ${e}`,{...t.driverAdapterError})}},ia=class extends oe{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},Kp=class extends oe{constructor(e){super(`Transaction already closed: A ${e} cannot be executed on a committed transaction`)}},zp=class extends oe{constructor(e){super(`Transaction already closed: A ${e} cannot be executed on a committed transaction`)}},Yp=class extends oe{constructor(){super("Unable to start a transaction in the given time.")}},Zp=class extends oe{constructor(e,{timeout:t,timeTaken:r}){super(`A ${e} cannot be executed on an expired transaction. The timeout for this transaction was ${t} ms, however ${r} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction`,{operation:e,timeout:t,timeTaken:r})}},pi=class extends oe{constructor(e){super(`Internal Consistency Error: ${e}`)}},oa=class extends oe{constructor(e){super(`Invalid isolation level: ${e}`,{isolationLevel:e})}},Xp=100,ed={ReadUncommitted:"READ UNCOMMITTED",ReadCommitted:"READ COMMITTED",RepeatableRead:"REPEATABLE READ",Snapshot:"SNAPSHOT",Serializable:"SERIALIZABLE"},Ht=F("prisma:client:transactionManager"),td=()=>({sql:"COMMIT",args:[],argTypes:[]}),rd=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),sa=e=>({sql:"SET TRANSACTION ISOLATION LEVEL "+ed[e],args:[],argTypes:[]}),ca=class{constructor({driverAdapter:e}){tn(this,"transactions",new Map),tn(this,"closedTransactions",[]),tn(this,"driverAdapter"),this.driverAdapter=e}async startTransaction(e){let t=this.validateOptions(e),r={id:globalThis.crypto.randomUUID(),status:"waiting",timer:void 0,timeout:t.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(r.id,r),r.timer=this.startTransactionTimeout(r.id,t.maxWait);let n=await this.driverAdapter.transactionContext();if(!n.ok)throw new en("Failed to start transaction.",{driverAdapterError:n.error});this.requiresSettingIsolationLevelFirst()&&t.isolationLevel&&await n.value.executeRaw(sa(t.isolationLevel));let i=await n.value.startTransaction();if(!i.ok)throw new en("Failed to start transaction.",{driverAdapterError:i.error});switch(i.value.options.usePhantomQuery||(await i.value.executeRaw({sql:"BEGIN",args:[],argTypes:[]}),!this.requiresSettingIsolationLevelFirst()&&t.isolationLevel&&await n.value.executeRaw(sa(t.isolationLevel))),r.status){case"waiting":return r.transaction=i.value,clearTimeout(r.timer),r.timer=void 0,r.status="running",r.timer=this.startTransactionTimeout(r.id,t.timeout),{id:r.id};case"timed_out":throw new Yp;case"running":case"committed":case"rolled_back":throw new pi(`Transaction in invalid state ${r.status} although it just finished startup.`);default:Hp(r.status,"Unknown transaction status.")}}async commitTransaction(e){let t=this.getActiveTransaction(e,"commit");await this.closeTransaction(t,"committed")}async rollbackTransaction(e){let t=this.getActiveTransaction(e,"rollback");await this.closeTransaction(t,"rolled_back")}getTransaction(e,t){let r=this.getActiveTransaction(e.id,t);if(!r.transaction)throw new ia;return r.transaction}getActiveTransaction(e,t){let r=this.transactions.get(e);if(!r){let n=this.closedTransactions.find(i=>i.id===e);if(n)switch(Ht("Transaction already closed.",{transactionId:e,status:n.status}),n.status){case"waiting":case"running":throw new pi("Active transaction found in closed transactions list.");case"committed":throw new Kp(t);case"rolled_back":throw new zp(t);case"timed_out":throw new Zp(t,{timeout:n.timeout,timeTaken:Date.now()-n.startedAt})}else throw Ht("Transaction not found.",e),new ia}if(["committed","rolled_back","timed_out"].includes(r.status))throw new pi("Closed transaction found in active transactions map.");return r}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(e=>this.closeTransaction(e,"rolled_back")))}startTransactionTimeout(e,t){let r=Date.now();return setTimeout(async()=>{Ht("Transaction timed out.",{transactionId:e,timeoutStartedAt:r,timeout:t});let n=this.transactions.get(e);n&&["running","waiting"].includes(n.status)?await this.closeTransaction(n,"timed_out"):Ht("Transaction already committed or rolled back when timeout happened.",e)},t)}async closeTransaction(e,t){if(Ht("Closing transaction.",{transactionId:e.id,status:t}),e.status=t,e.transaction&&t==="committed"){let r=await e.transaction.commit();if(!r.ok)throw new en("Failed to commit transaction.",{driverAdapterError:r.error});e.transaction.options.usePhantomQuery||await e.transaction.executeRaw(td())}else if(e.transaction){let r=await e.transaction.rollback();if(!r.ok)throw new en("Failed to rollback transaction.",{driverAdapterError:r.error});e.transaction.options.usePhantomQuery||await e.transaction.executeRaw(rd())}clearTimeout(e.timer),e.timer=void 0,this.transactions.delete(e.id),this.closedTransactions.push(e),this.closedTransactions.length>Xp&&this.closedTransactions.shift()}validateOptions(e){if(!e.timeout)throw new oe("timeout is required");if(!e.maxWait)throw new oe("maxWait is required");if(e.isolationLevel==="Snapshot")throw new oa(e.isolationLevel);if(this.driverAdapter.provider==="sqlite"&&e.isolationLevel&&e.isolationLevel!=="Serializable")throw new oa(e.isolationLevel);return{...e,timeout:e.timeout,maxWait:e.maxWait}}requiresSettingIsolationLevelFirst(){return this.driverAdapter.provider==="mysql"}};var gi,pa={async loadQueryCompiler(e){let{clientVersion:t,adapter:r,compilerWasm:n}=e;if(r===void 0)throw new k(`The \`adapter\` option for \`PrismaClient\` is required in this context (${Yr().prettyName})`,t);if(n===void 0)throw new k("WASM query compiler was unexpectedly `undefined`",t);return gi===void 0&&(gi=(async()=>{let i=n.getRuntime(),o=await n.getQueryCompilerWasmModule();if(o==null)throw new k("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let s={"./query_compiler_bg.js":i},a=new WebAssembly.Instance(o,s),l=a.exports.__wbindgen_start;return i.__wbg_set_wasm(a.exports),l(),i.QueryCompiler})()),await gi}};var da="P2038",ln=F("prisma:client:clientEngine"),ct,pt=class{constructor(t,r){d(this,"name","ClientEngine");d(this,"queryCompiler");d(this,"instantiateQueryCompilerPromise");d(this,"QueryCompilerConstructor");d(this,"queryCompilerLoader");d(this,"config");d(this,"datamodel");d(this,"driverAdapter");d(this,"transactionManager");d(this,"logEmitter");d(this,"logQueries");d(this,"logLevel");d(this,"lastStartedQuery");d(this,"tracingHelper");_i(this,ct);if(!t.previewFeatures?.includes("driverAdapters"))throw new k("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,da);let{adapter:n}=t;if(n)this.driverAdapter=n,ln("Using driver adapter: %O",n);else throw new k("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,da);this.queryCompilerLoader=r??pa,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&Ni(this,ct,i=>{this.logEmitter.emit("query",{...i,params:JSON.stringify(i.params),target:"ClientEngine"})}),this.transactionManager=new ca({driverAdapter:this.driverAdapter}),this.instantiateQueryCompilerPromise=this.instantiateQueryCompiler()}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async instantiateQueryCompiler(){if(!this.queryCompiler){this.QueryCompilerConstructor||(this.QueryCompilerConstructor=await this.queryCompilerLoader.loadQueryCompiler(this.config));try{this.queryCompiler=new this.QueryCompilerConstructor({datamodel:this.datamodel,provider:this.driverAdapter.provider,connectionInfo:{}})}catch(t){throw this.transformInitError(t)}}}transformInitError(t){try{let r=JSON.parse(t.message);return new k(r.message,this.config.clientVersion,r.error_code)}catch{return t}}transformRequestError(t){if(t instanceof k)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new le(nd(this,t.message),this.config.clientVersion);if(t instanceof oe)return new Q(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let r=JSON.parse(t);return new G(`${r.message}
${r.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.instantiateQueryCompilerPromise}async stop(){await this.instantiateQueryCompilerPromise,await this.transactionManager.cancelAllTransactions()}version(){return"unknown"}async transaction(t,r,n){let i;try{if(t==="start"){let o=n;i=await this.transactionManager.startTransaction(o)}else if(t==="commit"){let o=n;await this.transactionManager.commitTransaction(o.id)}else if(t==="rollback"){let o=n;await this.transactionManager.rollbackTransaction(o.id)}else ae(t,"Invalid transaction action.")}catch(o){throw this.transformRequestError(o)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{traceparent:r,interactiveTransaction:n}){ln("sending request");let i=JSON.stringify(t);this.lastStartedQuery=i;try{await this.start();let o=await this.queryCompiler.compile(i),s=JSON.parse(o);ln("query plan created",o);let a=n?this.transactionManager.getTransaction(n,t.action):this.driverAdapter,l={},c=await new fi({queryable:a,placeholderValues:l,onQuery:wn(this,ct)}).run(s);return ln("query plan executed"),{data:{[t.action]:c}}}catch(o){throw this.transformRequestError(o)}}async requestBatch(t,{transaction:r,traceparent:n}){this.lastStartedQuery=JSON.stringify(t);try{await this.start();let i=await Promise.all(t.map(async a=>{let l=JSON.stringify(a),u=await this.queryCompiler.compile(l);return{query:a,plan:JSON.parse(u)}})),o;if(r?.kind==="itx")o=r.options;else{let a=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;o=await this.transaction("start",{},a)}let s=[];for(let{query:a,plan:l}of i){let u=this.transactionManager.getTransaction(o,a.action),c=new fi({queryable:u,placeholderValues:{},onQuery:wn(this,ct)});s.push(await c.run(l))}return r?.kind!=="itx"&&await this.transaction("commit",{},o),s}catch(i){throw this.transformRequestError(i)}}metrics(t){throw new Error("Method not implemented.")}};ct=new WeakMap;function nd(e,t){return Ys({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:e.lastStartedQuery})}function dt({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw new k(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new k("error: Missing URL environment variable, value, or override.",n);return i}var un=class extends Error{constructor(r,n){super(r);d(this,"clientVersion");d(this,"cause");this.clientVersion=n.clientVersion,this.cause=n.cause}get[Symbol.toStringTag](){return this.name}};var te=class extends un{constructor(r,n){super(r,n);d(this,"isRetryable");this.isRetryable=n.isRetryable??!0}};function A(e,t){return{...e,isRetryable:t}}var mt=class extends te{constructor(r){super("This request must be retried",A(r,!0));d(this,"name","ForcedRetryError");d(this,"code","P5001")}};x(mt,"ForcedRetryError");var qe=class extends te{constructor(r,n){super(r,A(n,!1));d(this,"name","InvalidDatasourceError");d(this,"code","P6001")}};x(qe,"InvalidDatasourceError");var Ve=class extends te{constructor(r,n){super(r,A(n,!1));d(this,"name","NotImplementedYetError");d(this,"code","P5004")}};x(Ve,"NotImplementedYetError");var M=class extends te{constructor(r,n){super(r,n);d(this,"response");this.response=n.response;let i=this.response.headers.get("prisma-request-id");if(i){let o=`(The request id was: ${i})`;this.message=this.message+" "+o}}};var je=class extends M{constructor(r){super("Schema needs to be uploaded",A(r,!0));d(this,"name","SchemaMissingError");d(this,"code","P5005")}};x(je,"SchemaMissingError");var hi="This request could not be understood by the server",zt=class extends M{constructor(r,n,i){super(n||hi,A(r,!1));d(this,"name","BadRequestError");d(this,"code","P5000");i&&(this.code=i)}};x(zt,"BadRequestError");var Yt=class extends M{constructor(r,n){super("Engine not started: healthcheck timeout",A(r,!0));d(this,"name","HealthcheckTimeoutError");d(this,"code","P5013");d(this,"logs");this.logs=n}};x(Yt,"HealthcheckTimeoutError");var Zt=class extends M{constructor(r,n,i){super(n,A(r,!0));d(this,"name","EngineStartupError");d(this,"code","P5014");d(this,"logs");this.logs=i}};x(Zt,"EngineStartupError");var Xt=class extends M{constructor(r){super("Engine version is not supported",A(r,!1));d(this,"name","EngineVersionNotSupportedError");d(this,"code","P5012")}};x(Xt,"EngineVersionNotSupportedError");var yi="Request timed out",er=class extends M{constructor(r,n=yi){super(n,A(r,!1));d(this,"name","GatewayTimeoutError");d(this,"code","P5009")}};x(er,"GatewayTimeoutError");var id="Interactive transaction error",tr=class extends M{constructor(r,n=id){super(n,A(r,!1));d(this,"name","InteractiveTransactionError");d(this,"code","P5015")}};x(tr,"InteractiveTransactionError");var od="Request parameters are invalid",rr=class extends M{constructor(r,n=od){super(n,A(r,!1));d(this,"name","InvalidRequestError");d(this,"code","P5011")}};x(rr,"InvalidRequestError");var wi="Requested resource does not exist",nr=class extends M{constructor(r,n=wi){super(n,A(r,!1));d(this,"name","NotFoundError");d(this,"code","P5003")}};x(nr,"NotFoundError");var Ei="Unknown server error",ft=class extends M{constructor(r,n,i){super(n||Ei,A(r,!0));d(this,"name","ServerError");d(this,"code","P5006");d(this,"logs");this.logs=i}};x(ft,"ServerError");var bi="Unauthorized, check your connection string",ir=class extends M{constructor(r,n=bi){super(n,A(r,!1));d(this,"name","UnauthorizedError");d(this,"code","P5007")}};x(ir,"UnauthorizedError");var xi="Usage exceeded, retry again later",or=class extends M{constructor(r,n=xi){super(n,A(r,!0));d(this,"name","UsageExceededError");d(this,"code","P5008")}};x(or,"UsageExceededError");async function sd(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function sr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await sd(e);if(n.type==="QueryEngineError")throw new Q(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new ft(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new je(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Xt(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Zt(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new k(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new Yt(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new tr(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new rr(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new ir(r,gt(bi,n));if(e.status===404)return new nr(r,gt(wi,n));if(e.status===429)throw new or(r,gt(xi,n));if(e.status===504)throw new er(r,gt(yi,n));if(e.status>=500)throw new ft(r,gt(Ei,n));if(e.status>=400)throw new zt(r,gt(hi,n))}function gt(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}function ma(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}var Ce="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function fa(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,l,u,c;for(let p=0;p<o;p=p+3)c=t[p]<<16|t[p+1]<<8|t[p+2],s=(c&16515072)>>18,a=(c&258048)>>12,l=(c&4032)>>6,u=c&63,r+=Ce[s]+Ce[a]+Ce[l]+Ce[u];return i==1?(c=t[o],s=(c&252)>>2,a=(c&3)<<4,r+=Ce[s]+Ce[a]+"=="):i==2&&(c=t[o]<<8|t[o+1],s=(c&64512)>>10,a=(c&1008)>>4,l=(c&15)<<2,r+=Ce[s]+Ce[a]+Ce[l]+"="),r}function ga(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new k("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}function ad(e){return e[0]*1e3+e[1]/1e6}function vi(e){return new Date(ad(e))}var ha={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.5.0-73.173f8d54f8d52e692c7e27e72a88314ec7aeff60","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};var ar=class extends te{constructor(r,n){super(`Cannot fetch data from service:
${r}`,A(n,!0));d(this,"name","RequestError");d(this,"code","P5010")}};x(ar,"RequestError");async function Ue(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new ar(a,{clientVersion:n,cause:s})}}var ud=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,ya=F("prisma:client:dataproxyEngine");async function cd(e,t){let r=ha["@prisma/engines-version"],n=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&ud.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){if(e.startsWith("localhost")||e.startsWith("127.0.0.1"))return"0.0.0";let[s]=r.split("-")??[],[a,l,u]=s.split("."),c=pd(`<=${a}.${l}.${u}`),p=await Ue(c,{clientVersion:n});if(!p.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${p.status} ${p.statusText}, response body: ${await p.text()||"<empty body>"}`);let m=await p.text();ya("length of body fetched from unpkg.com",m.length);let g;try{g=JSON.parse(m)}catch(h){throw console.error("JSON.parse error: body fetched from unpkg.com: ",m),h}return g.version}throw new Ve("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function wa(e,t){let r=await cd(e,t);return ya("version",r),r}function pd(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var Ea=3,cn=F("prisma:client:dataproxyEngine"),Pi=class{constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){d(this,"apiKey");d(this,"tracingHelper");d(this,"logLevel");d(this,"logQueries");d(this,"engineHash");this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,interactiveTransaction:r}={}){let n={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-transaction-id"]=r.id);let i=this.buildCaptureSettings();return i.length>0&&(n["X-capture-telemetry"]=i.join(", ")),n}buildCaptureSettings(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}},lr=class{constructor(t){d(this,"name","DataProxyEngine");d(this,"inlineSchema");d(this,"inlineSchemaHash");d(this,"inlineDatasources");d(this,"config");d(this,"logEmitter");d(this,"env");d(this,"clientVersion");d(this,"engineHash");d(this,"tracingHelper");d(this,"remoteClientVersion");d(this,"host");d(this,"headerBuilder");d(this,"startPromise");ga(t),this.config=t,this.env={...t.env,...typeof process<"u"?process.env:{}},this.inlineSchema=fa(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let[t,r]=this.extractHostAndApiKey();this.host=t,this.headerBuilder=new Pi({apiKey:r,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await wa(t,this.config),cn("host",this.host)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":cn(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:vi(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:vi(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`https://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await Ue(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||cn("schema response status",r.status);let n=await sr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=Wr(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(l=>(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l?this.convertProtocolErrorsToClientError(l.errors):l))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await Ue(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,interactiveTransaction:i}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||cn("graphql response status",a.status),await this.handleError(await sr(a,this.clientVersion));let l=await a.json();if(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l)throw this.convertProtocolErrorsToClientError(l.errors);return"batchResult"in l?l.batchResult:l}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let l=await Ue(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await sr(l,this.clientVersion));let u=await l.json(),{extensions:c}=u;c&&this.propagateResponseExtensions(c);let p=u.id,m=u["data-proxy"].endpoint;return{id:p,payload:{endpoint:m}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await Ue(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await sr(a,this.clientVersion));let l=await a.json(),{extensions:u}=l;u&&this.propagateResponseExtensions(u);return}}})}extractHostAndApiKey(){let t={clientVersion:this.clientVersion},r=Object.keys(this.inlineDatasources)[0],n=dt({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),i;try{i=new URL(n)}catch{throw new qe(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,host:s,searchParams:a}=i;if(o!=="prisma:"&&o!==gr)throw new qe(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t);let l=a.get("api_key");if(l===null||l.length<1)throw new qe(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);return[s,l]}metrics(){throw new Ve("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof te)||!i.isRetryable)throw i;if(r>=Ea)throw i instanceof mt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${Ea} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await ma(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof je)throw await this.uploadSchema(),new mt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?Hr(t[0],this.config.clientVersion,this.config.activeProvider):new G(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};function ba({copyEngine:e=!0},t){let r;try{r=dt({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let n=!!(r?.startsWith("prisma://")||_n(r));e&&n&&Ot("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)");let i=Je(t.generator),o=n||!e,s=!!t.adapter,a=i==="library",l=i==="binary",u=i==="client";if(o&&s||s&&!1){let c;throw e?r?.startsWith("prisma://")?c=["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:c=["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:c=["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."],new J(c.join(`
`),{clientVersion:t.clientVersion})}return o?new lr(t):u?new pt(t):new pt(t)}function pn({generator:e}){return e?.previewFeatures??[]}var xa=e=>({command:e});var va=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);function ht(e){try{return Pa(e,"fast")}catch{return Pa(e,"slow")}}function Pa(e,t){return JSON.stringify(e.map(r=>Ca(r,t)))}function Ca(e,t){if(Array.isArray(e))return e.map(r=>Ca(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(Ze(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(fe.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(Buffer.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(dd(e))return{prisma__type:"bytes",prisma__value:Buffer.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:Buffer.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?Aa(e):e}function dd(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Aa(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(Ta);let t={};for(let r of Object.keys(e))t[r]=Ta(e[r]);return t}function Ta(e){return typeof e=="bigint"?e.toString():Aa(e)}var md=/^(\s*alter\s)/i,Ra=F("prisma:client");function Ti(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&md.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var Ci=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(Qr(r))n=r.sql,i={values:ht(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:ht(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:ht(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:ht(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=va(r),i={values:ht(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?Ra(`prisma.${e}(${n}, ${i.values})`):Ra(`prisma.${e}(${n})`),{query:n,parameters:i}},Sa={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new ee(t,r)}},ka={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};function Ai(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=Oa(r(s)):Oa(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function Oa(e){return typeof e.then=="function"?e:Promise.resolve(e)}var fd=Rn.split(".")[0],gd={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},Ri=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${fd}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??gd}};function Ia(){return new Ri}function Da(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}function _a(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}var dn=class{constructor(){d(this,"_middlewares",[])}use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};var Ma=H(Fn());function mn(e){return typeof e.batchRequestIdx=="number"}function Na(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(Si(e.query.arguments)),t.push(Si(e.query.selection)),t.join("")}function Si(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${Si(n)})`:r}).join(" ")})`}var hd={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function ki(e){return hd[e]}var fn=class{constructor(t){this.options=t;d(this,"batches");d(this,"tickActive",!1);this.batches={}}request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function Be(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new fe(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>Be("bigint",r));case"bytes-array":return t.map(r=>Be("bytes",r));case"decimal-array":return t.map(r=>Be("decimal",r));case"datetime-array":return t.map(r=>Be("datetime",r));case"date-array":return t.map(r=>Be("date",r));case"time-array":return t.map(r=>Be("time",r));default:return t}}function gn(e){let t=[],r=yd(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=Be(e.types[s],i[s]);t.push(o)}return t}function yd(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var wd=F("prisma:client:request_handler"),hn=class{constructor(t,r){d(this,"client");d(this,"dataloader");d(this,"logEmitter");this.logEmitter=r,this.client=t,this.dataloader=new fn({batchLoader:Vs(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(p=>p.protocolQuery),l=this.client._tracingHelper.getTraceParent(s),u=n.some(p=>ki(p.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:l,transaction:Ed(o),containsWrite:u,customDataProxyFetch:i})).map((p,m)=>{if(p instanceof Error)return p;try{return this.mapQueryEngineResult(n[m],p)}catch(g){return g}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Fa(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:ki(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:Na(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return process.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(wd(t),bd(t,i))throw t;if(t instanceof Q&&xd(t)){let u=La(t.meta);$r({args:o,errors:[u],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let l=t.message;if(n&&(l=Sr({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:l})),l=this.sanitizeMessage(l),t.code){let u=s?{modelName:s,...t.meta}:t.meta;throw new Q(l,{code:t.code,clientVersion:this.client._clientVersion,meta:u,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new le(l,this.client._clientVersion);if(t instanceof G)throw new G(l,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof k)throw new k(l,this.client._clientVersion);if(t instanceof le)throw new le(l,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,Ma.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(u=>u!=="select"&&u!=="include"),a=ii(o,s),l=i==="queryRaw"?gn(a):ze(a);return n?n(l):l}get[Symbol.toStringTag](){return"RequestHandler"}};function Ed(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Fa(e)};ae(e,"Unknown transaction kind")}}function Fa(e){return{id:e.id,payload:e.payload}}function bd(e,t){return mn(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function xd(e){return e.code==="P2009"||e.code==="P2012"}function La(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(La)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}var $a="6.5.0";var qa=$a;var Qa=H(Gn());var I=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};x(I,"PrismaClientConstructorValidationError");var Va=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],ja=["pretty","colorless","minimal"],Ua=["info","query","warn","error"],Pd={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new I(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=yt(r,t)||` Available datasources: ${t.join(", ")}`;throw new I(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new I(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new I(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new I(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&Je(t.generator)==="client")throw new I('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new I('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!pn(t).includes("driverAdapters"))throw new I('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(Je(t.generator)==="binary")throw new I('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new I(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new I(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!ja.includes(e)){let t=yt(e,ja);throw new I(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new I(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!Ua.includes(r)){let n=yt(r,Ua);throw new I(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=yt(i,o);throw new I(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new I(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new I(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new I(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new I('"omit" option is expected to be an object.');if(e===null)throw new I('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=Cd(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let l=o.fields.find(u=>u.name===s);if(!l){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(l.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new I(Ad(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new I(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=yt(r,t);throw new I(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function Ga(e,t){for(let[r,n]of Object.entries(e)){if(!Va.includes(r)){let i=yt(r,Va);throw new I(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}Pd[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new I('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function yt(e,t){if(t.length===0||typeof e!="string")return"";let r=Td(e,t);return r?` Did you mean "${r}"?`:""}function Td(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,Qa.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function Cd(e,t){return Ba(t.models,e)??Ba(t.types,e)}function Ba(e,t){let r=Object.keys(e).find(n=>Ye(n)===t);if(r)return e[r]}function Ad(e,t){let r=ot(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=Lr(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}function Ja(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},l=u=>{o||(o=!0,r(u))};for(let u=0;u<e.length;u++)e[u].then(c=>{n[u]=c,a()},c=>{if(!mn(c)){l(c);return}c.batchRequestIdx===u?l(c):(i||(i=c),a())})})}var De=F("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var Rd={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},Sd=Symbol.for("prisma.client.transaction.id"),kd={id:0,nextId(){return++this.id}};function Za(e){class t{constructor(n){d(this,"_originalClient",this);d(this,"_runtimeDataModel");d(this,"_requestHandler");d(this,"_connectionPromise");d(this,"_disconnectionPromise");d(this,"_engineConfig");d(this,"_accelerateEngineConfig");d(this,"_clientVersion");d(this,"_errorFormat");d(this,"_tracingHelper");d(this,"_middlewares",new dn);d(this,"_previewFeatures");d(this,"_activeProvider");d(this,"_globalOmit");d(this,"_extensions");d(this,"_engine");d(this,"_appliedParent");d(this,"_createPrismaPromise",Ai());d(this,"$metrics",new at(this));d(this,"$extends",Is);e=n?.__internal?.configOverride?.(e)??e,Gs(e),n&&Ga(n,e);let i=new za.EventEmitter().on("error",()=>{});this._extensions=st.empty(),this._previewFeatures=pn(e),this._clientVersion=e.clientVersion??qa,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=Ia();let o={rootEnvPath:e.relativeEnvPaths.rootEnvPath&&ur.default.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&ur.default.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=Pn(n.adapter);let l=e.activeProvider==="postgresql"?"postgres":e.activeProvider;if(s.provider!==l)throw new k(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${l}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new k("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=!s&&Ct(o,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{let l=n??{},u=l.__internal??{},c=u.debug===!0;c&&F.enable("prisma:client");let p=ur.default.resolve(e.dirname,e.relativePath);Ya.default.existsSync(p)||(p=e.dirname),De("dirname",e.dirname),De("relativePath",e.relativePath),De("cwd",p);let m=u.engine||{};if(l.errorFormat?this._errorFormat=l.errorFormat:process.env.NODE_ENV==="production"?this._errorFormat="minimal":process.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:p,dirname:e.dirname,enableDebugLogs:c,allowTriggerPanic:m.allowTriggerPanic,datamodelPath:ur.default.join(e.dirname,e.filename??"schema.prisma"),prismaPath:m.binaryPath??void 0,engineEndpoint:m.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:l.log&&_a(l.log),logQueries:l.log&&!!(typeof l.log=="string"?l.log==="query":l.log.find(g=>typeof g=="string"?g==="query":g.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:Js(l,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:l.transactionOptions?.maxWait??2e3,timeout:l.transactionOptions?.timeout??5e3,isolationLevel:l.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:dt,getBatchRequestPayload:Wr,prismaGraphQLToJSError:Hr,PrismaClientUnknownRequestError:G,PrismaClientInitializationError:k,PrismaClientKnownRequestError:Q,debug:F("prisma:client:accelerateEngine"),engineVersion:Ha.version,clientVersion:e.clientVersion}},De("clientVersion",e.clientVersion),this._engine=ba(e,this._engineConfig),this._requestHandler=new hn(this,i),l.log)for(let g of l.log){let h=typeof g=="string"?g:g.emit==="stdout"?g.level:null;h&&this.$on(h,y=>{kt.log(`${kt.tags[h]??""}`,y.message||y.query)})}}catch(l){throw l.clientVersion=this._clientVersion,l}return this._appliedParent=Jt(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{Ji()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:Ci({clientMethod:i,activeProvider:a}),callsite:Ie(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=Wa(n,i);return Ti(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new J("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(Ti(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new J(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:xa,callsite:Ie(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:Ci({clientMethod:i,activeProvider:a}),callsite:Ie(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...Wa(n,i));throw new J("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new J("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=kd.nextId(),s=Da(n.length),a=n.map((l,u)=>{if(l?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let c=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,p={kind:"batch",id:o,index:u,isolationLevel:c,lock:s};return l.requestTransaction?.(p)??l});return Ja(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),l;try{let u={kind:"itx",...a};l=await n(this._createItxClient(u)),await this._engine.transaction("commit",o,a)}catch(u){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),u}return l}_createItxClient(n){return pe(Jt(pe(Os(this),[z("_appliedParent",()=>this._appliedParent._createItxClient(n)),z("_createPrismaPromise",()=>Ai(n)),z(Sd,()=>n.id)])),[lt(Ms)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??Rd,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=-1,l=async u=>{let c=this._middlewares.get(++a);if(c)return this._tracingHelper.runInChildSpan(s.middleware,S=>c(u,P=>(S?.end(),l(P))));let{runInTransaction:p,args:m,...g}=u,h={...n,...g};m&&(h.args=i.middlewareArgsToRequestArgs(m)),n.transaction!==void 0&&p===!1&&delete h.transaction;let y=await qs(this,h);return h.model?Ns({result:y,modelName:h.model,args:h.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):y};return this._tracingHelper.runInChildSpan(s.operation,()=>new Ka.AsyncResource("prisma-client-request").runInAsyncScope(()=>l(o)))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:l,argsMapper:u,transaction:c,unpacker:p,otelParentCtx:m,customDataProxyFetch:g}){try{n=u?u(n):n;let h={name:"serialize"},y=this._tracingHelper.runInChildSpan(h,()=>Ur({modelName:l,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return F.enabled("prisma:client")&&(De("Prisma Client call:"),De(`prisma.${i}(${Es(n)})`),De("Generated request:"),De(JSON.stringify(y,null,2)+`
`)),c?.kind==="batch"&&await c.lock,this._requestHandler.request({protocolQuery:y,modelName:l,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:c,unpacker:p,otelParentCtx:m,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:g})}catch(h){throw h.clientVersion=this._clientVersion,h}}_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}}return t}function Wa(e,t){return Od(e)?[new ee(e,t),Sa]:[e,ka]}function Od(e){return Array.isArray(e)&&Array.isArray(e.raw)}var Id=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Xa(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!Id.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}function el(e){Ct(e,{conflictCheck:"warn"})}0&&(module.exports={Debug,Decimal,Extensions,MetricsClient,PrismaClientInitializationError,PrismaClientKnownRequestError,PrismaClientRustPanicError,PrismaClientUnknownRequestError,PrismaClientValidationError,Public,Sql,createParam,defineDmmfProperty,deserializeJsonResponse,deserializeRawResult,dmmfToRuntimeDataModel,empty,getPrismaClient,getRuntime,join,makeStrictEnum,makeTypedQueryFactory,objectEnumValues,raw,serializeJsonQuery,skip,sqltag,warnEnvConflicts,warnOnce});
/*! Bundled license information:

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.5.0
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/
//# sourceMappingURL=client.js.map
