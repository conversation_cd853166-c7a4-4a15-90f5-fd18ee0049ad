"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/studio/image-upload.tsx":
/*!************************************************!*\
  !*** ./src/components/studio/image-upload.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageUpload: function() { return /* binding */ ImageUpload; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ ImageUpload auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ImageUpload() {\n    var _session_user_subscription, _session_user, _session, _session_user_subscription1, _session_user1, _session1, _session_user_subscription2, _session_user2, _session2, _session_user_subscription3, _session_user3, _session3, _session_user_subscription4, _session_user4, _session4, _session_user_subscription5, _session_user5, _session5, _session_user_subscription6, _session_user6, _session6, _session_user_subscription7, _session_user7, _session7, _session_user_subscription8, _session_user8, _session8, _session_user_subscription9, _session_user9, _session9, _session_user_subscription10, _session_user10, _session10, _session_user_subscription11, _session_user11, _session11;\n    _s();\n    const { setImage } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)();\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const handleImageUpload = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        // Process the image directly without any authentication or subscription checks\n        processImage(file);\n    }, [\n        setImage\n    ]);\n    const processImage = (file)=>{\n        setIsUploading(true);\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            var _e_target;\n            const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            if (result) {\n                setImage(result);\n            }\n            setIsUploading(false);\n        };\n        reader.readAsDataURL(file);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 flex items-center justify-center overflow-hidden bg-black text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl md:text-4xl font-bold mb-6\",\n                    children: \"Welcome to Image-Text Studio\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 mx-auto max-w-md mb-8\",\n                    children: \"Upload an image to get started with our advanced text overlay tools.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mx-auto flex justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"file\",\n                            accept: \"image/*\",\n                            onChange: handleImageUpload,\n                            className: \"absolute inset-0 cursor-pointer opacity-0 w-full h-full z-10\",\n                            title: \"Upload an image\",\n                            disabled: isUploading || ((_session = session) === null || _session === void 0 ? void 0 : (_session_user = _session.user) === null || _session_user === void 0 ? void 0 : (_session_user_subscription = _session_user.subscription) === null || _session_user_subscription === void 0 ? void 0 : _session_user_subscription.plan_type) === \"free\" && ((_session1 = session) === null || _session1 === void 0 ? void 0 : (_session_user1 = _session1.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_subscription1 = _session_user1.subscription) === null || _session_user_subscription1 === void 0 ? void 0 : _session_user_subscription1.free_edits_remaining) === 0 || ((_session2 = session) === null || _session2 === void 0 ? void 0 : (_session_user2 = _session2.user) === null || _session_user2 === void 0 ? void 0 : (_session_user_subscription2 = _session_user2.subscription) === null || _session_user_subscription2 === void 0 ? void 0 : _session_user_subscription2.plan_type) === \"lite\" && ((_session3 = session) === null || _session3 === void 0 ? void 0 : (_session_user3 = _session3.user) === null || _session_user3 === void 0 ? void 0 : (_session_user_subscription3 = _session_user3.subscription) === null || _session_user_subscription3 === void 0 ? void 0 : _session_user_subscription3.lite_edits_remaining) === 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"border-white/40 text-white hover:bg-white/20 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300\",\n                            type: \"button\",\n                            disabled: isUploading || ((_session4 = session) === null || _session4 === void 0 ? void 0 : (_session_user4 = _session4.user) === null || _session_user4 === void 0 ? void 0 : (_session_user_subscription4 = _session_user4.subscription) === null || _session_user_subscription4 === void 0 ? void 0 : _session_user_subscription4.plan_type) === \"free\" && ((_session5 = session) === null || _session5 === void 0 ? void 0 : (_session_user5 = _session5.user) === null || _session_user5 === void 0 ? void 0 : (_session_user_subscription5 = _session_user5.subscription) === null || _session_user_subscription5 === void 0 ? void 0 : _session_user_subscription5.free_edits_remaining) === 0 || ((_session6 = session) === null || _session6 === void 0 ? void 0 : (_session_user6 = _session6.user) === null || _session_user6 === void 0 ? void 0 : (_session_user_subscription6 = _session_user6.subscription) === null || _session_user_subscription6 === void 0 ? void 0 : _session_user_subscription6.plan_type) === \"lite\" && ((_session7 = session) === null || _session7 === void 0 ? void 0 : (_session_user7 = _session7.user) === null || _session_user7 === void 0 ? void 0 : (_session_user_subscription7 = _session_user7.subscription) === null || _session_user_subscription7 === void 0 ? void 0 : _session_user_subscription7.lite_edits_remaining) === 0,\n                            children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Processing...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Upload an image\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-sm text-white/70\",\n                    children: getSubscriptionMessage()\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                (((_session8 = session) === null || _session8 === void 0 ? void 0 : (_session_user8 = _session8.user) === null || _session_user8 === void 0 ? void 0 : (_session_user_subscription8 = _session_user8.subscription) === null || _session_user_subscription8 === void 0 ? void 0 : _session_user_subscription8.plan_type) === \"free\" && ((_session9 = session) === null || _session9 === void 0 ? void 0 : (_session_user9 = _session9.user) === null || _session_user9 === void 0 ? void 0 : (_session_user_subscription9 = _session_user9.subscription) === null || _session_user_subscription9 === void 0 ? void 0 : _session_user_subscription9.free_edits_remaining) === 0 || ((_session10 = session) === null || _session10 === void 0 ? void 0 : (_session_user10 = _session10.user) === null || _session_user10 === void 0 ? void 0 : (_session_user_subscription10 = _session_user10.subscription) === null || _session_user_subscription10 === void 0 ? void 0 : _session_user_subscription10.plan_type) === \"lite\" && ((_session11 = session) === null || _session11 === void 0 ? void 0 : (_session_user11 = _session11.user) === null || _session_user11 === void 0 ? void 0 : (_session_user_subscription11 = _session_user11.subscription) === null || _session_user_subscription11 === void 0 ? void 0 : _session_user_subscription11.lite_edits_remaining) === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"default\",\n                        className: \"bg-indigo-600 hover:bg-indigo-700 text-white\",\n                        onClick: ()=>router.push(\"/pricing\"),\n                        children: \"Upgrade Your Plan\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageUpload, \"hS/YmFr7ydpwEqNjxsrbjjk4rgc=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore\n    ];\n});\n_c = ImageUpload;\nvar _c;\n$RefreshReg$(_c, \"ImageUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/studio/image-upload.tsx\n"));

/***/ })

});