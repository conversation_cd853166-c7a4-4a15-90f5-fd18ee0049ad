"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var download_exports = {};
__export(download_exports, {
  download: () => import_chunk_PFE2F67S.download,
  getBinaryName: () => import_chunk_PFE2F67S.getBinaryName,
  getVersion: () => import_chunk_PFE2F67S.getVersion,
  maybeCopyToTmp: () => import_chunk_PFE2F67S.maybeCopyToTmp,
  plusX: () => import_chunk_PFE2F67S.plusX,
  vercelPkgPathRegex: () => import_chunk_PFE2F67S.vercelPkgPathRegex
});
module.exports = __toCommonJS(download_exports);
var import_chunk_PFE2F67S = require("./chunk-PFE2F67S.js");
var import_chunk_FXSJF4XA = require("./chunk-FXSJF4XA.js");
var import_chunk_MX3HXAU2 = require("./chunk-MX3HXAU2.js");
var import_chunk_SXLYQ75W = require("./chunk-SXLYQ75W.js");
var import_chunk_QWMYWBXN = require("./chunk-QWMYWBXN.js");
var import_chunk_EQBIW23N = require("./chunk-EQBIW23N.js");
var import_chunk_MSGI7ABO = require("./chunk-MSGI7ABO.js");
var import_chunk_TEEFYD2G = require("./chunk-TEEFYD2G.js");
var import_chunk_PXQVM7NP = require("./chunk-PXQVM7NP.js");
var import_chunk_X37PZICB = require("./chunk-X37PZICB.js");
var import_chunk_CWGQAQ3T = require("./chunk-CWGQAQ3T.js");
var import_chunk_S3LWA4WZ = require("./chunk-S3LWA4WZ.js");
var import_chunk_OSFPEEC6 = require("./chunk-OSFPEEC6.js");
