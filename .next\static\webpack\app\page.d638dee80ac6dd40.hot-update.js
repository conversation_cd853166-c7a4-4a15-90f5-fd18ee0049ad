"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/studio/image-upload.tsx":
/*!************************************************!*\
  !*** ./src/components/studio/image-upload.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageUpload: function() { return /* binding */ ImageUpload; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ ImageUpload auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ImageUpload() {\n    var _session_user_subscription, _session_user, _session_user_subscription1, _session_user1, _session_user_subscription2, _session_user2, _session_user_subscription3, _session_user3, _session_user_subscription4, _session_user4, _session_user_subscription5, _session_user5, _session_user_subscription6, _session_user6, _session_user_subscription7, _session_user7, _session_user_subscription8, _session_user8, _session_user_subscription9, _session_user9, _session_user_subscription10, _session_user10, _session_user_subscription11, _session_user11;\n    _s();\n    const { setImage, setJustUploadedImage } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore)();\n    const { data: session, update } = useSession();\n    const router = useRouter();\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const handleImageUpload = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        // Check if user is logged in\n        if (!(session === null || session === void 0 ? void 0 : session.user)) {\n            router.push(\"/auth/signin\");\n            return;\n        }\n        // Check subscription status\n        const subscription = session.user.subscription;\n        if (!subscription) {\n            router.push(\"/pricing\");\n            return;\n        }\n        console.log(\"Plan:\", subscription.plan_type);\n        // If user has pro plan, proceed without reducing credits\n        if (subscription.plan_type === \"pro\") {\n            processImage(file);\n            return;\n        }\n        // Check if lite user has any edits remaining\n        if (subscription.plan_type === \"lite\") {\n            if (subscription.lite_edits_remaining === 0) {\n                router.push(\"/pricing?limit=reached&plan=lite\");\n                return;\n            }\n            // Process lite plan credits via API\n            try {\n                setIsUploading(true);\n                const response = await fetch(\"/api/credits/update\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    console.error(\"Error updating lite credits:\", errorData);\n                    return;\n                }\n                const data = await response.json();\n                // Update session with new subscription data\n                await update({\n                    ...session,\n                    user: {\n                        ...session.user,\n                        subscription: data.subscription\n                    }\n                });\n                // Process image\n                processImage(file);\n            } catch (error) {\n                console.error(\"Error processing image:\", error);\n            } finally{\n                setIsUploading(false);\n            }\n            return;\n        }\n        // Free plan handling\n        // Check if free user has any edits remaining - only block if ZERO credits\n        if (subscription.free_edits_remaining === 0) {\n            console.log(\"Redirecting: No free credits remaining\");\n            router.push(\"/pricing?limit=reached&plan=free\");\n            return;\n        }\n        // Special handling for last credit (1 credit remaining)\n        const isLastCredit = subscription.free_edits_remaining === 1;\n        // Update free edits count in database via API\n        try {\n            setIsUploading(true);\n            // Call the API endpoint to update credits\n            console.log(\"Calling API to update credits...\");\n            const response = await fetch(\"/api/credits/update\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.error(\"Error updating credits:\", errorData);\n                return;\n            }\n            const data = await response.json();\n            console.log(\"API response:\", data);\n            // Update session with new subscription data\n            console.log(\"Updating session...\");\n            await update({\n                ...session,\n                user: {\n                    ...session.user,\n                    subscription: data.subscription\n                }\n            });\n            // If this was the last credit, mark that we just used it so we don't get redirected immediately\n            if (isLastCredit) {\n                console.log(\"This was the last credit - setting justUploadedImage flag\");\n                setJustUploadedImage(true);\n            }\n            // Process image\n            processImage(file);\n        } catch (error) {\n            console.error(\"Error processing image:\", error);\n        } finally{\n            setIsUploading(false);\n        }\n    }, [\n        session,\n        router,\n        setImage,\n        setJustUploadedImage,\n        update\n    ]);\n    const processImage = (file)=>{\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            var _e_target;\n            const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            if (result) {\n                setImage(result);\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Calculate message based on subscription status\n    const getSubscriptionMessage = ()=>{\n        if (!(session === null || session === void 0 ? void 0 : session.user)) {\n            return \"Sign in to start editing images\";\n        }\n        // Check if user is admin or influencer - they get unlimited access\n        if (session.user.is_admin) {\n            return \"You have unlimited image edits as an Admin\";\n        }\n        if (session.user.is_influencer) {\n            return \"You have unlimited image edits as an Influencer\";\n        }\n        const subscription = session.user.subscription;\n        if (!subscription) {\n            return \"Subscription not found, please contact support\";\n        }\n        if (subscription.plan_type === \"pro\") {\n            return \"You have unlimited image edits with your Pro plan\";\n        }\n        if (subscription.plan_type === \"lite\") {\n            return \"You have \".concat(subscription.lite_edits_remaining, \" of \").concat(subscription.lite_edits_monthly_limit, \" monthly edits remaining\");\n        }\n        if (subscription.free_edits_remaining === 0) {\n            return \"You've used all your free edits. Upgrade to continue.\";\n        }\n        return \"You have \".concat(subscription.free_edits_remaining, \" free image edits remaining\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 flex items-center justify-center overflow-hidden bg-black text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl md:text-4xl font-bold mb-6\",\n                    children: \"Welcome to Image-Text Studio\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 mx-auto max-w-md mb-8\",\n                    children: \"Upload an image to get started with our advanced text overlay tools.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mx-auto flex justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"file\",\n                            accept: \"image/*\",\n                            onChange: handleImageUpload,\n                            className: \"absolute inset-0 cursor-pointer opacity-0 w-full h-full z-10\",\n                            title: \"Upload an image\",\n                            disabled: isUploading || (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : (_session_user_subscription = _session_user.subscription) === null || _session_user_subscription === void 0 ? void 0 : _session_user_subscription.plan_type) === \"free\" && (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_subscription1 = _session_user1.subscription) === null || _session_user_subscription1 === void 0 ? void 0 : _session_user_subscription1.free_edits_remaining) === 0 || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : (_session_user_subscription2 = _session_user2.subscription) === null || _session_user_subscription2 === void 0 ? void 0 : _session_user_subscription2.plan_type) === \"lite\" && (session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : (_session_user_subscription3 = _session_user3.subscription) === null || _session_user_subscription3 === void 0 ? void 0 : _session_user_subscription3.lite_edits_remaining) === 0\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"border-white/40 text-white hover:bg-white/20 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300\",\n                            type: \"button\",\n                            disabled: isUploading || (session === null || session === void 0 ? void 0 : (_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : (_session_user_subscription4 = _session_user4.subscription) === null || _session_user_subscription4 === void 0 ? void 0 : _session_user_subscription4.plan_type) === \"free\" && (session === null || session === void 0 ? void 0 : (_session_user5 = session.user) === null || _session_user5 === void 0 ? void 0 : (_session_user_subscription5 = _session_user5.subscription) === null || _session_user_subscription5 === void 0 ? void 0 : _session_user_subscription5.free_edits_remaining) === 0 || (session === null || session === void 0 ? void 0 : (_session_user6 = session.user) === null || _session_user6 === void 0 ? void 0 : (_session_user_subscription6 = _session_user6.subscription) === null || _session_user_subscription6 === void 0 ? void 0 : _session_user_subscription6.plan_type) === \"lite\" && (session === null || session === void 0 ? void 0 : (_session_user7 = session.user) === null || _session_user7 === void 0 ? void 0 : (_session_user_subscription7 = _session_user7.subscription) === null || _session_user_subscription7 === void 0 ? void 0 : _session_user_subscription7.lite_edits_remaining) === 0,\n                            children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Processing...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Upload an image\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-sm text-white/70\",\n                    children: getSubscriptionMessage()\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                ((session === null || session === void 0 ? void 0 : (_session_user8 = session.user) === null || _session_user8 === void 0 ? void 0 : (_session_user_subscription8 = _session_user8.subscription) === null || _session_user_subscription8 === void 0 ? void 0 : _session_user_subscription8.plan_type) === \"free\" && (session === null || session === void 0 ? void 0 : (_session_user9 = session.user) === null || _session_user9 === void 0 ? void 0 : (_session_user_subscription9 = _session_user9.subscription) === null || _session_user_subscription9 === void 0 ? void 0 : _session_user_subscription9.free_edits_remaining) === 0 || (session === null || session === void 0 ? void 0 : (_session_user10 = session.user) === null || _session_user10 === void 0 ? void 0 : (_session_user_subscription10 = _session_user10.subscription) === null || _session_user_subscription10 === void 0 ? void 0 : _session_user_subscription10.plan_type) === \"lite\" && (session === null || session === void 0 ? void 0 : (_session_user11 = session.user) === null || _session_user11 === void 0 ? void 0 : (_session_user_subscription11 = _session_user11.subscription) === null || _session_user_subscription11 === void 0 ? void 0 : _session_user_subscription11.lite_edits_remaining) === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"default\",\n                        className: \"bg-indigo-600 hover:bg-indigo-700 text-white\",\n                        onClick: ()=>router.push(\"/pricing\"),\n                        children: \"Upgrade Your Plan\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n            lineNumber: 191,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\coding\\\\HTML\\\\Image-text-studio\\\\src\\\\components\\\\studio\\\\image-upload.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageUpload, \"gjI7Vvjbw9dm2aMKl5xF7IUaEGc=\", true, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_1__.useStore\n    ];\n});\n_c = ImageUpload;\nvar _c;\n$RefreshReg$(_c, \"ImageUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3N0dWRpby9pbWFnZS11cGxvYWQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUUyQztBQUNJO0FBQ1Y7QUFDUTtBQUV0QyxTQUFTSztRQXFNQUMsNEJBQUFBLGVBQXFEQSw2QkFBQUEsZ0JBQ3JEQSw2QkFBQUEsZ0JBQXFEQSw2QkFBQUEsZ0JBUXJEQSw2QkFBQUEsZ0JBQXFEQSw2QkFBQUEsZ0JBQ3JEQSw2QkFBQUEsZ0JBQXFEQSw2QkFBQUEsZ0JBdUIxREEsNkJBQUFBLGdCQUFxREEsNkJBQUFBLGdCQUNyREEsOEJBQUFBLGlCQUFxREEsOEJBQUFBOztJQXRPOUQsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLG9CQUFvQixFQUFFLEdBQUdSLHlEQUFRQTtJQUNuRCxNQUFNLEVBQUVTLE1BQU1ILE9BQU8sRUFBRUksTUFBTSxFQUFFLEdBQUdDO0lBQ2xDLE1BQU1DLFNBQVNDO0lBQ2YsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdYLCtDQUFRQSxDQUFDO0lBRS9DLE1BQU1ZLG9CQUFvQmIsa0RBQVdBLENBQUMsT0FBT2M7WUFDOUJBO1FBQWIsTUFBTUMsUUFBT0Qsc0JBQUFBLE1BQU1FLE1BQU0sQ0FBQ0MsS0FBSyxjQUFsQkgsMENBQUFBLG1CQUFvQixDQUFDLEVBQUU7UUFDcEMsSUFBSSxDQUFDQyxNQUFNO1FBRVgsNkJBQTZCO1FBQzdCLElBQUksRUFBQ1osb0JBQUFBLDhCQUFBQSxRQUFTZSxJQUFJLEdBQUU7WUFDbEJULE9BQU9VLElBQUksQ0FBQztZQUNaO1FBQ0Y7UUFFQSw0QkFBNEI7UUFDNUIsTUFBTUMsZUFBZWpCLFFBQVFlLElBQUksQ0FBQ0UsWUFBWTtRQUM5QyxJQUFJLENBQUNBLGNBQWM7WUFDakJYLE9BQU9VLElBQUksQ0FBQztZQUNaO1FBQ0Y7UUFFQUUsUUFBUUMsR0FBRyxDQUFDLFNBQVNGLGFBQWFHLFNBQVM7UUFFM0MseURBQXlEO1FBQ3pELElBQUlILGFBQWFHLFNBQVMsS0FBSyxPQUFPO1lBQ3BDQyxhQUFhVDtZQUNiO1FBQ0Y7UUFFQSw2Q0FBNkM7UUFDN0MsSUFBSUssYUFBYUcsU0FBUyxLQUFLLFFBQVE7WUFDckMsSUFBSUgsYUFBYUssb0JBQW9CLEtBQUssR0FBRztnQkFDM0NoQixPQUFPVSxJQUFJLENBQUM7Z0JBQ1o7WUFDRjtZQUVBLG9DQUFvQztZQUNwQyxJQUFJO2dCQUNGUCxlQUFlO2dCQUVmLE1BQU1jLFdBQVcsTUFBTUMsTUFBTSx1QkFBdUI7b0JBQ2xEQyxRQUFRO29CQUNSQyxTQUFTO3dCQUNQLGdCQUFnQjtvQkFDbEI7Z0JBQ0Y7Z0JBRUEsSUFBSSxDQUFDSCxTQUFTSSxFQUFFLEVBQUU7b0JBQ2hCLE1BQU1DLFlBQVksTUFBTUwsU0FBU00sSUFBSTtvQkFDckNYLFFBQVFZLEtBQUssQ0FBQyxnQ0FBZ0NGO29CQUM5QztnQkFDRjtnQkFFQSxNQUFNekIsT0FBTyxNQUFNb0IsU0FBU00sSUFBSTtnQkFFaEMsNENBQTRDO2dCQUM1QyxNQUFNekIsT0FBTztvQkFDWCxHQUFHSixPQUFPO29CQUNWZSxNQUFNO3dCQUNKLEdBQUdmLFFBQVFlLElBQUk7d0JBQ2ZFLGNBQWNkLEtBQUtjLFlBQVk7b0JBQ2pDO2dCQUNGO2dCQUVBLGdCQUFnQjtnQkFDaEJJLGFBQWFUO1lBQ2YsRUFBRSxPQUFPa0IsT0FBTztnQkFDZFosUUFBUVksS0FBSyxDQUFDLDJCQUEyQkE7WUFDM0MsU0FBVTtnQkFDUnJCLGVBQWU7WUFDakI7WUFDQTtRQUNGO1FBRUEscUJBQXFCO1FBRXJCLDBFQUEwRTtRQUMxRSxJQUFJUSxhQUFhYyxvQkFBb0IsS0FBSyxHQUFHO1lBQzNDYixRQUFRQyxHQUFHLENBQUM7WUFDWmIsT0FBT1UsSUFBSSxDQUFDO1lBQ1o7UUFDRjtRQUVBLHdEQUF3RDtRQUN4RCxNQUFNZ0IsZUFBZWYsYUFBYWMsb0JBQW9CLEtBQUs7UUFFM0QsOENBQThDO1FBQzlDLElBQUk7WUFDRnRCLGVBQWU7WUFFZiwwQ0FBMEM7WUFDMUNTLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1JLFdBQVcsTUFBTUMsTUFBTSx1QkFBdUI7Z0JBQ2xEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLElBQUksQ0FBQ0gsU0FBU0ksRUFBRSxFQUFFO2dCQUNoQixNQUFNQyxZQUFZLE1BQU1MLFNBQVNNLElBQUk7Z0JBQ3JDWCxRQUFRWSxLQUFLLENBQUMsMkJBQTJCRjtnQkFDekM7WUFDRjtZQUVBLE1BQU16QixPQUFPLE1BQU1vQixTQUFTTSxJQUFJO1lBQ2hDWCxRQUFRQyxHQUFHLENBQUMsaUJBQWlCaEI7WUFFN0IsNENBQTRDO1lBQzVDZSxRQUFRQyxHQUFHLENBQUM7WUFDWixNQUFNZixPQUFPO2dCQUNYLEdBQUdKLE9BQU87Z0JBQ1ZlLE1BQU07b0JBQ0osR0FBR2YsUUFBUWUsSUFBSTtvQkFDZkUsY0FBY2QsS0FBS2MsWUFBWTtnQkFDakM7WUFDRjtZQUVBLGdHQUFnRztZQUNoRyxJQUFJZSxjQUFjO2dCQUNoQmQsUUFBUUMsR0FBRyxDQUFDO2dCQUNaakIscUJBQXFCO1lBQ3ZCO1lBRUEsZ0JBQWdCO1lBQ2hCbUIsYUFBYVQ7UUFDZixFQUFFLE9BQU9rQixPQUFPO1lBQ2RaLFFBQVFZLEtBQUssQ0FBQywyQkFBMkJBO1FBQzNDLFNBQVU7WUFDUnJCLGVBQWU7UUFDakI7SUFDRixHQUFHO1FBQUNUO1FBQVNNO1FBQVFMO1FBQVVDO1FBQXNCRTtLQUFPO0lBRTVELE1BQU1pQixlQUFlLENBQUNUO1FBQ3BCLE1BQU1xQixTQUFTLElBQUlDO1FBQ25CRCxPQUFPRSxNQUFNLEdBQUcsQ0FBQ0M7Z0JBQ0FBO1lBQWYsTUFBTUMsVUFBU0QsWUFBQUEsRUFBRXZCLE1BQU0sY0FBUnVCLGdDQUFBQSxVQUFVQyxNQUFNO1lBQy9CLElBQUlBLFFBQVE7Z0JBQ1ZwQyxTQUFTb0M7WUFDWDtRQUNGO1FBQ0FKLE9BQU9LLGFBQWEsQ0FBQzFCO0lBQ3ZCO0lBRUEsaURBQWlEO0lBQ2pELE1BQU0yQix5QkFBeUI7UUFDN0IsSUFBSSxFQUFDdkMsb0JBQUFBLDhCQUFBQSxRQUFTZSxJQUFJLEdBQUU7WUFDbEIsT0FBTztRQUNUO1FBRUEsbUVBQW1FO1FBQ25FLElBQUlmLFFBQVFlLElBQUksQ0FBQ3lCLFFBQVEsRUFBRTtZQUN6QixPQUFPO1FBQ1Q7UUFFQSxJQUFJeEMsUUFBUWUsSUFBSSxDQUFDMEIsYUFBYSxFQUFFO1lBQzlCLE9BQU87UUFDVDtRQUVBLE1BQU14QixlQUFlakIsUUFBUWUsSUFBSSxDQUFDRSxZQUFZO1FBQzlDLElBQUksQ0FBQ0EsY0FBYztZQUNqQixPQUFPO1FBQ1Q7UUFFQSxJQUFJQSxhQUFhRyxTQUFTLEtBQUssT0FBTztZQUNwQyxPQUFPO1FBQ1Q7UUFFQSxJQUFJSCxhQUFhRyxTQUFTLEtBQUssUUFBUTtZQUNyQyxPQUFPLFlBQW9ESCxPQUF4Q0EsYUFBYUssb0JBQW9CLEVBQUMsUUFBNEMsT0FBdENMLGFBQWF5Qix3QkFBd0IsRUFBQztRQUNuRztRQUVBLElBQUl6QixhQUFhYyxvQkFBb0IsS0FBSyxHQUFHO1lBQzNDLE9BQU87UUFDVDtRQUVBLE9BQU8sWUFBOEMsT0FBbENkLGFBQWFjLG9CQUFvQixFQUFDO0lBQ3ZEO0lBRUEscUJBQ0UsOERBQUNZO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBR0QsV0FBVTs4QkFBc0M7Ozs7Ozs4QkFDcEQsOERBQUNFO29CQUFFRixXQUFVOzhCQUFzQzs7Ozs7OzhCQUluRCw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRzs0QkFDQ0MsTUFBSzs0QkFDTEMsUUFBTzs0QkFDUEMsVUFBVXhDOzRCQUNWa0MsV0FBVTs0QkFDVk8sT0FBTTs0QkFDTkMsVUFBVTVDLGVBQ1AsQ0FBQ1Isb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixxQ0FBQUEsNkJBQUFBLGNBQWVpQixZQUFZLGNBQTNCakIsaURBQUFBLDJCQUE2Qm9CLFNBQVMsTUFBSyxVQUFVcEIsQ0FBQUEsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixzQ0FBQUEsOEJBQUFBLGVBQWVpQixZQUFZLGNBQTNCakIsa0RBQUFBLDRCQUE2QitCLG9CQUFvQixNQUFLLEtBQzNHL0IsQ0FBQUEsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixzQ0FBQUEsOEJBQUFBLGVBQWVpQixZQUFZLGNBQTNCakIsa0RBQUFBLDRCQUE2Qm9CLFNBQVMsTUFBSyxVQUFVcEIsQ0FBQUEsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixzQ0FBQUEsOEJBQUFBLGVBQWVpQixZQUFZLGNBQTNCakIsa0RBQUFBLDRCQUE2QnNCLG9CQUFvQixNQUFLOzs7Ozs7c0NBRWpILDhEQUFDM0IseURBQU1BOzRCQUNMMEQsU0FBUTs0QkFDUkMsTUFBSzs0QkFDTFYsV0FBVTs0QkFDVkksTUFBSzs0QkFDTEksVUFBVTVDLGVBQ1AsQ0FBQ1Isb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixzQ0FBQUEsOEJBQUFBLGVBQWVpQixZQUFZLGNBQTNCakIsa0RBQUFBLDRCQUE2Qm9CLFNBQVMsTUFBSyxVQUFVcEIsQ0FBQUEsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixzQ0FBQUEsOEJBQUFBLGVBQWVpQixZQUFZLGNBQTNCakIsa0RBQUFBLDRCQUE2QitCLG9CQUFvQixNQUFLLEtBQzNHL0IsQ0FBQUEsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixzQ0FBQUEsOEJBQUFBLGVBQWVpQixZQUFZLGNBQTNCakIsa0RBQUFBLDRCQUE2Qm9CLFNBQVMsTUFBSyxVQUFVcEIsQ0FBQUEsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixzQ0FBQUEsOEJBQUFBLGVBQWVpQixZQUFZLGNBQTNCakIsa0RBQUFBLDRCQUE2QnNCLG9CQUFvQixNQUFLO3NDQUU5R2QsNEJBQ0MsOERBQUMrQztnQ0FBS1gsV0FBVTs7a0RBQ2QsOERBQUNZO3dDQUFJWixXQUFVO3dDQUE2Q2EsT0FBTTt3Q0FBNkJDLE1BQUs7d0NBQU9DLFNBQVE7OzBEQUNqSCw4REFBQ0M7Z0RBQU9oQixXQUFVO2dEQUFhaUIsSUFBRztnREFBS0MsSUFBRztnREFBS0MsR0FBRTtnREFBS0MsUUFBTztnREFBZUMsYUFBWTs7Ozs7OzBEQUN4Riw4REFBQ0M7Z0RBQUt0QixXQUFVO2dEQUFhYyxNQUFLO2dEQUFlUyxHQUFFOzs7Ozs7Ozs7Ozs7b0NBQy9DOzs7Ozs7cURBSVI7O2tEQUNFLDhEQUFDdkUsa0ZBQU1BO3dDQUFDZ0QsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs7Ozs7Ozs7OEJBTzNDLDhEQUFDRTtvQkFBRUYsV0FBVTs4QkFDVkw7Ozs7OztnQkFHRCxFQUFDdkMsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixzQ0FBQUEsOEJBQUFBLGVBQWVpQixZQUFZLGNBQTNCakIsa0RBQUFBLDRCQUE2Qm9CLFNBQVMsTUFBSyxVQUFVcEIsQ0FBQUEsb0JBQUFBLCtCQUFBQSxpQkFBQUEsUUFBU2UsSUFBSSxjQUFiZixzQ0FBQUEsOEJBQUFBLGVBQWVpQixZQUFZLGNBQTNCakIsa0RBQUFBLDRCQUE2QitCLG9CQUFvQixNQUFLLEtBQzNHL0IsQ0FBQUEsb0JBQUFBLCtCQUFBQSxrQkFBQUEsUUFBU2UsSUFBSSxjQUFiZix1Q0FBQUEsK0JBQUFBLGdCQUFlaUIsWUFBWSxjQUEzQmpCLG1EQUFBQSw2QkFBNkJvQixTQUFTLE1BQUssVUFBVXBCLENBQUFBLG9CQUFBQSwrQkFBQUEsa0JBQUFBLFFBQVNlLElBQUksY0FBYmYsdUNBQUFBLCtCQUFBQSxnQkFBZWlCLFlBQVksY0FBM0JqQixtREFBQUEsNkJBQTZCc0Isb0JBQW9CLE1BQUssQ0FBQyxtQkFDN0csOERBQUNxQjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ2pELHlEQUFNQTt3QkFDTDBELFNBQVE7d0JBQ1JULFdBQVU7d0JBQ1Z3QixTQUFTLElBQU05RCxPQUFPVSxJQUFJLENBQUM7a0NBQzVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7R0FyUGdCakI7O1FBQzZCTCxxREFBUUE7OztLQURyQ0siLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvc3R1ZGlvL2ltYWdlLXVwbG9hZC50c3g/OTJjNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VTdG9yZSB9IGZyb20gXCJAL3N0b3JlL3VzZVN0b3JlXCJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IFVwbG9hZCB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCJcblxuZXhwb3J0IGZ1bmN0aW9uIEltYWdlVXBsb2FkKCkge1xuICBjb25zdCB7IHNldEltYWdlLCBzZXRKdXN0VXBsb2FkZWRJbWFnZSB9ID0gdXNlU3RvcmUoKVxuICBjb25zdCB7IGRhdGE6IHNlc3Npb24sIHVwZGF0ZSB9ID0gdXNlU2Vzc2lvbigpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IFtpc1VwbG9hZGluZywgc2V0SXNVcGxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VVcGxvYWQgPSB1c2VDYWxsYmFjayhhc3luYyAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGV2ZW50LnRhcmdldC5maWxlcz8uWzBdXG4gICAgaWYgKCFmaWxlKSByZXR1cm5cbiAgICBcbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGxvZ2dlZCBpblxuICAgIGlmICghc2Vzc2lvbj8udXNlcikge1xuICAgICAgcm91dGVyLnB1c2goJy9hdXRoL3NpZ25pbicpXG4gICAgICByZXR1cm5cbiAgICB9XG4gICAgXG4gICAgLy8gQ2hlY2sgc3Vic2NyaXB0aW9uIHN0YXR1c1xuICAgIGNvbnN0IHN1YnNjcmlwdGlvbiA9IHNlc3Npb24udXNlci5zdWJzY3JpcHRpb25cbiAgICBpZiAoIXN1YnNjcmlwdGlvbikge1xuICAgICAgcm91dGVyLnB1c2goJy9wcmljaW5nJylcbiAgICAgIHJldHVyblxuICAgIH1cbiAgICBcbiAgICBjb25zb2xlLmxvZyhcIlBsYW46XCIsIHN1YnNjcmlwdGlvbi5wbGFuX3R5cGUpXG4gICAgXG4gICAgLy8gSWYgdXNlciBoYXMgcHJvIHBsYW4sIHByb2NlZWQgd2l0aG91dCByZWR1Y2luZyBjcmVkaXRzXG4gICAgaWYgKHN1YnNjcmlwdGlvbi5wbGFuX3R5cGUgPT09ICdwcm8nKSB7XG4gICAgICBwcm9jZXNzSW1hZ2UoZmlsZSlcbiAgICAgIHJldHVyblxuICAgIH1cbiAgICBcbiAgICAvLyBDaGVjayBpZiBsaXRlIHVzZXIgaGFzIGFueSBlZGl0cyByZW1haW5pbmdcbiAgICBpZiAoc3Vic2NyaXB0aW9uLnBsYW5fdHlwZSA9PT0gJ2xpdGUnKSB7XG4gICAgICBpZiAoc3Vic2NyaXB0aW9uLmxpdGVfZWRpdHNfcmVtYWluaW5nID09PSAwKSB7XG4gICAgICAgIHJvdXRlci5wdXNoKCcvcHJpY2luZz9saW1pdD1yZWFjaGVkJnBsYW49bGl0ZScpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBQcm9jZXNzIGxpdGUgcGxhbiBjcmVkaXRzIHZpYSBBUElcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldElzVXBsb2FkaW5nKHRydWUpXG4gICAgICAgIFxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NyZWRpdHMvdXBkYXRlJywge1xuICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgICBcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGxpdGUgY3JlZGl0czonLCBlcnJvckRhdGEpXG4gICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgICAgXG4gICAgICAgIC8vIFVwZGF0ZSBzZXNzaW9uIHdpdGggbmV3IHN1YnNjcmlwdGlvbiBkYXRhXG4gICAgICAgIGF3YWl0IHVwZGF0ZSh7XG4gICAgICAgICAgLi4uc2Vzc2lvbixcbiAgICAgICAgICB1c2VyOiB7XG4gICAgICAgICAgICAuLi5zZXNzaW9uLnVzZXIsXG4gICAgICAgICAgICBzdWJzY3JpcHRpb246IGRhdGEuc3Vic2NyaXB0aW9uXG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgICBcbiAgICAgICAgLy8gUHJvY2VzcyBpbWFnZVxuICAgICAgICBwcm9jZXNzSW1hZ2UoZmlsZSlcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHByb2Nlc3NpbmcgaW1hZ2U6JywgZXJyb3IpXG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRJc1VwbG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICAgIHJldHVyblxuICAgIH1cbiAgICBcbiAgICAvLyBGcmVlIHBsYW4gaGFuZGxpbmdcbiAgICBcbiAgICAvLyBDaGVjayBpZiBmcmVlIHVzZXIgaGFzIGFueSBlZGl0cyByZW1haW5pbmcgLSBvbmx5IGJsb2NrIGlmIFpFUk8gY3JlZGl0c1xuICAgIGlmIChzdWJzY3JpcHRpb24uZnJlZV9lZGl0c19yZW1haW5pbmcgPT09IDApIHtcbiAgICAgIGNvbnNvbGUubG9nKFwiUmVkaXJlY3Rpbmc6IE5vIGZyZWUgY3JlZGl0cyByZW1haW5pbmdcIilcbiAgICAgIHJvdXRlci5wdXNoKCcvcHJpY2luZz9saW1pdD1yZWFjaGVkJnBsYW49ZnJlZScpXG4gICAgICByZXR1cm5cbiAgICB9XG4gICAgXG4gICAgLy8gU3BlY2lhbCBoYW5kbGluZyBmb3IgbGFzdCBjcmVkaXQgKDEgY3JlZGl0IHJlbWFpbmluZylcbiAgICBjb25zdCBpc0xhc3RDcmVkaXQgPSBzdWJzY3JpcHRpb24uZnJlZV9lZGl0c19yZW1haW5pbmcgPT09IDFcbiAgICBcbiAgICAvLyBVcGRhdGUgZnJlZSBlZGl0cyBjb3VudCBpbiBkYXRhYmFzZSB2aWEgQVBJXG4gICAgdHJ5IHtcbiAgICAgIHNldElzVXBsb2FkaW5nKHRydWUpXG4gICAgICBcbiAgICAgIC8vIENhbGwgdGhlIEFQSSBlbmRwb2ludCB0byB1cGRhdGUgY3JlZGl0c1xuICAgICAgY29uc29sZS5sb2coXCJDYWxsaW5nIEFQSSB0byB1cGRhdGUgY3JlZGl0cy4uLlwiKVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9jcmVkaXRzL3VwZGF0ZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9XG4gICAgICB9KVxuICAgICAgXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBjcmVkaXRzOicsIGVycm9yRGF0YSlcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgIGNvbnNvbGUubG9nKFwiQVBJIHJlc3BvbnNlOlwiLCBkYXRhKVxuICAgICAgXG4gICAgICAvLyBVcGRhdGUgc2Vzc2lvbiB3aXRoIG5ldyBzdWJzY3JpcHRpb24gZGF0YVxuICAgICAgY29uc29sZS5sb2coXCJVcGRhdGluZyBzZXNzaW9uLi4uXCIpXG4gICAgICBhd2FpdCB1cGRhdGUoe1xuICAgICAgICAuLi5zZXNzaW9uLFxuICAgICAgICB1c2VyOiB7XG4gICAgICAgICAgLi4uc2Vzc2lvbi51c2VyLFxuICAgICAgICAgIHN1YnNjcmlwdGlvbjogZGF0YS5zdWJzY3JpcHRpb25cbiAgICAgICAgfVxuICAgICAgfSlcbiAgICAgIFxuICAgICAgLy8gSWYgdGhpcyB3YXMgdGhlIGxhc3QgY3JlZGl0LCBtYXJrIHRoYXQgd2UganVzdCB1c2VkIGl0IHNvIHdlIGRvbid0IGdldCByZWRpcmVjdGVkIGltbWVkaWF0ZWx5XG4gICAgICBpZiAoaXNMYXN0Q3JlZGl0KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiVGhpcyB3YXMgdGhlIGxhc3QgY3JlZGl0IC0gc2V0dGluZyBqdXN0VXBsb2FkZWRJbWFnZSBmbGFnXCIpXG4gICAgICAgIHNldEp1c3RVcGxvYWRlZEltYWdlKHRydWUpXG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIFByb2Nlc3MgaW1hZ2VcbiAgICAgIHByb2Nlc3NJbWFnZShmaWxlKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwcm9jZXNzaW5nIGltYWdlOicsIGVycm9yKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1VwbG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH0sIFtzZXNzaW9uLCByb3V0ZXIsIHNldEltYWdlLCBzZXRKdXN0VXBsb2FkZWRJbWFnZSwgdXBkYXRlXSlcbiAgXG4gIGNvbnN0IHByb2Nlc3NJbWFnZSA9IChmaWxlOiBGaWxlKSA9PiB7XG4gICAgY29uc3QgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKVxuICAgIHJlYWRlci5vbmxvYWQgPSAoZSkgPT4ge1xuICAgICAgY29uc3QgcmVzdWx0ID0gZS50YXJnZXQ/LnJlc3VsdCBhcyBzdHJpbmdcbiAgICAgIGlmIChyZXN1bHQpIHtcbiAgICAgICAgc2V0SW1hZ2UocmVzdWx0KVxuICAgICAgfVxuICAgIH1cbiAgICByZWFkZXIucmVhZEFzRGF0YVVSTChmaWxlKVxuICB9XG5cbiAgLy8gQ2FsY3VsYXRlIG1lc3NhZ2UgYmFzZWQgb24gc3Vic2NyaXB0aW9uIHN0YXR1c1xuICBjb25zdCBnZXRTdWJzY3JpcHRpb25NZXNzYWdlID0gKCkgPT4ge1xuICAgIGlmICghc2Vzc2lvbj8udXNlcikge1xuICAgICAgcmV0dXJuIFwiU2lnbiBpbiB0byBzdGFydCBlZGl0aW5nIGltYWdlc1wiXG4gICAgfVxuICAgIFxuICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgYWRtaW4gb3IgaW5mbHVlbmNlciAtIHRoZXkgZ2V0IHVubGltaXRlZCBhY2Nlc3NcbiAgICBpZiAoc2Vzc2lvbi51c2VyLmlzX2FkbWluKSB7XG4gICAgICByZXR1cm4gXCJZb3UgaGF2ZSB1bmxpbWl0ZWQgaW1hZ2UgZWRpdHMgYXMgYW4gQWRtaW5cIlxuICAgIH1cbiAgICBcbiAgICBpZiAoc2Vzc2lvbi51c2VyLmlzX2luZmx1ZW5jZXIpIHtcbiAgICAgIHJldHVybiBcIllvdSBoYXZlIHVubGltaXRlZCBpbWFnZSBlZGl0cyBhcyBhbiBJbmZsdWVuY2VyXCJcbiAgICB9XG4gICAgXG4gICAgY29uc3Qgc3Vic2NyaXB0aW9uID0gc2Vzc2lvbi51c2VyLnN1YnNjcmlwdGlvblxuICAgIGlmICghc3Vic2NyaXB0aW9uKSB7XG4gICAgICByZXR1cm4gXCJTdWJzY3JpcHRpb24gbm90IGZvdW5kLCBwbGVhc2UgY29udGFjdCBzdXBwb3J0XCJcbiAgICB9XG4gICAgXG4gICAgaWYgKHN1YnNjcmlwdGlvbi5wbGFuX3R5cGUgPT09ICdwcm8nKSB7XG4gICAgICByZXR1cm4gXCJZb3UgaGF2ZSB1bmxpbWl0ZWQgaW1hZ2UgZWRpdHMgd2l0aCB5b3VyIFBybyBwbGFuXCJcbiAgICB9XG4gICAgXG4gICAgaWYgKHN1YnNjcmlwdGlvbi5wbGFuX3R5cGUgPT09ICdsaXRlJykge1xuICAgICAgcmV0dXJuIGBZb3UgaGF2ZSAke3N1YnNjcmlwdGlvbi5saXRlX2VkaXRzX3JlbWFpbmluZ30gb2YgJHtzdWJzY3JpcHRpb24ubGl0ZV9lZGl0c19tb250aGx5X2xpbWl0fSBtb250aGx5IGVkaXRzIHJlbWFpbmluZ2BcbiAgICB9XG4gICAgXG4gICAgaWYgKHN1YnNjcmlwdGlvbi5mcmVlX2VkaXRzX3JlbWFpbmluZyA9PT0gMCkge1xuICAgICAgcmV0dXJuIFwiWW91J3ZlIHVzZWQgYWxsIHlvdXIgZnJlZSBlZGl0cy4gVXBncmFkZSB0byBjb250aW51ZS5cIlxuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYFlvdSBoYXZlICR7c3Vic2NyaXB0aW9uLmZyZWVfZWRpdHNfcmVtYWluaW5nfSBmcmVlIGltYWdlIGVkaXRzIHJlbWFpbmluZ2BcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG92ZXJmbG93LWhpZGRlbiBiZy1ibGFjayB0ZXh0LXdoaXRlXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBtZDp0ZXh0LTR4bCBmb250LWJvbGQgbWItNlwiPldlbGNvbWUgdG8gSW1hZ2UtVGV4dCBTdHVkaW88L2gxPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWF4LXctbWQgbWItOFwiPlxuICAgICAgICAgIFVwbG9hZCBhbiBpbWFnZSB0byBnZXQgc3RhcnRlZCB3aXRoIG91ciBhZHZhbmNlZCB0ZXh0IG92ZXJsYXkgdG9vbHMuXG4gICAgICAgIDwvcD5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbXgtYXV0byBmbGV4IGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICBhY2NlcHQ9XCJpbWFnZS8qXCJcbiAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbWFnZVVwbG9hZH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgY3Vyc29yLXBvaW50ZXIgb3BhY2l0eS0wIHctZnVsbCBoLWZ1bGwgei0xMFwiXG4gICAgICAgICAgICB0aXRsZT1cIlVwbG9hZCBhbiBpbWFnZVwiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNVcGxvYWRpbmcgfHwgXG4gICAgICAgICAgICAgICgoc2Vzc2lvbj8udXNlcj8uc3Vic2NyaXB0aW9uPy5wbGFuX3R5cGUgPT09ICdmcmVlJyAmJiBzZXNzaW9uPy51c2VyPy5zdWJzY3JpcHRpb24/LmZyZWVfZWRpdHNfcmVtYWluaW5nID09PSAwKSB8fCBcbiAgICAgICAgICAgICAgIChzZXNzaW9uPy51c2VyPy5zdWJzY3JpcHRpb24/LnBsYW5fdHlwZSA9PT0gJ2xpdGUnICYmIHNlc3Npb24/LnVzZXI/LnN1YnNjcmlwdGlvbj8ubGl0ZV9lZGl0c19yZW1haW5pbmcgPT09IDApKX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiIFxuICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci13aGl0ZS80MCB0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzIwIGhvdmVyOnRleHQtd2hpdGUgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzVXBsb2FkaW5nIHx8IFxuICAgICAgICAgICAgICAoKHNlc3Npb24/LnVzZXI/LnN1YnNjcmlwdGlvbj8ucGxhbl90eXBlID09PSAnZnJlZScgJiYgc2Vzc2lvbj8udXNlcj8uc3Vic2NyaXB0aW9uPy5mcmVlX2VkaXRzX3JlbWFpbmluZyA9PT0gMCkgfHwgXG4gICAgICAgICAgICAgICAoc2Vzc2lvbj8udXNlcj8uc3Vic2NyaXB0aW9uPy5wbGFuX3R5cGUgPT09ICdsaXRlJyAmJiBzZXNzaW9uPy51c2VyPy5zdWJzY3JpcHRpb24/LmxpdGVfZWRpdHNfcmVtYWluaW5nID09PSAwKSl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzVXBsb2FkaW5nID8gKFxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIC1tbC0xIG1yLTIgaC00IHctNCB0ZXh0LXdoaXRlXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPGNpcmNsZSBjbGFzc05hbWU9XCJvcGFjaXR5LTI1XCIgY3g9XCIxMlwiIGN5PVwiMTJcIiByPVwiMTBcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjRcIj48L2NpcmNsZT5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgZD1cIk00IDEyYTggOCAwIDAxOC04VjBDNS4zNzMgMCAwIDUuMzczIDAgMTJoNHptMiA1LjI5MUE3Ljk2MiA3Ljk2MiAwIDAxNCAxMkgwYzAgMy4wNDIgMS4xMzUgNS44MjQgMyA3LjkzOGwzLTIuNjQ3elwiPjwvcGF0aD5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICBQcm9jZXNzaW5nLi4uXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPFVwbG9hZCBjbGFzc05hbWU9XCJtci0yIGgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgIFVwbG9hZCBhbiBpbWFnZVxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LXNtIHRleHQtd2hpdGUvNzBcIj5cbiAgICAgICAgICB7Z2V0U3Vic2NyaXB0aW9uTWVzc2FnZSgpfVxuICAgICAgICA8L3A+XG4gICAgICAgIFxuICAgICAgICB7KChzZXNzaW9uPy51c2VyPy5zdWJzY3JpcHRpb24/LnBsYW5fdHlwZSA9PT0gJ2ZyZWUnICYmIHNlc3Npb24/LnVzZXI/LnN1YnNjcmlwdGlvbj8uZnJlZV9lZGl0c19yZW1haW5pbmcgPT09IDApIHx8XG4gICAgICAgICAgKHNlc3Npb24/LnVzZXI/LnN1YnNjcmlwdGlvbj8ucGxhbl90eXBlID09PSAnbGl0ZScgJiYgc2Vzc2lvbj8udXNlcj8uc3Vic2NyaXB0aW9uPy5saXRlX2VkaXRzX3JlbWFpbmluZyA9PT0gMCkpICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwiZGVmYXVsdFwiIFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1pbmRpZ28tNjAwIGhvdmVyOmJnLWluZGlnby03MDAgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvcHJpY2luZycpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBVcGdyYWRlIFlvdXIgUGxhblxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufSAiXSwibmFtZXMiOlsidXNlU3RvcmUiLCJCdXR0b24iLCJVcGxvYWQiLCJ1c2VDYWxsYmFjayIsInVzZVN0YXRlIiwiSW1hZ2VVcGxvYWQiLCJzZXNzaW9uIiwic2V0SW1hZ2UiLCJzZXRKdXN0VXBsb2FkZWRJbWFnZSIsImRhdGEiLCJ1cGRhdGUiLCJ1c2VTZXNzaW9uIiwicm91dGVyIiwidXNlUm91dGVyIiwiaXNVcGxvYWRpbmciLCJzZXRJc1VwbG9hZGluZyIsImhhbmRsZUltYWdlVXBsb2FkIiwiZXZlbnQiLCJmaWxlIiwidGFyZ2V0IiwiZmlsZXMiLCJ1c2VyIiwicHVzaCIsInN1YnNjcmlwdGlvbiIsImNvbnNvbGUiLCJsb2ciLCJwbGFuX3R5cGUiLCJwcm9jZXNzSW1hZ2UiLCJsaXRlX2VkaXRzX3JlbWFpbmluZyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwib2siLCJlcnJvckRhdGEiLCJqc29uIiwiZXJyb3IiLCJmcmVlX2VkaXRzX3JlbWFpbmluZyIsImlzTGFzdENyZWRpdCIsInJlYWRlciIsIkZpbGVSZWFkZXIiLCJvbmxvYWQiLCJlIiwicmVzdWx0IiwicmVhZEFzRGF0YVVSTCIsImdldFN1YnNjcmlwdGlvbk1lc3NhZ2UiLCJpc19hZG1pbiIsImlzX2luZmx1ZW5jZXIiLCJsaXRlX2VkaXRzX21vbnRobHlfbGltaXQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJpbnB1dCIsInR5cGUiLCJhY2NlcHQiLCJvbkNoYW5nZSIsInRpdGxlIiwiZGlzYWJsZWQiLCJ2YXJpYW50Iiwic2l6ZSIsInNwYW4iLCJzdmciLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94IiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInBhdGgiLCJkIiwib25DbGljayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/studio/image-upload.tsx\n"));

/***/ })

});