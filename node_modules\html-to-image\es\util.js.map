{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": "AAEA,MAAM,UAAU,UAAU,CAAC,GAAW,EAAE,OAAsB;IAC5D,0BAA0B;IAC1B,IAAI,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;QAC9B,OAAO,GAAG,CAAA;KACX;IAED,4CAA4C;IAC5C,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;QACtB,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAA;KACtC;IAED,+BAA+B;IAC/B,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;QAC1B,OAAO,GAAG,CAAA;KACX;IAED,MAAM,GAAG,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAA;IACxD,MAAM,IAAI,GAAG,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;IACtC,MAAM,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;IAEhC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IAC1B,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;IAEvB,IAAI,OAAO,EAAE;QACX,IAAI,CAAC,IAAI,GAAG,OAAO,CAAA;KACpB;IAED,CAAC,CAAC,IAAI,GAAG,GAAG,CAAA;IAEZ,OAAO,CAAC,CAAC,IAAI,CAAA;AACf,CAAC;AAED,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,EAAE;IACxB,kDAAkD;IAClD,mFAAmF;IACnF,IAAI,OAAO,GAAG,CAAC,CAAA;IAEf,kDAAkD;IAClD,MAAM,MAAM,GAAG,GAAG,EAAE;IAClB,sCAAsC;IACtC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAElE,OAAO,GAAG,EAAE;QACV,OAAO,IAAI,CAAC,CAAA;QACZ,OAAO,IAAI,MAAM,EAAE,GAAG,OAAO,EAAE,CAAA;IACjC,CAAC,CAAA;AACH,CAAC,CAAC,EAAE,CAAA;AAEJ,MAAM,UAAU,KAAK,CAAI,EAAU;IACjC,OAAO,CAAC,IAAO,EAAE,EAAE,CACjB,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,EAAE;QACzB,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAA;IACrC,CAAC,CAAC,CAAA;AACN,CAAC;AAED,MAAM,UAAU,OAAO,CAAI,SAAc;IACvC,MAAM,GAAG,GAAQ,EAAE,CAAA;IAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAChD,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;KACvB;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,IAAI,UAAU,GAAoB,IAAI,CAAA;AACtC,MAAM,UAAU,kBAAkB,CAAC,UAAmB,EAAE;IACtD,IAAI,UAAU,EAAE;QACd,OAAO,UAAU,CAAA;KAClB;IAED,IAAI,OAAO,CAAC,sBAAsB,EAAE;QAClC,UAAU,GAAG,OAAO,CAAC,sBAAsB,CAAA;QAC3C,OAAO,UAAU,CAAA;KAClB;IAED,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAA;IAEvE,OAAO,UAAU,CAAA;AACnB,CAAC;AAED,SAAS,EAAE,CAAC,IAAiB,EAAE,aAAqB;IAClD,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,MAAM,CAAA;IACpD,MAAM,GAAG,GAAG,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAA;IACtE,OAAO,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACpD,CAAC;AAED,SAAS,YAAY,CAAC,IAAiB;IACrC,MAAM,UAAU,GAAG,EAAE,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAA;IAChD,MAAM,WAAW,GAAG,EAAE,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAA;IAClD,OAAO,IAAI,CAAC,WAAW,GAAG,UAAU,GAAG,WAAW,CAAA;AACpD,CAAC;AAED,SAAS,aAAa,CAAC,IAAiB;IACtC,MAAM,SAAS,GAAG,EAAE,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAA;IAC9C,MAAM,YAAY,GAAG,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAA;IACpD,OAAO,IAAI,CAAC,YAAY,GAAG,SAAS,GAAG,YAAY,CAAA;AACrD,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,UAAuB,EAAE,UAAmB,EAAE;IACzE,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,YAAY,CAAC,UAAU,CAAC,CAAA;IACvD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,UAAU,CAAC,CAAA;IAE1D,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAA;AAC1B,CAAC;AAED,MAAM,UAAU,aAAa;IAC3B,IAAI,KAAK,CAAA;IAET,IAAI,aAAa,CAAA;IACjB,IAAI;QACF,aAAa,GAAG,OAAO,CAAA;KACxB;IAAC,OAAO,CAAC,EAAE;QACV,OAAO;KACR;IAED,MAAM,GAAG,GACP,aAAa,IAAI,aAAa,CAAC,GAAG;QAChC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB;QACpC,CAAC,CAAC,IAAI,CAAA;IACV,IAAI,GAAG,EAAE;QACP,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;QACzB,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvB,KAAK,GAAG,CAAC,CAAA;SACV;KACF;IACD,OAAO,KAAK,IAAI,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAA;AAC9C,CAAC;AAED,4FAA4F;AAC5F,MAAM,oBAAoB,GAAG,KAAK,CAAA;AAElC,MAAM,UAAU,qBAAqB,CAAC,MAAyB;IAC7D,IACE,MAAM,CAAC,KAAK,GAAG,oBAAoB;QACnC,MAAM,CAAC,MAAM,GAAG,oBAAoB,EACpC;QACA,IACE,MAAM,CAAC,KAAK,GAAG,oBAAoB;YACnC,MAAM,CAAC,MAAM,GAAG,oBAAoB,EACpC;YACA,IAAI,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE;gBAChC,MAAM,CAAC,MAAM,IAAI,oBAAoB,GAAG,MAAM,CAAC,KAAK,CAAA;gBACpD,MAAM,CAAC,KAAK,GAAG,oBAAoB,CAAA;aACpC;iBAAM;gBACL,MAAM,CAAC,KAAK,IAAI,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAA;gBACpD,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAA;aACrC;SACF;aAAM,IAAI,MAAM,CAAC,KAAK,GAAG,oBAAoB,EAAE;YAC9C,MAAM,CAAC,MAAM,IAAI,oBAAoB,GAAG,MAAM,CAAC,KAAK,CAAA;YACpD,MAAM,CAAC,KAAK,GAAG,oBAAoB,CAAA;SACpC;aAAM;YACL,MAAM,CAAC,KAAK,IAAI,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAA;YACpD,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAA;SACrC;KACF;AACH,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,MAAyB,EACzB,UAAmB,EAAE;IAErB,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,CAAC,MAAM,CACX,OAAO,EACP,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EACzC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CACtC,CAAA;QACH,CAAC,CAAC,CAAA;KACH;IAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAC9B,MAAM;aACH,SAAS,CACR,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EACvC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAC9C;aACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACjB,CAAA;QACD,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,CAAA;QAC/B,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAA;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;YAC/B,WAAW,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;SAC5C;QAED,OAAO,CACL,IAAI,IAAI,CAAC,CAAC,WAAW,CAAC,EAAE;YACtB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;SAChD,CAAC,CACH,CAAA;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,GAAW;IACrC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAA;QACvB,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;YAChB,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACrB,qBAAqB,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;YAC3C,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QACD,GAAG,CAAC,OAAO,GAAG,MAAM,CAAA;QACpB,GAAG,CAAC,WAAW,GAAG,WAAW,CAAA;QAC7B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAA;QACtB,GAAG,CAAC,GAAG,GAAG,GAAG,CAAA;IACf,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,YAAY,CAAC,GAAe;IAChD,OAAO,OAAO,CAAC,OAAO,EAAE;SACrB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,aAAa,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;SACtD,IAAI,CAAC,kBAAkB,CAAC;SACxB,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,oCAAoC,IAAI,EAAE,CAAC,CAAA;AAC/D,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,IAAiB,EACjB,KAAa,EACb,MAAc;IAEd,MAAM,KAAK,GAAG,4BAA4B,CAAA;IAC1C,MAAM,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IAClD,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;IAEtE,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC,CAAA;IACrC,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,MAAM,EAAE,CAAC,CAAA;IACvC,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,KAAK,IAAI,MAAM,EAAE,CAAC,CAAA;IAErD,aAAa,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;IAC3C,aAAa,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;IAC5C,aAAa,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IACpC,aAAa,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IACpC,aAAa,CAAC,YAAY,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAA;IAE/D,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA;IAC9B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IAC/B,OAAO,YAAY,CAAC,GAAG,CAAC,CAAA;AAC1B,CAAC;AAED,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAGjC,IAA6C,EAC7C,QAAW,EACa,EAAE;IAC1B,IAAI,IAAI,YAAY,QAAQ;QAAE,OAAO,IAAI,CAAA;IAEzC,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;IAEjD,IAAI,aAAa,KAAK,IAAI;QAAE,OAAO,KAAK,CAAA;IAExC,OAAO,CACL,aAAa,CAAC,WAAW,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;QAChD,mBAAmB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC7C,CAAA;AACH,CAAC,CAAA"}